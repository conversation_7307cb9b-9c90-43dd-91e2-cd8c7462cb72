["test_all_event_types.py::test_event_type", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_batch_event_processing", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_busied_out_event_processing", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_database_connectivity", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_invalid_xml_handling", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_lambda_response_format", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_lambda_timeout_handling", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_login_event_processing", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_missing_required_fields", "tests/unit/test_database_service.py::TestDatabaseService::test_get_connection_context_manager", "tests/unit/test_database_service.py::TestDatabaseService::test_init_missing_env_vars", "tests/unit/test_database_service.py::TestDatabaseService::test_init_missing_settings_attributes", "tests/unit/test_database_service.py::TestDatabaseService::test_init_success", "tests/unit/test_database_service.py::TestDimensionManager::test_get_date_key", "tests/unit/test_database_service.py::TestDimensionManager::test_get_time_key", "tests/unit/test_database_service.py::TestFactManager::test_init", "tests/unit/test_database_service.py::TestRedshiftDataAPIConnection::test_cursor_creation", "tests/unit/test_database_service.py::TestRedshiftDataAPIConnection::test_init", "tests/unit/test_database_service.py::TestRedshiftDataAPICursor::test_build_request_params", "tests/unit/test_database_service.py::TestRedshiftDataAPICursor::test_convert_parameter_value_types", "tests/unit/test_database_service.py::TestRedshiftDataAPICursor::test_convert_record_to_dict", "tests/unit/test_database_service.py::TestRedshiftDataAPICursor::test_extract_field_value_types", "tests/unit/test_database_service.py::TestRedshiftDataAPICursor::test_fetchall", "tests/unit/test_database_service.py::TestRedshiftDataAPICursor::test_fetchone_no_results", "tests/unit/test_database_service.py::TestRedshiftDataAPICursor::test_fetchone_with_results", "tests/unit/test_database_service.py::TestRedshiftDataAPICursor::test_prepare_sql_with_none_params", "tests/unit/test_database_service.py::TestRedshiftDataAPICursor::test_prepare_sql_with_params_no_params", "tests/unit/test_database_service.py::TestRedshiftDataAPICursor::test_prepare_sql_with_params_with_params", "tests/unit/test_database_service.py::TestRedshiftDataAPICursor::test_rowcount", "tests/unit/test_event_processor.py::TestEventProcessor::test_process_single_event_failure", "tests/unit/test_event_processor.py::TestEventProcessor::test_process_single_event_success", "tests/unit/test_event_processor.py::TestEventProcessor::test_repository_dimension_keys", "tests/unit/test_event_processor.py::TestEventProcessor::test_resolve_dimension_keys", "tests/unit/test_event_processor.py::TestEventProcessor::test_timestamp_parsing", "tests/unit/test_event_processor.py::TestEventProcessor::test_transform_to_fact_data", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_acd_login_event_processing", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_batch_processing_multiple_events", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_empty_records", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_invalid_xml_handling", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_partial_batch_failure", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_sns_wrapped_message", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_successful_login_event_processing", "tests/unit/test_models.py::TestAgentEvent::test_acd_event_validation", "tests/unit/test_models.py::TestAgentEvent::test_extra_fields_rejected", "tests/unit/test_models.py::TestAgentEvent::test_invalid_agent_uri", "tests/unit/test_models.py::TestAgentEvent::test_missing_required_fields", "tests/unit/test_models.py::TestAgentEvent::test_timestamp_parsing", "tests/unit/test_models.py::TestAgentEvent::test_valid_acd_login_event", "tests/unit/test_models.py::TestAgentEvent::test_valid_login_event", "tests/unit/test_models.py::TestDimensionKeys::test_invalid_date_key", "tests/unit/test_models.py::TestDimensionKeys::test_invalid_time_key", "tests/unit/test_models.py::TestDimensionKeys::test_valid_dimension_keys", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_extract_xml_from_raw_sqs_message", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_extract_xml_from_sqs_json_message", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_acd_login_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_agent_available_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_agent_busied_out_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_invalid_xml", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_login_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_logout_event_with_voice_qos", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_unknown_event_type", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_xml_missing_required_fields"]