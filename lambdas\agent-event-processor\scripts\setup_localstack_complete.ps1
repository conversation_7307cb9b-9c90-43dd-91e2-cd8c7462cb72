#!/usr/bin/env pwsh
# Complete LocalStack setup script for Agent Event Processor with Redshift Data API

param(
    [string]$Profile = $env:AWS_PROFILE,
    [string]$LocalStackEndpoint = "http://localhost:4566",
    [string]$Region = "us-east-1",
    [switch]$SkipBuild = $false,
    [switch]$Help
)

if ($Help) {
    Write-Host "Usage: .\setup_localstack_complete.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Profile <profile>     AWS profile to use (default: `$env:AWS_PROFILE)"
    Write-Host "  -LocalStackEndpoint    LocalStack endpoint (default: http://localhost:4566)"
    Write-Host "  -Region <region>       AWS region (default: us-east-1)"
    Write-Host "  -SkipBuild            Skip building Lambda package"
    Write-Host "  -Help                 Show this help message"
    Write-Host ""
    Write-Host "Example:"
    Write-Host "  `$env:AWS_PROFILE='admin-memo'; .\setup_localstack_complete.ps1"
    exit 0
}

$ErrorActionPreference = "Stop"

Write-Host "Setting up complete LocalStack environment for Agent Event Processor..." -ForegroundColor Green
Write-Host "Profile: $Profile" -ForegroundColor Cyan
Write-Host "Endpoint: $LocalStackEndpoint" -ForegroundColor Cyan

# Configuration
$QUEUE_NAME = "agent-events-queue"
$DLQ_NAME = "agent-events-dlq"
$LAMBDA_NAME = "agent-event-processor"
$REDSHIFT_CLUSTER = "localstack-redshift"
$REDSHIFT_DATABASE = "dev"
$REDSHIFT_USER = "solacom"

# Set AWS environment variables
if ($Profile) {
    $env:AWS_PROFILE = $Profile
    Write-Host "Using AWS Profile: $Profile" -ForegroundColor Yellow
} else {
    # Use test credentials for LocalStack
    $env:AWS_ACCESS_KEY_ID = "test"
    $env:AWS_SECRET_ACCESS_KEY = "test"
    Write-Host "Using test credentials for LocalStack" -ForegroundColor Yellow
}

$env:AWS_DEFAULT_REGION = $Region

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition
$ProjectDir = Split-Path -Parent $ScriptDir

# Function to wait for LocalStack
function Wait-ForLocalStack {
    Write-Host "Waiting for LocalStack to be ready..." -ForegroundColor Yellow
    $maxAttempts = 30
    $attempt = 0
    
    do {
        try {
            $response = Invoke-WebRequest -Uri "$LocalStackEndpoint/_localstack/health" -UseBasicParsing -TimeoutSec 5
            $health = $response.Content | ConvertFrom-Json
            
            if ($health.services.sqs -eq "available" -and $health.services.lambda -eq "available") {
                Write-Host "LocalStack is ready!" -ForegroundColor Green
                return $true
            }
        }
        catch {
            # Ignore errors and continue waiting
        }
        
        $attempt++
        if ($attempt -ge $maxAttempts) {
            Write-Host "LocalStack failed to start within timeout" -ForegroundColor Red
            return $false
        }
        
        Write-Host "Waiting for LocalStack... (attempt $attempt/$maxAttempts)" -ForegroundColor Gray
        Start-Sleep -Seconds 2
    } while ($true)
}

# Function to create SQS resources
function Setup-SQSResources {
    Write-Host "Setting up SQS resources..." -ForegroundColor Yellow
    
    # Create Dead Letter Queue
    Write-Host "Creating SQS Dead Letter Queue..." -ForegroundColor Cyan
    aws --endpoint-url=$LocalStackEndpoint sqs create-queue --queue-name $DLQ_NAME --region $Region
    
    $dlqUrl = aws --endpoint-url=$LocalStackEndpoint sqs get-queue-url --queue-name $DLQ_NAME --region $Region --query 'QueueUrl' --output text
    $dlqArn = aws --endpoint-url=$LocalStackEndpoint sqs get-queue-attributes --queue-url $dlqUrl --attribute-names QueueArn --region $Region --query 'Attributes.QueueArn' --output text
    
    Write-Host "Created DLQ: $dlqArn" -ForegroundColor Green
    
    # Create main queue with DLQ configuration
    Write-Host "Creating main SQS Queue..." -ForegroundColor Cyan
    $redrivePolicy = @{
        deadLetterTargetArn = $dlqArn
        maxReceiveCount = 3
    } | ConvertTo-Json -Compress
    
    aws --endpoint-url=$LocalStackEndpoint sqs create-queue --queue-name $QUEUE_NAME --attributes "RedrivePolicy=$redrivePolicy" --region $Region
    
    $queueUrl = aws --endpoint-url=$LocalStackEndpoint sqs get-queue-url --queue-name $QUEUE_NAME --region $Region --query 'QueueUrl' --output text
    $queueArn = aws --endpoint-url=$LocalStackEndpoint sqs get-queue-attributes --queue-url $queueUrl --attribute-names QueueArn --region $Region --query 'Attributes.QueueArn' --output text
    
    Write-Host "Created Queue: $queueUrl" -ForegroundColor Green
    
    return @{
        QueueUrl = $queueUrl
        QueueArn = $queueArn
        DlqUrl = $dlqUrl
        DlqArn = $dlqArn
    }
}

# Function to create Redshift cluster
function Setup-RedshiftCluster {
    Write-Host "Setting up Redshift cluster..." -ForegroundColor Yellow
    
    try {
        Write-Host "Creating Redshift cluster..." -ForegroundColor Cyan
        aws --endpoint-url=$LocalStackEndpoint redshift create-cluster `
            --cluster-identifier $REDSHIFT_CLUSTER `
            --db-name $REDSHIFT_DATABASE `
            --master-username $REDSHIFT_USER `
            --master-user-password "testpassword" `
            --node-type dc2.large `
            --region $Region
        
        Write-Host "Redshift cluster created: $REDSHIFT_CLUSTER" -ForegroundColor Green
    }
    catch {
        Write-Host "Redshift cluster may already exist or LocalStack doesn't support it fully" -ForegroundColor Yellow
        Write-Host "Continuing with setup..." -ForegroundColor Yellow
    }
}

# Function to create IAM role
function Setup-IAMRole {
    Write-Host "Setting up IAM role..." -ForegroundColor Yellow
    
    $assumeRolePolicy = @{
        Version = "2012-10-17"
        Statement = @(
            @{
                Effect = "Allow"
                Principal = @{
                    Service = "lambda.amazonaws.com"
                }
                Action = "sts:AssumeRole"
            }
        )
    } | ConvertTo-Json -Depth 10 -Compress
    
    try {
        aws --endpoint-url=$LocalStackEndpoint iam create-role `
            --role-name lambda-execution-role `
            --assume-role-policy-document $assumeRolePolicy `
            --region $Region
        
        Write-Host "IAM role created: lambda-execution-role" -ForegroundColor Green
    }
    catch {
        Write-Host "IAM role may already exist" -ForegroundColor Yellow
    }
}

# Function to build Lambda package
function Build-LambdaPackage {
    if ($SkipBuild) {
        Write-Host "Skipping Lambda build..." -ForegroundColor Yellow
        return
    }
    
    Write-Host "Building Lambda package..." -ForegroundColor Yellow
    
    $buildScript = Join-Path $ScriptDir "build_lambda.ps1"
    if (Test-Path $buildScript) {
        & $buildScript -Clean
        if ($LASTEXITCODE -ne 0) {
            throw "Lambda build failed"
        }
    } else {
        throw "Build script not found: $buildScript"
    }
}

# Function to deploy Lambda
function Deploy-Lambda {
    param($QueueArn)
    
    Write-Host "Deploying Lambda function..." -ForegroundColor Yellow
    
    $zipPath = Join-Path $ProjectDir "agent-event-processor.zip"
    if (-not (Test-Path $zipPath)) {
        throw "Lambda package not found: $zipPath"
    }
    
    # Create CloudWatch Log Group
    try {
        aws --endpoint-url=$LocalStackEndpoint logs create-log-group `
            --log-group-name "/aws/lambda/$LAMBDA_NAME" `
            --region $Region
    }
    catch {
        Write-Host "Log group may already exist" -ForegroundColor Yellow
    }
    
    # Deploy Lambda function
    try {
        aws --endpoint-url=$LocalStackEndpoint lambda create-function `
            --function-name $LAMBDA_NAME `
            --runtime python3.12 `
            --role "arn:aws:iam::123456789012:role/lambda-execution-role" `
            --handler "agent_event_processor.lambda_function.lambda_handler" `
            --zip-file "fileb://$zipPath" `
            --timeout 300 `
            --memory-size 512 `
            --environment "Variables={AWS_ENDPOINT_URL=$LocalStackEndpoint,DATABASE_CLUSTER_IDENTIFIER=$REDSHIFT_CLUSTER,DATABASE_REDSHIFT_DATABASE=$REDSHIFT_DATABASE,DATABASE_REDSHIFT_USER=$REDSHIFT_USER,CLIENT_NAME=TestClient,CLIENT_TIMEZONE=America/New_York}" `
            --region $Region
        
        Write-Host "Lambda function deployed: $LAMBDA_NAME" -ForegroundColor Green
    }
    catch {
        Write-Host "Updating existing Lambda function..." -ForegroundColor Yellow
        aws --endpoint-url=$LocalStackEndpoint lambda update-function-code `
            --function-name $LAMBDA_NAME `
            --zip-file "fileb://$zipPath" `
            --region $Region
    }
    
    # Create SQS trigger
    try {
        aws --endpoint-url=$LocalStackEndpoint lambda create-event-source-mapping `
            --event-source-arn $QueueArn `
            --function-name $LAMBDA_NAME `
            --batch-size 10 `
            --maximum-batching-window-in-seconds 5 `
            --region $Region
        
        Write-Host "SQS trigger created" -ForegroundColor Green
    }
    catch {
        Write-Host "SQS trigger may already exist" -ForegroundColor Yellow
    }
}

# Main execution
try {
    # Wait for LocalStack
    if (-not (Wait-ForLocalStack)) {
        throw "LocalStack is not ready"
    }
    
    # Setup resources
    $sqsResources = Setup-SQSResources
    Setup-RedshiftCluster
    Setup-IAMRole
    Build-LambdaPackage
    Deploy-Lambda -QueueArn $sqsResources.QueueArn
    
    # Summary
    Write-Host "`n" + "="*70 -ForegroundColor Green
    Write-Host "LocalStack setup completed successfully!" -ForegroundColor Green
    Write-Host "="*70 -ForegroundColor Green
    Write-Host "Resources created:" -ForegroundColor Cyan
    Write-Host "  - SQS Queue: $($sqsResources.QueueUrl)" -ForegroundColor White
    Write-Host "  - SQS DLQ: $($sqsResources.DlqUrl)" -ForegroundColor White
    Write-Host "  - Redshift Cluster: $REDSHIFT_CLUSTER" -ForegroundColor White
    Write-Host "  - Lambda Function: $LAMBDA_NAME" -ForegroundColor White
    Write-Host "  - Log Group: /aws/lambda/$LAMBDA_NAME" -ForegroundColor White
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "  1. Send test events: .\scripts\send_test_events.ps1" -ForegroundColor White
    Write-Host "  2. Check logs: docker logs agent-event-processor-lambda" -ForegroundColor White
    Write-Host "  3. Run test script: .\test_busied_out.py" -ForegroundColor White
}
catch {
    Write-Host "Setup failed: $_" -ForegroundColor Red
    exit 1
}
