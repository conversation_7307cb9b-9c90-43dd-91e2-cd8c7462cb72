{".class": "MypyFile", "_fullname": "tests.unit.test_database_service", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DatabaseService": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.services.database_service.DatabaseService", "kind": "Gdef"}, "DatabaseSettings": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.config.settings.DatabaseSettings", "kind": "Gdef"}, "DimensionManager": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.services.database_service.DimensionManager", "kind": "Gdef"}, "FactManager": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.services.database_service.FactManager", "kind": "Gdef"}, "Mock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.Mock", "kind": "Gdef"}, "RedshiftDataAPIConnection": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.services.database_service.RedshiftDataAPIConnection", "kind": "Gdef"}, "RedshiftDataAPICursor": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.services.database_service.RedshiftDataAPICursor", "kind": "Gdef"}, "TestDatabaseService": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.unit.test_database_service.TestDatabaseService", "name": "TestDatabaseService", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tests.unit.test_database_service.TestDatabaseService", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.unit.test_database_service", "mro": ["tests.unit.test_database_service.TestDatabaseService", "builtins.object"], "names": {".class": "SymbolTable", "mock_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_database_service.TestDatabaseService.mock_settings", "name": "mock_settings", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_database_service.TestDatabaseService.mock_settings", "name": "mock_settings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tests.unit.test_database_service.TestDatabaseService"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mock_settings of TestDatabaseService", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "test_get_connection_context_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_settings"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestDatabaseService.test_get_connection_context_manager", "name": "test_get_connection_context_manager", "type": null}}, "test_init_missing_settings_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestDatabaseService.test_init_missing_settings_attributes", "name": "test_init_missing_settings_attributes", "type": null}}, "test_init_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_settings"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestDatabaseService.test_init_success", "name": "test_init_success", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.unit.test_database_service.TestDatabaseService.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.unit.test_database_service.TestDatabaseService", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestDimensionManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.unit.test_database_service.TestDimensionManager", "name": "TestDimensionManager", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tests.unit.test_database_service.TestDimensionManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.unit.test_database_service", "mro": ["tests.unit.test_database_service.TestDimensionManager", "builtins.object"], "names": {".class": "SymbolTable", "dimension_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_db_service"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_database_service.TestDimensionManager.dimension_manager", "name": "dimension_manager", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_database_service.TestDimensionManager.dimension_manager", "name": "dimension_manager", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mock_db_service"], "arg_types": ["tests.unit.test_database_service.TestDimensionManager", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dimension_manager of TestDimensionManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "mock_db_service": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_database_service.TestDimensionManager.mock_db_service", "name": "mock_db_service", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_database_service.TestDimensionManager.mock_db_service", "name": "mock_db_service", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tests.unit.test_database_service.TestDimensionManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mock_db_service of TestDimensionManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "test_get_date_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dimension_manager"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestDimensionManager.test_get_date_key", "name": "test_get_date_key", "type": null}}, "test_get_time_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dimension_manager"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestDimensionManager.test_get_time_key", "name": "test_get_time_key", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.unit.test_database_service.TestDimensionManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.unit.test_database_service.TestDimensionManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestFactManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.unit.test_database_service.TestFactManager", "name": "TestFactManager", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tests.unit.test_database_service.TestFactManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.unit.test_database_service", "mro": ["tests.unit.test_database_service.TestFactManager", "builtins.object"], "names": {".class": "SymbolTable", "fact_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_db_service"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_database_service.TestFactManager.fact_manager", "name": "fact_manager", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_database_service.TestFactManager.fact_manager", "name": "fact_manager", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mock_db_service"], "arg_types": ["tests.unit.test_database_service.TestFactManager", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fact_manager of TestFactManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "mock_db_service": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_database_service.TestFactManager.mock_db_service", "name": "mock_db_service", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_database_service.TestFactManager.mock_db_service", "name": "mock_db_service", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tests.unit.test_database_service.TestFactManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mock_db_service of TestFactManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "test_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fact_manager", "mock_db_service"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestFactManager.test_init", "name": "test_init", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.unit.test_database_service.TestFactManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.unit.test_database_service.TestFactManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestRedshiftDataAPIConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.unit.test_database_service.TestRedshiftDataAPIConnection", "name": "TestRedshiftDataAPIConnection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPIConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.unit.test_database_service", "mro": ["tests.unit.test_database_service.TestRedshiftDataAPIConnection", "builtins.object"], "names": {".class": "SymbolTable", "test_cursor_creation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPIConnection.test_cursor_creation", "name": "test_cursor_creation", "type": null}}, "test_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPIConnection.test_init", "name": "test_init", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.unit.test_database_service.TestRedshiftDataAPIConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.unit.test_database_service.TestRedshiftDataAPIConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestRedshiftDataAPICursor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor", "name": "TestRedshiftDataAPICursor", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.unit.test_database_service", "mro": ["tests.unit.test_database_service.TestRedshiftDataAPICursor", "builtins.object"], "names": {".class": "SymbolTable", "cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_connection"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.cursor", "name": "cursor", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.cursor", "name": "cursor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mock_connection"], "arg_types": ["tests.unit.test_database_service.TestRedshiftDataAPICursor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cursor of TestRedshiftDataAPICursor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "mock_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.mock_connection", "name": "mock_connection", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.mock_connection", "name": "mock_connection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tests.unit.test_database_service.TestRedshiftDataAPICursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mock_connection of TestRedshiftDataAPICursor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "test_build_request_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.test_build_request_params", "name": "test_build_request_params", "type": null}}, "test_convert_parameter_value_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.test_convert_parameter_value_types", "name": "test_convert_parameter_value_types", "type": null}}, "test_convert_record_to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.test_convert_record_to_dict", "name": "test_convert_record_to_dict", "type": null}}, "test_extract_field_value_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.test_extract_field_value_types", "name": "test_extract_field_value_types", "type": null}}, "test_fetchall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.test_fetchall", "name": "test_fetchall", "type": null}}, "test_fetchone_no_results": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.test_fetchone_no_results", "name": "test_fetchone_no_results", "type": null}}, "test_fetchone_with_results": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.test_fetchone_with_results", "name": "test_fetchone_with_results", "type": null}}, "test_prepare_sql_with_none_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.test_prepare_sql_with_none_params", "name": "test_prepare_sql_with_none_params", "type": null}}, "test_prepare_sql_with_params_no_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.test_prepare_sql_with_params_no_params", "name": "test_prepare_sql_with_params_no_params", "type": null}}, "test_prepare_sql_with_params_with_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.test_prepare_sql_with_params_with_params", "name": "test_prepare_sql_with_params_with_params", "type": null}}, "test_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.test_rowcount", "name": "test_rowcount", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.unit.test_database_service.TestRedshiftDataAPICursor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.unit.test_database_service.TestRedshiftDataAPICursor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_database_service.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_database_service.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_database_service.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_database_service.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_database_service.__package__", "name": "__package__", "type": "builtins.str"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}}, "path": "tests\\unit\\test_database_service.py"}