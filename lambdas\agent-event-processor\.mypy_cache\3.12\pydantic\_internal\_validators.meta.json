{"data_mtime": 1757356838, "dep_lines": [23, 8, 19, 20, 21, 24, 6, 8, 9, 10, 11, 12, 13, 14, 16, 18, 19, 21, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 5, 5, 20, 10, 10, 5, 5, 5, 5, 5, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._import_utils", "collections.abc", "pydantic_core.core_schema", "pydantic_core._pydantic_core", "typing_inspection.typing_objects", "pydantic.errors", "__future__", "collections", "math", "re", "typing", "decimal", "fractions", "ipaddress", "zoneinfo", "typing_extensions", "pydantic_core", "typing_inspection", "builtins", "pyexpat.errors", "pyexpat.model", "_decimal", "_typeshed", "abc", "datetime", "numbers"], "hash": "4c9711f5bc5c3d78f39ed37a42089bf1cc8f4641594311d6df64a828611ca749", "id": "pydantic._internal._validators", "ignore_all": true, "interface_hash": "531423febd3b7d8b03c8e2e4da237955b7928f1fc4269cdf0605f5875776a0a3", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_validators.py", "plugin_data": null, "size": 20610, "suppressed": [], "version_id": "1.8.0"}