{"data_mtime": 1757364036, "dep_lines": [3, 4, 1, 2, 5, 6, 7, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "email.message", "sys", "_typeshed", "types", "typing", "typing_extensions", "builtins", "_collections_abc", "abc", "email"], "hash": "2bf95b74d75e12c39f79386c89d2af27d03411002887e7544703dc0baceb1e01", "id": "urllib.response", "ignore_all": true, "interface_hash": "85dc06a68f1287673f6d46f0e1245733b0c52e1153721f857e8245dc1b919120", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\urllib\\response.pyi", "plugin_data": null, "size": 2302, "suppressed": [], "version_id": "1.8.0"}