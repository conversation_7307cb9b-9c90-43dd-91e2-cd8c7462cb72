{"data_mtime": 1757356840, "dep_lines": [10, 11, 7, 8, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 20, 20, 30, 30], "dependencies": ["boto3.resources.model", "botocore.client", "logging", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "abc", "botocore"], "hash": "bec89966a24c61929785cbba20cf23efe8e4ffe0ce6d1c20a63e2408cdca8ae7", "id": "boto3.resources.base", "ignore_all": true, "interface_hash": "9b995f3027152ada3d53cf44a0fc6f3a1499e78184c31e19bab6ed923c3bffe4", "mtime": 1757092242, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\boto3-stubs\\resources\\base.pyi", "plugin_data": null, "size": 1090, "suppressed": [], "version_id": "1.8.0"}