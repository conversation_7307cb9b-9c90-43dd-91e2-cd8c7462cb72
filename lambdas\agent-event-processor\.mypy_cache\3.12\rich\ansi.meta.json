{"data_mtime": 1757356834, "dep_lines": [6, 7, 8, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.color", "rich.style", "rich.text", "re", "sys", "contextlib", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "_collections_abc", "_typeshed", "abc", "enum", "functools", "rich.color_triplet", "rich.jupyter", "typing_extensions"], "hash": "02fb352c76d275cc8ebc339da442d952850b7018987b063be9e341a7ab85061b", "id": "rich.ansi", "ignore_all": true, "interface_hash": "77f8a44d6d80e2e7a645864251a4f5e91b29ff880da069d56874d772e3321197", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\ansi.py", "plugin_data": null, "size": 6921, "suppressed": [], "version_id": "1.8.0"}