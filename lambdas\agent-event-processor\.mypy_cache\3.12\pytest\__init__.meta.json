{"data_mtime": **********, "dep_lines": [18, 5, 6, 7, 8, 9, 20, 21, 22, 26, 27, 29, 30, 31, 36, 37, 40, 45, 50, 55, 57, 60, 62, 63, 65, 66, 67, 3, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest._code", "_pytest.assertion", "_pytest.cacheprovider", "_pytest.capture", "_pytest.config", "_pytest.debugging", "_pytest.doctest", "_pytest.fixtures", "_pytest.freeze_support", "_pytest.legacypath", "_pytest.logging", "_pytest.main", "_pytest.mark", "_pytest.monkeypatch", "_pytest.nodes", "_pytest.outcomes", "_pytest.pytester", "_pytest.python", "_pytest.python_api", "_pytest.recwarn", "_pytest.reports", "_pytest.runner", "_pytest.stash", "_pytest.terminal", "_pytest.tmpdir", "_pytest.warning_types", "_pytest", "builtins", "abc", "typing"], "hash": "ff25ba88c3f21377d4fcb8973c2c082eefab59c318a2e552996438f52e2f9a47", "id": "pytest", "ignore_all": true, "interface_hash": "dfa3c788e9b20b08bc7b5675b99d06bd91a6a95be2f5ae8e359ee41abe64ca9d", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pytest\\__init__.py", "plugin_data": null, "size": 5237, "suppressed": [], "version_id": "1.8.0"}