{"data_mtime": 1757356835, "dep_lines": [21, 12, 26, 27, 1, 3, 4, 5, 6, 7, 8, 9, 10, 24, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 5, 10, 10, 10, 10, 10, 10, 5, 5, 25, 5, 20, 20, 30, 30], "dependencies": ["urllib3.util.util", "urllib3.exceptions", "urllib3.connectionpool", "urllib3.response", "__future__", "email", "logging", "random", "re", "time", "typing", "itertools", "types", "typing_extensions", "builtins", "pyexpat.errors", "pyexpat.model", "abc", "io"], "hash": "6e3fb6614a9b9712e5bfc4c78397f1c30f83339e1709b8e0657210ef55e2a026", "id": "urllib3.util.retry", "ignore_all": true, "interface_hash": "bfa266ab41d95d55bf07a7b16b3b74950e27fc8c06be5b65146b3c0e62f8c7da", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\util\\retry.py", "plugin_data": null, "size": 18459, "suppressed": [], "version_id": "1.8.0"}