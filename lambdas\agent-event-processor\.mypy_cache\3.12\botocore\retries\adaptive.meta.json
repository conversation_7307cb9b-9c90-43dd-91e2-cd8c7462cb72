{"data_mtime": 1757356839, "dep_lines": [11, 12, 13, 10, 11, 7, 8, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 20, 5, 5, 5, 20, 20, 30], "dependencies": ["botocore.retries.bucket", "botocore.retries.standard", "botocore.retries.throttling", "botocore.client", "botocore.retries", "logging", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "abc"], "hash": "5b3c12046fdf0b4a68596597555223836891f665cf3a3d2819581e5d8637ad8b", "id": "botocore.retries.adaptive", "ignore_all": true, "interface_hash": "6d138eb193e90c62febb8d97504fcc0145de9946682f001a7154a0d7b51718e1", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\retries\\adaptive.pyi", "plugin_data": null, "size": 1009, "suppressed": [], "version_id": "1.8.0"}