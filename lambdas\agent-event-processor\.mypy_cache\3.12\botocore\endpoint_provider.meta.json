{"data_mtime": **********, "dep_lines": [8, 18, 20, 21, 7, 9, 10, 11, 12, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["collections.abc", "botocore.compat", "botocore.exceptions", "botocore.utils", "logging", "enum", "re", "string", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "_collections_abc", "abc"], "hash": "f6512f45eca25c4c86dc7d24600bdcb53bfd55b2fed5846f0d56d38c2846756a", "id": "botocore.endpoint_provider", "ignore_all": true, "interface_hash": "0149c1be4b8f93f8955a4ba44d6d2868ab04eb1048908f783da4ef8d70e26d96", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\endpoint_provider.pyi", "plugin_data": null, "size": 6233, "suppressed": [], "version_id": "1.8.0"}