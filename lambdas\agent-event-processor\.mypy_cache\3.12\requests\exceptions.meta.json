{"data_mtime": 1757364037, "dep_lines": [3, 5, 6, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30], "dependencies": ["urllib3.exceptions", "requests.models", "requests.sessions", "typing", "builtins", "abc", "urllib3"], "hash": "2f348ff517b768f902d112e5d1bbe86e6a0614d36808b79b9f6f110a9b6e959d", "id": "requests.exceptions", "ignore_all": true, "interface_hash": "d2a99756930bae44bc602e1a0a4fb640fb08eadfdc103bce3ceab868523a5ccf", "mtime": 1757363991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\requests-stubs\\exceptions.pyi", "plugin_data": null, "size": 1624, "suppressed": [], "version_id": "1.8.0"}