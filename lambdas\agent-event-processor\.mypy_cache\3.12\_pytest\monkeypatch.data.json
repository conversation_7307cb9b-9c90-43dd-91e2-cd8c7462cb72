{".class": "MypyFile", "_fullname": "_pytest.monkeypatch", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "K": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.K", "name": "K", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MonkeyPatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.monkeypatch.MonkeyPatch", "name": "MonkeyPatch", "type_vars": []}, "deletable_attributes": [], "flags": ["is_final"], "fullname": "_pytest.monkeypatch.MonkeyPatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.monkeypatch", "mro": ["_pytest.monkeypatch.MonkeyPatch", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.monkeypatch.MonkeyPatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_cwd": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.monkeypatch.MonkeyPatch._cwd", "name": "_cwd", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "_savesyspath": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.monkeypatch.MonkeyPatch._savesyspath", "name": "_savesyspath", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "NoneType"}]}}}, "_setattr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.monkeypatch.MonkeyPatch._setattr", "name": "_setattr", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.object", "builtins.str", "builtins.object"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "_setitem": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.monkeypatch.MonkeyPatch._setitem", "name": "_setitem", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "builtins.object", "builtins.object"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "chdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.monkeypatch.MonkeyPatch.chdir", "name": "chdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chdir of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "_pytest.monkeypatch.MonkeyPatch.context", "name": "context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "_pytest.monkeypatch.MonkeyPatch"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "context of MonkeyPatch", "ret_type": {".class": "Instance", "args": ["_pytest.monkeypatch.MonkeyPatch", {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.monkeypatch.MonkeyPatch.context", "name": "context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "_pytest.monkeypatch.MonkeyPatch"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "context of MonkeyPatch", "ret_type": {".class": "Instance", "args": ["_pytest.monkeypatch.MonkeyPatch"], "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "delattr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "target", "name", "raising"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.monkeypatch.MonkeyPatch.delattr", "name": "delattr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "target", "name", "raising"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", {".class": "UnionType", "items": ["builtins.object", "builtins.str"]}, {".class": "UnionType", "items": ["builtins.str", "_pytest.monkeypatch.Notset"]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delattr of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "delenv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "raising"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.monkeypatch.MonkeyPatch.delenv", "name": "delenv", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "raising"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delenv of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "delitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "dic", "name", "raising"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.monkeypatch.MonkeyPatch.delitem", "name": "delitem", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "dic", "name", "raising"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.K", "id": -1, "name": "K", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.V", "id": -2, "name": "V", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Mapping"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.K", "id": -1, "name": "K", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delitem of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.K", "id": -1, "name": "K", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.V", "id": -2, "name": "V", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "setattr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "_pytest.monkeypatch.MonkeyPatch.setattr", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "target", "name", "value", "raising"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "_pytest.monkeypatch.MonkeyPatch.setattr", "name": "setattr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "target", "name", "value", "raising"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", {".class": "UnionType", "items": ["builtins.str", "builtins.object"]}, {".class": "UnionType", "items": ["builtins.object", "builtins.str"]}, "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setattr of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "target", "name", "value", "raising"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.monkeypatch.MonkeyPatch.setattr", "name": "setattr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "target", "name", "value", "raising"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", "builtins.str", "builtins.object", "_pytest.monkeypatch.Notset", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setattr of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.monkeypatch.MonkeyPatch.setattr", "name": "setattr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "target", "name", "value", "raising"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", "builtins.str", "builtins.object", "_pytest.monkeypatch.Notset", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setattr of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "target", "name", "value", "raising"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.monkeypatch.MonkeyPatch.setattr", "name": "setattr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "target", "name", "value", "raising"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", "builtins.object", "builtins.str", "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setattr of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.monkeypatch.MonkeyPatch.setattr", "name": "setattr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "target", "name", "value", "raising"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", "builtins.object", "builtins.str", "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setattr of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "target", "name", "value", "raising"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", "builtins.str", "builtins.object", "_pytest.monkeypatch.Notset", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setattr of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "target", "name", "value", "raising"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", "builtins.object", "builtins.str", "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setattr of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "setenv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "value", "prepend"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.monkeypatch.MonkeyPatch.setenv", "name": "setenv", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "value", "prepend"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setenv of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "setitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dic", "name", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.monkeypatch.MonkeyPatch.setitem", "name": "setitem", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dic", "name", "value"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.K", "id": -1, "name": "K", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.V", "id": -2, "name": "V", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Mapping"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.K", "id": -1, "name": "K", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.V", "id": -2, "name": "V", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setitem of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.K", "id": -1, "name": "K", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.V", "id": -2, "name": "V", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "syspath_prepend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.monkeypatch.MonkeyPatch.syspath_prepend", "name": "syspath_prepend", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "syspath_prepend of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "undo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.monkeypatch.MonkeyPatch.undo", "name": "undo", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.monkeypatch.MonkeyPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "undo of MonkeyPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.MonkeyPatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.monkeypatch.MonkeyPatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "Notset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.monkeypatch.Notset", "name": "Notset", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.monkeypatch.Notset", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.monkeypatch", "mro": ["_pytest.monkeypatch.Notset", "builtins.object"], "names": {".class": "SymbolTable", "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.monkeypatch.Notset.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["_pytest.monkeypatch.Notset"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Notset", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.Notset.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.monkeypatch.Notset", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PytestWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestWarning", "kind": "Gdef"}, "RE_IMPORT_ERROR_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.monkeypatch.RE_IMPORT_ERROR_NAME", "name": "RE_IMPORT_ERROR_NAME", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.monkeypatch.V", "name": "V", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.monkeypatch.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.monkeypatch.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.monkeypatch.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.monkeypatch.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.monkeypatch.__package__", "name": "__package__", "type": "builtins.str"}}, "annotated_getattr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["obj", "name", "ann"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.monkeypatch.annotated_getattr", "name": "annotated_getattr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["obj", "name", "ann"], "arg_types": ["builtins.object", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "annotated_getattr", "ret_type": "builtins.object", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "derive_importpath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["import_path", "raising"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.monkeypatch.derive_importpath", "name": "derive_importpath", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["import_path", "raising"], "arg_types": ["builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "derive_importpath", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.object"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef"}, "fixture": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures.fixture", "kind": "Gdef"}, "monkeypatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.monkeypatch.monkeypatch", "name": "monkeypatch", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "monkeypatch", "ret_type": {".class": "Instance", "args": ["_pytest.monkeypatch.MonkeyPatch", {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.monkeypatch.monkeypatch", "name": "monkeypatch", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "monkeypatch", "ret_type": {".class": "Instance", "args": ["_pytest.monkeypatch.MonkeyPatch", {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "notset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.monkeypatch.notset", "name": "notset", "type": "_pytest.monkeypatch.Notset"}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "resolve": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.monkeypatch.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve", "ret_type": "builtins.object", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\monkeypatch.py"}