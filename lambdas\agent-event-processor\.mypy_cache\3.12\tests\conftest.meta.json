{"data_mtime": 1757364118, "dep_lines": [13, 18, 8, 7, 10, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.agent_event_processor.config.settings", "src.agent_event_processor.models.events", "unittest.mock", "typing", "psycopg2", "pytest", "builtins", "_pytest", "_pytest.config", "_pytest.fixtures", "abc", "datetime", "enum", "psycopg2._psycopg", "psycopg2.extensions", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings", "pydantic_settings.main", "src", "src.agent_event_processor", "src.agent_event_processor.models", "unittest"], "hash": "6fed608ca0b42875095cc05db182614592100c545c354d20654c68721c34bca5", "id": "tests.conftest", "ignore_all": false, "interface_hash": "53fd30b2e661ed1aa5a4b065d831b7a9bdb14c5c835777af4942c79cc07e7a03", "mtime": 1757364063, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "tests\\conftest.py", "plugin_data": null, "size": 4752, "suppressed": [], "version_id": "1.8.0"}