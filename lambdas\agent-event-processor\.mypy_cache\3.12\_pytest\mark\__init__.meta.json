{"data_mtime": 1757363959, "dep_lines": [10, 12, 23, 19, 24, 27, 2, 3, 156, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.mark.expression", "_pytest.mark.structures", "_pytest.config.argparsing", "_pytest.config", "_pytest.stash", "_pytest.nodes", "dataclasses", "typing", "pytest", "builtins", "_pytest.compat", "_pytest.hookspec", "abc", "enum", "pluggy", "pluggy._hooks"], "hash": "b5f798510c29203a9f72f6563a371bd3b1759a7ca87aa5cbb635fe6564d58354", "id": "_pytest.mark", "ignore_all": true, "interface_hash": "894c91b8c9640bc9c5076dcdbb7eb8d3caf258e081cabea5be6a1ab6e55f0bb2", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\mark\\__init__.py", "plugin_data": null, "size": 8468, "suppressed": [], "version_id": "1.8.0"}