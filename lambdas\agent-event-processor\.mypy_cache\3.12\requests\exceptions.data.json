{".class": "MypyFile", "_fullname": "requests.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseHTTPError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.HTTPError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ChunkedEncodingError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.ChunkedEncodingError", "name": "ChunkedEncodingError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.ChunkedEncodingError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.ChunkedEncodingError", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.ChunkedEncodingError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.ChunkedEncodingError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.ConnectionError", "requests.exceptions.Timeout"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.ConnectTimeout", "name": "ConnectTimeout", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.ConnectTimeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.ConnectTimeout", "requests.exceptions.ConnectionError", "requests.exceptions.Timeout", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.ConnectTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.ConnectTimeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.ConnectionError", "name": "ConnectionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.ConnectionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.ConnectionError", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.ConnectionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.ConnectionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContentDecodingError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException", "urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.ContentDecodingError", "name": "ContentDecodingError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.ContentDecodingError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.ContentDecodingError", "requests.exceptions.RequestException", "builtins.OSError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.ContentDecodingError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.ContentDecodingError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FileModeWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestsWarning", "builtins.DeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.FileModeWarning", "name": "FileModeWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.FileModeWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.FileModeWarning", "requests.exceptions.RequestsWarning", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.FileModeWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.FileModeWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.HTTPError", "name": "HTTPError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.HTTPError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.HTTPError", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "requests.exceptions.HTTPError.request", "name": "request", "type": {".class": "UnionType", "items": ["requests.models.Request", "requests.models.PreparedRequest", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}]}}}, "response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "requests.exceptions.HTTPError.response", "name": "response", "type": {".class": "UnionType", "items": ["requests.models.Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.HTTPError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.HTTPError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidHeader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.InvalidHeader", "name": "InvalidHeader", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.InvalidHeader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.InvalidHeader", "requests.exceptions.RequestException", "builtins.OSError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.InvalidHeader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.InvalidHeader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidJSONError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.InvalidJSONError", "name": "InvalidJSONError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.InvalidJSONError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.InvalidJSONError", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.InvalidJSONError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.InvalidJSONError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidProxyURL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.InvalidURL"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.InvalidProxyURL", "name": "InvalidProxyURL", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.InvalidProxyURL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.InvalidProxyURL", "requests.exceptions.InvalidURL", "requests.exceptions.RequestException", "builtins.OSError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.InvalidProxyURL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.InvalidProxyURL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.InvalidSchema", "name": "InvalidSchema", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.InvalidSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.InvalidSchema", "requests.exceptions.RequestException", "builtins.OSError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.InvalidSchema.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.InvalidSchema", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidURL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.InvalidURL", "name": "InvalidURL", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.InvalidURL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.InvalidURL", "requests.exceptions.RequestException", "builtins.OSError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.InvalidURL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.InvalidURL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JSONDecodeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.InvalidJSONError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.JSONDecodeError", "name": "JSONDecodeError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.JSONDecodeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.JSONDecodeError", "requests.exceptions.InvalidJSONError", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.JSONDecodeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.JSONDecodeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MissingSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.MissingSchema", "name": "MissingSchema", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.MissingSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.MissingSchema", "requests.exceptions.RequestException", "builtins.OSError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.MissingSchema.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.MissingSchema", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PreparedRequest": {".class": "SymbolTableNode", "cross_ref": "requests.sessions.PreparedRequest", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ProxyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.ConnectionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.ProxyError", "name": "ProxyError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.ProxyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.ProxyError", "requests.exceptions.ConnectionError", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.ProxyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.ProxyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.Timeout"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.ReadTimeout", "name": "ReadTimeout", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.ReadTimeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.ReadTimeout", "requests.exceptions.Timeout", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.ReadTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.ReadTimeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Request": {".class": "SymbolTableNode", "cross_ref": "requests.models.Request", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RequestException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.OSError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.RequestException", "name": "RequestException", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.RequestException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5], "arg_names": ["self", "args", "request", "response"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.exceptions.RequestException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["self", "args", "request", "response"], "arg_types": ["requests.exceptions.RequestException", "builtins.object", {".class": "UnionType", "items": ["requests.models.Request", "requests.models.PreparedRequest", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["requests.models.Response", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RequestException", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "requests.exceptions.RequestException.request", "name": "request", "type": {".class": "UnionType", "items": ["requests.models.Request", "requests.models.PreparedRequest", {".class": "NoneType"}]}}}, "response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "requests.exceptions.RequestException.response", "name": "response", "type": {".class": "UnionType", "items": ["requests.models.Response", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.RequestException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.RequestException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RequestsDependencyWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestsWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.RequestsDependencyWarning", "name": "RequestsDependencyWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.RequestsDependencyWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.RequestsDependencyWarning", "requests.exceptions.RequestsWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.RequestsDependencyWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.RequestsDependencyWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RequestsWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Warning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.RequestsWarning", "name": "RequestsWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.RequestsWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.RequestsWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.RequestsWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.RequestsWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Response": {".class": "SymbolTableNode", "cross_ref": "requests.models.Response", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RetryError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.RetryError", "name": "RetryError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.RetryError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.RetryError", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.RetryError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.RetryError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSLError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.ConnectionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.SSLError", "name": "SSLError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.SSLError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.SSLError", "requests.exceptions.ConnectionError", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.SSLError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.SSLError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamConsumedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException", "builtins.TypeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.StreamConsumedError", "name": "StreamConsumedError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.StreamConsumedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.StreamConsumedError", "requests.exceptions.RequestException", "builtins.OSError", "builtins.TypeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.StreamConsumedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.StreamConsumedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Timeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.Timeout", "name": "Timeout", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.Timeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.Timeout", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.Timeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.Timeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TooManyRedirects": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.TooManyRedirects", "name": "TooManyRedirects", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.TooManyRedirects", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.TooManyRedirects", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.TooManyRedirects.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.TooManyRedirects", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "URLRequired": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.URLRequired", "name": "URLRequired", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.URLRequired", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.URLRequired", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.URLRequired.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.URLRequired", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnrewindableBodyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.exceptions.RequestException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.exceptions.UnrewindableBodyError", "name": "UnrewindableBodyError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.exceptions.UnrewindableBodyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.exceptions", "mro": ["requests.exceptions.UnrewindableBodyError", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.exceptions.UnrewindableBodyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.exceptions.UnrewindableBodyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\requests-stubs\\exceptions.pyi"}