{"data_mtime": 1757364036, "dep_lines": [1, 3, 2, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30], "dependencies": ["email.message", "urllib.response", "typing", "builtins", "abc", "email"], "hash": "23eba188131937db6e62b8de6567336c14f93307bb5fe12ea6a7fbe3fe1e5e0a", "id": "urllib.error", "ignore_all": true, "interface_hash": "75f55a2b30d23f18837bba7d5f6e1c3c90ae2016d4878b3f888fa75a46f83443", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\urllib\\error.pyi", "plugin_data": null, "size": 816, "suppressed": [], "version_id": "1.8.0"}