"""
Unit tests for data models.

This module tests the Pydantic models for agent events and database
credentials with comprehensive validation scenarios.
"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from src.agent_event_processor.models.events import AgentEvent, EventType


class TestAgentEvent:
    """Test suite for AgentEvent model."""

    def test_valid_login_event(self, sample_login_event):
        """Test creation of valid login event."""
        event = AgentEvent.model_validate(sample_login_event)

        assert event.event_type == EventType.LOGIN
        assert event.agent == "john.doe"
        assert event.agency_or_element == "Brandon911"
        assert event.get_agent_uri() == "tel:+2045553006"
        assert event.get_value("reason") == "normal"
        assert isinstance(event.timestamp, datetime)

    def test_valid_acd_login_event(self, sample_acd_login_event):
        """Test creation of valid ACD login event."""
        event = AgentEvent.model_validate(sample_acd_login_event)

        assert event.event_type == EventType.ACD_LOGIN
        assert event.get_ring_group_name() == "911 Queue"
        assert event.get_value("ringGroupUri") == "sip:<EMAIL>"

    def test_timestamp_parsing(self):
        """Test various timestamp formats."""
        # ISO format with Z
        event_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "eventType": "Login",
            "agencyOrElement": "Test",
            "agent": "test.user",
            "event_data": {},
        }
        event = AgentEvent.model_validate(event_data)
        assert isinstance(event.timestamp, datetime)

        # ISO format with timezone
        event_data["timestamp"] = "2024-01-15T10:30:00+00:00"
        event = AgentEvent.model_validate(event_data)
        assert isinstance(event.timestamp, datetime)

    def test_invalid_agent_uri(self):
        """Test validation of agent URI format - now handled by business logic."""
        # Note: URI validation is now handled in business logic, not model validation
        event_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "eventType": "Login",
            "agencyOrElement": "Test",
            "agent": "test.user",
            "event_data": {"uri": "invalid-uri"},  # This will be accepted by model
        }

        # Model validation should pass, business logic can validate URI format
        event = AgentEvent.model_validate(event_data)
        assert event.get_agent_uri() == "invalid-uri"

    def test_missing_required_fields(self):
        """Test validation with missing required fields."""
        incomplete_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "eventType": "Login",
            # Missing agencyOrElement, agent, and event_data
        }

        with pytest.raises(ValidationError) as exc_info:
            AgentEvent.model_validate(incomplete_data)

        errors = exc_info.value.errors()
        error_fields = [error["loc"][0] for error in errors]
        assert "agencyOrElement" in error_fields
        assert "agent" in error_fields
        assert "event_data" in error_fields

    def test_acd_event_validation(self):
        """Test ACD event specific validation."""
        # ACD Login without ringGroupName should fail
        event_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "eventType": "ACDLogin",
            "agencyOrElement": "Test",
            "agent": "test.user",
            "event_data": {},  # Missing ringGroupName
        }

        with pytest.raises(ValidationError) as exc_info:
            AgentEvent.model_validate(event_data)

        assert "ringGroupName" in str(exc_info.value)

    def test_extra_fields_rejected(self):
        """Test that extra fields are rejected."""
        event_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "eventType": "Login",
            "agencyOrElement": "Test",
            "agent": "test.user",
            "event_data": {},
            "extra_field": "should_be_rejected",
        }

        with pytest.raises(ValidationError) as exc_info:
            AgentEvent.model_validate(event_data)

        assert "extra_field" in str(exc_info.value)
