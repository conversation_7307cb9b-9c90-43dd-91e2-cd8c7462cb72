{"data_mtime": 1757356839, "dep_lines": [7, 10, 8, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "botocore.utils", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "abc"], "hash": "5ae0cab74826abf7c0a6696559ffc9c109fd6d315fdc19ba484a7295cec537ae", "id": "botocore.model", "ignore_all": true, "interface_hash": "336cc6a4d2967fd9ea1289a86a8e53687eec4db817e6d0b60364129b3eda93bc", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\model.pyi", "plugin_data": null, "size": 6931, "suppressed": [], "version_id": "1.8.0"}