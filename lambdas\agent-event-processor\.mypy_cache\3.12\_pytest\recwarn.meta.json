{"data_mtime": 1757363959, "dep_lines": [18, 20, 22, 23, 2, 3, 4, 5, 6, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["_pytest.compat", "_pytest.deprecated", "_pytest.fixtures", "_pytest.outcomes", "re", "warnings", "pprint", "types", "typing", "builtins", "_pytest.config", "_pytest.warning_types", "abc"], "hash": "28e51d5c154e73766a1c3bce099495c416d3e1251ececebcb8c6961f4ba36ac0", "id": "_pytest.recwarn", "ignore_all": true, "interface_hash": "e53e66d4de2895c96dc8c369955687be0b05dca3a2d6ff2a8e19db1476930d9f", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\recwarn.py", "plugin_data": null, "size": 10930, "suppressed": [], "version_id": "1.8.0"}