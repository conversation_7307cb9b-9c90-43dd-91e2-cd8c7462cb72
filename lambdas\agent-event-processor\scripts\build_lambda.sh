#!/bin/bash
# Bash script to build Lambda function for LocalStack deployment

set -e

# Configuration
OUTPUT_DIR="build"
ZIP_NAME="agent-event-processor.zip"
CLEAN=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --clean)
            CLEAN=true
            shift
            ;;
        --output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --zip-name)
            ZIP_NAME="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [--clean] [--output-dir DIR] [--zip-name NAME]"
            echo "  --clean         Clean build directory before building"
            echo "  --output-dir    Output directory for build (default: build)"
            echo "  --zip-name      Name of the zip file (default: agent-event-processor.zip)"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "Building Lambda function for LocalStack deployment..."

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_DIR/$OUTPUT_DIR"

# Clean build directory if requested
if [ "$CLEAN" = true ] && [ -d "$BUILD_DIR" ]; then
    echo "Cleaning build directory..."
    rm -rf "$BUILD_DIR"
fi

# Create build directory
mkdir -p "$BUILD_DIR"
echo "Build directory: $BUILD_DIR"

# Copy source code
echo "Copying source code..."
SOURCE_DIR="$PROJECT_DIR/src"
TARGET_SOURCE_DIR="$BUILD_DIR/src"

if [ -d "$TARGET_SOURCE_DIR" ]; then
    rm -rf "$TARGET_SOURCE_DIR"
fi

cp -r "$SOURCE_DIR" "$TARGET_SOURCE_DIR"

# Install dependencies
echo "Installing dependencies..."
REQUIREMENTS_FILE="$PROJECT_DIR/requirements.txt"

if [ ! -f "$REQUIREMENTS_FILE" ]; then
    echo "Error: Requirements file not found: $REQUIREMENTS_FILE"
    exit 1
fi

# Install to build directory
cd "$BUILD_DIR"

# Check if we're in a virtual environment or if python3 is available
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "Error: Python not found"
    exit 1
fi

echo "Using Python: $PYTHON_CMD"

# Install dependencies
$PYTHON_CMD -m pip install -r "$REQUIREMENTS_FILE" -t . --no-deps --upgrade

if [ $? -ne 0 ]; then
    echo "Error: Failed to install dependencies"
    exit 1
fi

# Create deployment package
echo "Creating deployment package..."
ZIP_PATH="$PROJECT_DIR/$ZIP_NAME"

if [ -f "$ZIP_PATH" ]; then
    rm "$ZIP_PATH"
fi

# Create zip file
if command -v zip &> /dev/null; then
    echo "Using zip command for compression..."
    zip -r "$ZIP_PATH" . -x "*.pyc" "*/__pycache__/*" "*.git/*"
else
    echo "Error: zip command not found. Please install zip utility."
    exit 1
fi

if [ $? -ne 0 ]; then
    echo "Error: Failed to create zip file"
    exit 1
fi

# Return to original directory
cd "$PROJECT_DIR"

# Verify the package
if [ -f "$ZIP_PATH" ]; then
    ZIP_SIZE=$(stat -f%z "$ZIP_PATH" 2>/dev/null || stat -c%s "$ZIP_PATH" 2>/dev/null)
    ZIP_SIZE_MB=$(echo "scale=2; $ZIP_SIZE / 1024 / 1024" | bc -l 2>/dev/null || echo "$(($ZIP_SIZE / 1024 / 1024))")
    
    echo "Lambda package created successfully!"
    echo "Package: $ZIP_PATH"
    echo "Size: ${ZIP_SIZE_MB} MB"
    
    # List contents
    echo ""
    echo "Package contents (first 20 files):"
    unzip -l "$ZIP_PATH" | head -25
else
    echo "Error: Failed to create Lambda package"
    exit 1
fi

echo ""
echo "Build completed successfully!"
echo "You can now deploy this package to LocalStack using:"
echo "  docker-compose up -d"
echo "  ./scripts/setup_localstack_complete.sh"
