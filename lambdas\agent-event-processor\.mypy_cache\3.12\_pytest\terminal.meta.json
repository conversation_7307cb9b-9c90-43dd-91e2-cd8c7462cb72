{"data_mtime": 1757363959, "dep_lines": [39, 41, 42, 48, 35, 36, 37, 38, 40, 43, 44, 51, 53, 60, 535, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 33, 35, 58, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 10, 5, 5, 5, 5, 5, 5, 25, 20, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest._io.wcwidth", "_pytest.assertion.util", "_pytest.config.argparsing", "_pytest._version", "_pytest.nodes", "_pytest.timing", "_pytest._code", "_pytest._io", "_pytest.compat", "_pytest.config", "_pytest.pathlib", "_pytest.reports", "_pytest.main", "_pytest.warnings", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "datetime", "inspect", "platform", "sys", "textwrap", "warnings", "collections", "functools", "pathlib", "typing", "pluggy", "_pytest", "typing_extensions", "builtins", "_collections_abc", "_pytest._io.terminalwriter", "_pytest.config.compat", "_pytest.hookspec", "_pytest.mark", "abc", "enum", "os", "pluggy._hooks", "types"], "hash": "161f416fef18b3218fcc9890bed15ae603041021d40c19703b7350e9ee7a3188", "id": "_pytest.terminal", "ignore_all": true, "interface_hash": "f3878a25815aa528eea35af9bcfa2b39d4be706d738d12980fefa8327f143bdc", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\terminal.py", "plugin_data": null, "size": 53509, "suppressed": [], "version_id": "1.8.0"}