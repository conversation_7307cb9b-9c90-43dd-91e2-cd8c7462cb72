{"data_mtime": 1757356839, "dep_lines": [13, 16, 23, 24, 17, 19, 21, 22, 32, 3, 5, 6, 7, 8, 9, 10, 12, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic_settings.sources.types", "pydantic_settings.sources.utils", "pydantic.fields", "typing_inspection.introspection", "pydantic_settings.exceptions", "pydantic_settings.utils", "pydantic_settings.main", "__future__", "json", "os", "abc", "dataclasses", "pathlib", "typing", "pydantic", "typing_extensions", "builtins", "pyexpat.errors", "pyexpat.model", "_collections_abc", "_typeshed", "pydantic._internal", "pydantic._internal._dataclasses", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.main", "re"], "hash": "a7e585aef875bdc98290bf43675635d60cbcee7d510a97f648f3790682f642a3", "id": "pydantic_settings.sources.base", "ignore_all": true, "interface_hash": "34f792b4a3a08a0cdb5e5161dc94a44759b6c1262097ccee0efd0d1e2f0e6c2a", "mtime": 1757091892, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_settings\\sources\\base.py", "plugin_data": null, "size": 20467, "suppressed": [], "version_id": "1.8.0"}