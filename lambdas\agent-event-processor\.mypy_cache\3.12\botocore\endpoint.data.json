{".class": "MypyFile", "_fullname": "botocore.endpoint", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AWSPreparedRequest": {".class": "SymbolTableNode", "cross_ref": "botocore.awsrequest.AWSPreparedRequest", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseEventHooks": {".class": "SymbolTableNode", "cross_ref": "botocore.hooks.BaseEventHooks", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DEFAULT_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.endpoint.DEFAULT_TIMEOUT", "name": "DEFAULT_TIMEOUT", "type": "builtins.int"}}, "Endpoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.endpoint.Endpoint", "name": "Endpoint", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.endpoint.Endpoint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.endpoint", "mro": ["botocore.endpoint.Endpoint", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "host", "endpoint_prefix", "event_emitter", "response_parser_factory", "http_session"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.endpoint.Endpoint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "host", "endpoint_prefix", "event_emitter", "response_parser_factory", "http_session"], "arg_types": ["botocore.endpoint.Endpoint", "builtins.str", "builtins.str", "botocore.hooks.BaseEventHooks", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["botocore.httpsession.URLLib3Session", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Endpoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.endpoint.Endpoint.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.endpoint.Endpoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of Endpoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "params", "operation_model"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.endpoint.Endpoint.create_request", "name": "create_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "params", "operation_model"], "arg_types": ["botocore.endpoint.Endpoint", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["botocore.model.OperationModel", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_request of Endpoint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.endpoint.Endpoint.host", "name": "host", "type": "builtins.str"}}, "http_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.endpoint.Endpoint.http_session", "name": "http_session", "type": "botocore.httpsession.URLLib3Session"}}, "make_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "operation_model", "request_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.endpoint.Endpoint.make_request", "name": "make_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "operation_model", "request_dict"], "arg_types": ["botocore.endpoint.Endpoint", "botocore.model.OperationModel", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_request of Endpoint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "prepare_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.endpoint.Endpoint.prepare_request", "name": "prepare_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.endpoint.Endpoint", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_request of Endpoint", "ret_type": "botocore.awsrequest.AWSPreparedRequest", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.endpoint.Endpoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.endpoint.Endpoint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EndpointCreator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.endpoint.EndpointCreator", "name": "EndpointCreator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.endpoint.EndpointCreator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.endpoint", "mro": ["botocore.endpoint.EndpointCreator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_emitter"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.endpoint.EndpointCreator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_emitter"], "arg_types": ["botocore.endpoint.EndpointCreator", "botocore.hooks.BaseEventHooks"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EndpointCreator", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "service_model", "region_name", "endpoint_url", "verify", "response_parser_factory", "timeout", "max_pool_connections", "http_session_cls", "proxies", "socket_options", "client_cert", "proxies_config"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.endpoint.EndpointCreator.create_endpoint", "name": "create_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "service_model", "region_name", "endpoint_url", "verify", "response_parser_factory", "timeout", "max_pool_connections", "http_session_cls", "proxies", "socket_options", "client_cert", "proxies_config"], "arg_types": ["botocore.endpoint.EndpointCreator", "botocore.model.ServiceModel", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["botocore.parsers.ResponseParserFactory", {".class": "NoneType"}]}, "builtins.float", "builtins.int", {".class": "TypeType", "item": "botocore.httpsession.URLLib3Session"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.int"], "type_ref": "builtins.tuple"}], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_endpoint of EndpointCreator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.endpoint.EndpointCreator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.endpoint.EndpointCreator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPClientError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.HTTPClientError", "kind": "Gdef"}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MAX_POOL_CONNECTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.endpoint.MAX_POOL_CONNECTIONS", "name": "MAX_POOL_CONNECTIONS", "type": "builtins.int"}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OperationModel": {".class": "SymbolTableNode", "cross_ref": "botocore.model.OperationModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ResponseParserFactory": {".class": "SymbolTableNode", "cross_ref": "botocore.parsers.ResponseParserFactory", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ServiceModel": {".class": "SymbolTableNode", "cross_ref": "botocore.model.ServiceModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StreamingBody": {".class": "SymbolTableNode", "cross_ref": "botocore.response.StreamingBody", "kind": "Gdef"}, "URLLib3Session": {".class": "SymbolTableNode", "cross_ref": "botocore.httpsession.URLLib3Session", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.endpoint.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.endpoint.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.endpoint.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.endpoint.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.endpoint.__package__", "name": "__package__", "type": "builtins.str"}}, "convert_to_response_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_response", "operation_model"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.endpoint.convert_to_response_dict", "name": "convert_to_response_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_response", "operation_model"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "botocore.model.OperationModel"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert_to_response_dict", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_request_object": {".class": "SymbolTableNode", "cross_ref": "botocore.awsrequest.create_request_object", "kind": "Gdef"}, "first_non_none_response": {".class": "SymbolTableNode", "cross_ref": "botocore.hooks.first_non_none_response", "kind": "Gdef"}, "get_environ_proxies": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.get_environ_proxies", "kind": "Gdef"}, "get_global_history_recorder": {".class": "SymbolTableNode", "cross_ref": "botocore.history.get_global_history_recorder", "kind": "Gdef"}, "history_recorder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.endpoint.history_recorder", "name": "history_recorder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "is_valid_endpoint_url": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.is_valid_endpoint_url", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.endpoint.logger", "name": "logger", "type": "logging.Logger"}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\endpoint.pyi"}