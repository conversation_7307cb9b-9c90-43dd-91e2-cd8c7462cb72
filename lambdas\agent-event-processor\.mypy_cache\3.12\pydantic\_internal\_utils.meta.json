{"data_mtime": 1757356838, "dep_lines": [26, 26, 27, 14, 26, 32, 6, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 22, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 20, 25, 5, 10, 10, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._import_utils", "collections.abc", "pydantic._internal", "pydantic.main", "__future__", "dataclasses", "keyword", "typing", "warnings", "weakref", "collections", "copy", "functools", "inspect", "itertools", "types", "typing_extensions", "pydantic", "builtins", "pyexpat.errors", "pyexpat.model", "_collections_abc", "_typeshed", "_weakref", "abc", "pydantic._internal._model_construction", "pydantic.v1", "pydantic.v1.utils"], "hash": "bb7680d05b5546d766557808b266158a5126ce37dce9771d884f55be1cae777b", "id": "pydantic._internal._utils", "ignore_all": true, "interface_hash": "f064819880c75d99e762adc0d85ac0a2016e8d48a3aaa98cb5264ac861b7baac", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_utils.py", "plugin_data": null, "size": 15248, "suppressed": [], "version_id": "1.8.0"}