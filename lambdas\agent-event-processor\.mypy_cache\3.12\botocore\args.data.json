{".class": "MypyFile", "_fullname": "botocore.args", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseEventHooks": {".class": "SymbolTableNode", "cross_ref": "botocore.hooks.BaseEventHooks", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseRestSerializer": {".class": "SymbolTableNode", "cross_ref": "botocore.serialize.BaseRestSerializer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClientArgsCreator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.args.ClientArgsCreator", "name": "ClientArgsCreator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.args.ClientArgsCreator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.args", "mro": ["botocore.args.ClientArgsCreator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "event_emitter", "user_agent", "response_parser_factory", "loader", "exceptions_factory", "config_store", "user_agent_creator"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.args.ClientArgsCreator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "event_emitter", "user_agent", "response_parser_factory", "loader", "exceptions_factory", "config_store", "user_agent_creator"], "arg_types": ["botocore.args.ClientArgsCreator", "botocore.hooks.BaseEventHooks", "builtins.str", "botocore.parsers.ResponseParserFactory", "botocore.loaders.Loader", "botocore.errorfactory.ClientExceptionsFactory", "botocore.configprovider.ConfigValueStore", {".class": "UnionType", "items": ["botocore.useragent.UserAgentString", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClientArgsCreator", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "compute_client_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "service_model", "client_config", "endpoint_bridge", "region_name", "endpoint_url", "is_secure", "scoped_config"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.args.ClientArgsCreator.compute_client_args", "name": "compute_client_args", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "service_model", "client_config", "endpoint_bridge", "region_name", "endpoint_url", "is_secure", "scoped_config"], "arg_types": ["botocore.args.ClientArgsCreator", "botocore.model.ServiceModel", {".class": "UnionType", "items": ["botocore.config.Config", {".class": "NoneType"}]}, "botocore.client.ClientEndpointBridge", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compute_client_args of ClientArgsCreator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "compute_endpoint_resolver_builtin_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "region_name", "service_name", "s3_config", "endpoint_bridge", "client_endpoint_url", "legacy_endpoint_url", "credentials", "account_id_endpoint_mode"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.args.ClientArgsCreator.compute_endpoint_resolver_builtin_defaults", "name": "compute_endpoint_resolver_builtin_defaults", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "region_name", "service_name", "s3_config", "endpoint_bridge", "client_endpoint_url", "legacy_endpoint_url", "credentials", "account_id_endpoint_mode"], "arg_types": ["botocore.args.ClientArgsCreator", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "botocore.client.ClientEndpointBridge", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "preferred"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disabled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "required"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compute_endpoint_resolver_builtin_defaults of ClientArgsCreator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "compute_s3_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "client_config"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.args.ClientArgsCreator.compute_s3_config", "name": "compute_s3_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "client_config"], "arg_types": ["botocore.args.ClientArgsCreator", {".class": "UnionType", "items": ["botocore.config.Config", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compute_s3_config of ClientArgsCreator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_client_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "service_model", "region_name", "is_secure", "endpoint_url", "verify", "credentials", "scoped_config", "client_config", "endpoint_bridge", "auth_token", "endpoints_ruleset_data", "partition_data"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.args.ClientArgsCreator.get_client_args", "name": "get_client_args", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "service_model", "region_name", "is_secure", "endpoint_url", "verify", "credentials", "scoped_config", "client_config", "endpoint_bridge", "auth_token", "endpoints_ruleset_data", "partition_data"], "arg_types": ["botocore.args.ClientArgsCreator", "botocore.model.ServiceModel", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["botocore.config.Config", {".class": "NoneType"}]}, "botocore.client.ClientEndpointBridge", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_client_args of ClientArgsCreator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.args._GetClientArgsTypeDef"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.args.ClientArgsCreator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.args.ClientArgsCreator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClientConfigString": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str", "botocore.args.ConfigObjectWrapper"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.args.ClientConfigString", "name": "ClientConfigString", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.args.ClientConfigString", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "botocore.args", "mro": ["botocore.args.ClientConfigString", "builtins.str", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "botocore.args.ConfigObjectWrapper", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cls", "value"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "botocore.args.ClientConfigString.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.args._R", "id": -1, "name": "_R", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ClientConfigString", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.args._R", "id": -1, "name": "_R", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.args._R", "id": -1, "name": "_R", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.args.ClientConfigString.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.args.ClientConfigString", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClientEndpointBridge": {".class": "SymbolTableNode", "cross_ref": "botocore.client.ClientEndpointBridge", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClientExceptionsFactory": {".class": "SymbolTableNode", "cross_ref": "botocore.errorfactory.ClientExceptionsFactory", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Config": {".class": "SymbolTableNode", "cross_ref": "botocore.config.Config", "kind": "Gdef"}, "ConfigObjectWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.args.ConfigObjectWrapper", "name": "ConfigObjectWrapper", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.args.ConfigObjectWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.args", "mro": ["botocore.args.ConfigObjectWrapper", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.args.ConfigObjectWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.args.ConfigObjectWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConfigValueStore": {".class": "SymbolTableNode", "cross_ref": "botocore.configprovider.ConfigValueStore", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Credentials": {".class": "SymbolTableNode", "cross_ref": "botocore.credentials.Credentials", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Endpoint": {".class": "SymbolTableNode", "cross_ref": "botocore.endpoint.Endpoint", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EndpointCreator": {".class": "SymbolTableNode", "cross_ref": "botocore.endpoint.EndpointCreator", "kind": "Gdef"}, "LEGACY_GLOBAL_STS_REGIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.args.LEGACY_GLOBAL_STS_REGIONS", "name": "LEGACY_GLOBAL_STS_REGIONS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Loader": {".class": "SymbolTableNode", "cross_ref": "botocore.loaders.Loader", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PRIORITY_ORDERED_SUPPORTED_PROTOCOLS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.args.PRIORITY_ORDERED_SUPPORTED_PROTOCOLS", "name": "PRIORITY_ORDERED_SUPPORTED_PROTOCOLS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}}, "RequestSigner": {".class": "SymbolTableNode", "cross_ref": "botocore.signers.RequestSigner", "kind": "Gdef"}, "ResponseParser": {".class": "SymbolTableNode", "cross_ref": "botocore.parsers.ResponseParser", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ResponseParserFactory": {".class": "SymbolTableNode", "cross_ref": "botocore.parsers.ResponseParserFactory", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ServiceModel": {".class": "SymbolTableNode", "cross_ref": "botocore.model.ServiceModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "USERAGENT_APPID_MAXLEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.args.USERAGENT_APPID_MAXLEN", "name": "USERAGENT_APPID_MAXLEN", "type": "builtins.int"}}, "UserAgentString": {".class": "SymbolTableNode", "cross_ref": "botocore.useragent.UserAgentString", "kind": "Gdef", "module_hidden": true, "module_public": false}, "VALID_ACCOUNT_ID_ENDPOINT_MODE_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.args.VALID_ACCOUNT_ID_ENDPOINT_MODE_CONFIG", "name": "VALID_ACCOUNT_ID_ENDPOINT_MODE_CONFIG", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}}, "VALID_REGIONAL_ENDPOINTS_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.args.VALID_REGIONAL_ENDPOINTS_CONFIG", "name": "VALID_REGIONAL_ENDPOINTS_CONFIG", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "VALID_REQUEST_CHECKSUM_CALCULATION_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.args.VALID_REQUEST_CHECKSUM_CALCULATION_CONFIG", "name": "VALID_REQUEST_CHECKSUM_CALCULATION_CONFIG", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}}, "VALID_RESPONSE_CHECKSUM_VALIDATION_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.args.VALID_RESPONSE_CHECKSUM_VALIDATION_CONFIG", "name": "VALID_RESPONSE_CHECKSUM_VALIDATION_CONFIG", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}}, "_GetClientArgsTypeDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.args._GetClientArgsTypeDef", "name": "_GetClientArgsTypeDef", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.args._GetClientArgsTypeDef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.args", "mro": ["botocore.args._GetClientArgsTypeDef", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["serializer", "botocore.serialize.BaseRestSerializer"], ["endpoint", "botocore.endpoint.Endpoint"], ["response_parser", "botocore.parsers.ResponseParser"], ["event_emitter", "botocore.hooks.BaseEventHooks"], ["request_signer", "botocore.signers.RequestSigner"], ["service_model", "botocore.model.ServiceModel"], ["loader", "botocore.loaders.Loader"], ["client_config", "botocore.config.Config"], ["partition", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["exceptions_factory", "botocore.errorfactory.ClientExceptionsFactory"]], "required_keys": ["client_config", "endpoint", "event_emitter", "exceptions_factory", "loader", "partition", "request_signer", "response_parser", "serializer", "service_model"]}}}, "_R": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.args._R", "name": "_R", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.args.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.args.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.args.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.args.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.args.__package__", "name": "__package__", "type": "builtins.str"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.args.logger", "name": "logger", "type": "logging.Logger"}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\args.pyi"}