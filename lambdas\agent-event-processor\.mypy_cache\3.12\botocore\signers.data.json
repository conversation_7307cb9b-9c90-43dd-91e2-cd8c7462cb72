{".class": "MypyFile", "_fullname": "botocore.signers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseEventHooks": {".class": "SymbolTableNode", "cross_ref": "botocore.hooks.BaseEventHooks", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CloudFrontSigner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.signers.CloudFrontSigner", "name": "CloudFrontSigner", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.signers.CloudFrontSigner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.signers", "mro": ["botocore.signers.CloudFrontSigner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key_id", "rsa_signer"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.CloudFrontSigner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key_id", "rsa_signer"], "arg_types": ["botocore.signers.CloudFrontSigner", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CloudFrontSigner", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "build_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "resource", "date_less_than", "date_greater_than", "ip_address"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.CloudFrontSigner.build_policy", "name": "build_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "resource", "date_less_than", "date_greater_than", "ip_address"], "arg_types": ["botocore.signers.CloudFrontSigner", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_policy of CloudFrontSigner", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "generate_presigned_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "url", "date_less_than", "policy"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.CloudFrontSigner.generate_presigned_url", "name": "generate_presigned_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "url", "date_less_than", "policy"], "arg_types": ["botocore.signers.CloudFrontSigner", "builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_presigned_url of CloudFrontSigner", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "key_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.signers.CloudFrontSigner.key_id", "name": "key_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "rsa_signer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.signers.CloudFrontSigner.rsa_signer", "name": "rsa_signer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.signers.CloudFrontSigner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.signers.CloudFrontSigner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Credentials": {".class": "SymbolTableNode", "cross_ref": "botocore.credentials.Credentials", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "ReadOnlyCredentials": {".class": "SymbolTableNode", "cross_ref": "botocore.credentials.ReadOnlyCredentials", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RequestSigner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.signers.RequestSigner", "name": "RequestSigner", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.signers.RequestSigner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.signers", "mro": ["botocore.signers.RequestSigner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "service_id", "region_name", "signing_name", "signature_version", "credentials", "event_emitter", "auth_token"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.RequestSigner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "service_id", "region_name", "signing_name", "signature_version", "credentials", "event_emitter", "auth_token"], "arg_types": ["botocore.signers.RequestSigner", "botocore.model.ServiceId", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}]}, "botocore.hooks.BaseEventHooks", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RequestSigner", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "generate_presigned_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "request_dict", "operation_name", "expires_in", "region_name", "signing_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.RequestSigner.generate_presigned_url", "name": "generate_presigned_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "request_dict", "operation_name", "expires_in", "region_name", "signing_name"], "arg_types": ["botocore.signers.RequestSigner", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_presigned_url of RequestSigner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.signers.RequestSigner.get_auth", "name": "get_auth", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_auth_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "signing_name", "region_name", "signature_version", "request_credentials", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.RequestSigner.get_auth_instance", "name": "get_auth_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "signing_name", "region_name", "signature_version", "request_credentials", "kwargs"], "arg_types": ["botocore.signers.RequestSigner", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_auth_instance of RequestSigner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "operation_name", "request", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.RequestSigner.handler", "name": "handler", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "operation_name", "request", "kwargs"], "arg_types": ["botocore.signers.RequestSigner", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handler of RequestSigner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "region_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "botocore.signers.RequestSigner.region_name", "name": "region_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.signers.RequestSigner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "region_name of RequestSigner", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "botocore.signers.RequestSigner.region_name", "name": "region_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.signers.RequestSigner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "region_name of RequestSigner", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "operation_name", "request", "region_name", "signing_type", "expires_in", "signing_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.RequestSigner.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "operation_name", "request", "region_name", "signing_type", "expires_in", "signing_name"], "arg_types": ["botocore.signers.RequestSigner", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of Request<PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "signature_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "botocore.signers.RequestSigner.signature_version", "name": "signature_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.signers.RequestSigner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature_version of RequestSigner", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "botocore.signers.RequestSigner.signature_version", "name": "signature_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.signers.RequestSigner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature_version of RequestSigner", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "signing_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "botocore.signers.RequestSigner.signing_name", "name": "signing_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.signers.RequestSigner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signing_name of Request<PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "botocore.signers.RequestSigner.signing_name", "name": "signing_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.signers.RequestSigner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signing_name of Request<PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.signers.RequestSigner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.signers.RequestSigner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3PostPresigner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.signers.S3PostPresigner", "name": "S3PostPresigner", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.signers.S3PostPresigner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.signers", "mro": ["botocore.signers.S3PostPresigner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request_signer"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.S3PostPresigner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request_signer"], "arg_types": ["botocore.signers.S3PostPresigner", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3PostPresigner", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "generate_presigned_post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "request_dict", "fields", "conditions", "expires_in", "region_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.S3PostPresigner.generate_presigned_post", "name": "generate_presigned_post", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "request_dict", "fields", "conditions", "expires_in", "region_name"], "arg_types": ["botocore.signers.S3PostPresigner", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_presigned_post of S3PostPresigner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.signers.S3PostPresigner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.signers.S3PostPresigner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ServiceId": {".class": "SymbolTableNode", "cross_ref": "botocore.model.ServiceId", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UnknownClientMethodError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.UnknownClientMethodError", "kind": "Gdef"}, "UnknownSignatureVersionError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.UnknownSignatureVersionError", "kind": "Gdef"}, "UnsupportedSignatureVersionError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.UnsupportedSignatureVersionError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.signers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.signers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.signers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.signers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.signers.__package__", "name": "__package__", "type": "builtins.str"}}, "add_dsql_generate_db_auth_token_methods": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["class_attributes", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.add_dsql_generate_db_auth_token_methods", "name": "add_dsql_generate_db_auth_token_methods", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["class_attributes", "kwargs"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_dsql_generate_db_auth_token_methods", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_generate_db_auth_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["class_attributes", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.add_generate_db_auth_token", "name": "add_generate_db_auth_token", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["class_attributes", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_generate_db_auth_token", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_generate_presigned_post": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["class_attributes", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.add_generate_presigned_post", "name": "add_generate_presigned_post", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["class_attributes", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_generate_presigned_post", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_generate_presigned_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["class_attributes", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.add_generate_presigned_url", "name": "add_generate_presigned_url", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["class_attributes", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_generate_presigned_url", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_request_object": {".class": "SymbolTableNode", "cross_ref": "botocore.awsrequest.create_request_object", "kind": "Gdef"}, "datetime2timestamp": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.datetime2timestamp", "kind": "Gdef"}, "dsql_generate_db_connect_admin_auth_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "Hostname", "Region", "ExpiresIn"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.dsql_generate_db_connect_admin_auth_token", "name": "dsql_generate_db_connect_admin_auth_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "Hostname", "Region", "ExpiresIn"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dsql_generate_db_connect_admin_auth_token", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "dsql_generate_db_connect_auth_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "Hostname", "Region", "ExpiresIn"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.dsql_generate_db_connect_auth_token", "name": "dsql_generate_db_connect_auth_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "Hostname", "Region", "ExpiresIn"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dsql_generate_db_connect_auth_token", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "generate_db_auth_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "DBHostname", "Port", "DBUsername", "Region"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.generate_db_auth_token", "name": "generate_db_auth_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "DBHostname", "Port", "DBUsername", "Region"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_db_auth_token", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "generate_presigned_post": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "Bucket", "Key", "Fields", "Conditions", "ExpiresIn"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.generate_presigned_post", "name": "generate_presigned_post", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "Bucket", "Key", "Fields", "Conditions", "ExpiresIn"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_presigned_post", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "generate_presigned_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "ClientMethod", "Params", "ExpiresIn", "HttpMethod"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.signers.generate_presigned_url", "name": "generate_presigned_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "ClientMethod", "Params", "ExpiresIn", "HttpMethod"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "builtins.int", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_presigned_url", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "prepare_request_dict": {".class": "SymbolTableNode", "cross_ref": "botocore.awsrequest.prepare_request_dict", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\signers.pyi"}