{"data_mtime": 1757356838, "dep_lines": [7, 11, 12, 15, 8, 9, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "botocore.awsrequest", "botocore.compat", "botocore.exceptions", "logging", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "abc"], "hash": "504a6a26a0eec3b7515a49048397edc74f4de16f4f2ac763e28e8812b16c3cf1", "id": "botocore.httpsession", "ignore_all": true, "interface_hash": "f092836dadc90e8385b1b1fc95d87f90b07ba743c26d1b97cdd0b0f9ee295e1a", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\httpsession.pyi", "plugin_data": null, "size": 2373, "suppressed": [], "version_id": "1.8.0"}