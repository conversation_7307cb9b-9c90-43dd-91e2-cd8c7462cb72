<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">63%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-08 14:44 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1___init___py.html">src\agent_event_processor\__init__.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614___init___py.html">src\agent_event_processor\config\__init__.py</a></td>
                <td class="name left"><a href="z_20164841b8185614___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t104">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t104"><data value='get_settings'>get_settings</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html#t29">src\agent_event_processor\lambda_function.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html#t29"><data value='prepare_event_data'>_prepare_event_data</data></a></td>
                <td>15</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="13 15">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html#t70">src\agent_event_processor\lambda_function.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html#t70"><data value='process_single_record'>_process_single_record</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html#t115">src\agent_event_processor\lambda_function.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html#t115"><data value='lambda_handler'>lambda_handler</data></a></td>
                <td>29</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="25 29">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html">src\agent_event_processor\lambda_function.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa___init___py.html">src\agent_event_processor\models\__init__.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t59">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t59"><data value='get_value'>AgentEvent.get_value</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t83">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t83"><data value='get_agent_uri'>AgentEvent.get_agent_uri</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t88">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t88"><data value='get_ring_group_name'>AgentEvent.get_ring_group_name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t92">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t92"><data value='get_media_label'>AgentEvent.get_media_label</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t96">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t96"><data value='get_operator_id'>AgentEvent.get_operator_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t100">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t100"><data value='get_workstation'>AgentEvent.get_workstation</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t104">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t104"><data value='get_device_name'>AgentEvent.get_device_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t108">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t108"><data value='get_voice_qos'>AgentEvent.get_voice_qos</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t112">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t112"><data value='has_voice_qos'>AgentEvent.has_voice_qos</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t117">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t117"><data value='validate_acd_requirements'>AgentEvent.validate_acd_requirements</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942___init___py.html">src\agent_event_processor\services\__init__.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t27">src\agent_event_processor\services\database_repository.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t27"><data value='init__'>AgentEventRepository.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t33">src\agent_event_processor\services\database_repository.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t33"><data value='get_or_create_tenant_key'>AgentEventRepository.get_or_create_tenant_key</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t40">src\agent_event_processor\services\database_repository.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t40"><data value='get_or_create_agent_key'>AgentEventRepository.get_or_create_agent_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t46">src\agent_event_processor\services\database_repository.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t46"><data value='get_date_key'>AgentEventRepository.get_date_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t50">src\agent_event_processor\services\database_repository.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t50"><data value='get_time_key'>AgentEventRepository.get_time_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t54">src\agent_event_processor\services\database_repository.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t54"><data value='get_or_create_queue_key'>AgentEventRepository.get_or_create_queue_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t60">src\agent_event_processor\services\database_repository.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t60"><data value='insert_agent_event'>AgentEventRepository.insert_agent_event</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html">src\agent_event_processor\services\database_repository.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t50">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t50"><data value='init__'>RedshiftDataAPIConnection.__init__</data></a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t96">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t96"><data value='cursor'>RedshiftDataAPIConnection.cursor</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t100">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t100"><data value='close'>RedshiftDataAPIConnection.close</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t104">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t104"><data value='enter__'>RedshiftDataAPIConnection.__enter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t108">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t108"><data value='exit__'>RedshiftDataAPIConnection.__exit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t116">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t116"><data value='init__'>RedshiftDataAPICursor.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t127">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t127"><data value='execute'>RedshiftDataAPICursor.execute</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t159">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t159"><data value='prepare_sql_with_params'>RedshiftDataAPICursor._prepare_sql_with_params</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t183">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t183"><data value='convert_parameter_value'>RedshiftDataAPICursor._convert_parameter_value</data></a></td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="12 14">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t203">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t203"><data value='build_request_params'>RedshiftDataAPICursor._build_request_params</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t220">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t220"><data value='execute_statement'>RedshiftDataAPICursor._execute_statement</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t234">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t234"><data value='fetch_results_if_select'>RedshiftDataAPICursor._fetch_results_if_select</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t251">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t251"><data value='wait_for_completion'>RedshiftDataAPICursor._wait_for_completion</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t311">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t311"><data value='fetch_results'>RedshiftDataAPICursor._fetch_results</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t342">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t342"><data value='convert_record_to_dict'>RedshiftDataAPICursor._convert_record_to_dict</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t361">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t361"><data value='extract_field_value'>RedshiftDataAPICursor._extract_field_value</data></a></td>
                <td>12</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="10 12">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t378">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t378"><data value='fetchone'>RedshiftDataAPICursor.fetchone</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t382">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t382"><data value='fetchall'>RedshiftDataAPICursor.fetchall</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t386">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t386"><data value='rowcount'>RedshiftDataAPICursor.rowcount</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t390">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t390"><data value='close'>RedshiftDataAPICursor.close</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t394">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t394"><data value='enter__'>RedshiftDataAPICursor.__enter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t398">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t398"><data value='exit__'>RedshiftDataAPICursor.__exit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t406">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t406"><data value='init__'>DatabaseService.__init__</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t434">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t434"><data value='get_connection'>DatabaseService.get_connection</data></a></td>
                <td>13</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="6 13">46%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t461">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t461"><data value='execute_query'>DatabaseService.execute_query</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t472">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t472"><data value='execute_statement'>DatabaseService.execute_statement</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t489">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t489"><data value='init__'>DimensionManager.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t494">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t494"><data value='is_duplicate_key_error'>DimensionManager._is_duplicate_key_error</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t502">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t502"><data value='find_existing_tenant'>DimensionManager._find_existing_tenant</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t510">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t510"><data value='create_tenant_record'>DimensionManager._create_tenant_record</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t537">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t537"><data value='handle_tenant_race_condition'>DimensionManager._handle_tenant_race_condition</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t554">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t554"><data value='get_or_create_tenant_key'>DimensionManager.get_or_create_tenant_key</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t595">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t595"><data value='find_current_agent'>DimensionManager._find_current_agent</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t609">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t609"><data value='extract_agent_attributes'>DimensionManager._extract_agent_attributes</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t618">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t618"><data value='close_current_agent_record'>DimensionManager._close_current_agent_record</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t631">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t631"><data value='create_agent_record'>DimensionManager._create_agent_record</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t673">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t673"><data value='handle_agent_race_condition'>DimensionManager._handle_agent_race_condition</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t696">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t696"><data value='get_or_create_agent_key'>DimensionManager.get_or_create_agent_key</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t757">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t757"><data value='get_date_key'>DimensionManager.get_date_key</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t761">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t761"><data value='get_time_key'>DimensionManager.get_time_key</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t769">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t769"><data value='init__'>FactManager.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t774">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t774"><data value='is_duplicate_key_error'>FactManager._is_duplicate_key_error</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t782">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t782"><data value='insert_agent_event'>FactManager.insert_agent_event</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>71</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="71 71">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t28">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t28"><data value='init__'>EventProcessor.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t39">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t39"><data value='process_single_event'>EventProcessor.process_single_event</data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t111">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t111"><data value='parse_event_timestamp'>EventProcessor._parse_event_timestamp</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t132">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t132"><data value='extract_agent_data'>EventProcessor._extract_agent_data</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t143">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t143"><data value='extract_field_value'>EventProcessor._extract_field_value</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t159">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t159"><data value='convert_to_local_time'>EventProcessor._convert_to_local_time</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe___init___py.html">src\agent_event_processor\utils\__init__.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_decorators_py.html#t12">src\agent_event_processor\utils\decorators.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_decorators_py.html#t12"><data value='capture_method'>capture_method</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_decorators_py.html#t27">src\agent_event_processor\utils\decorators.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_decorators_py.html#t27"><data value='wrapper'>capture_method.wrapper</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_decorators_py.html">src\agent_event_processor\utils\decorators.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_decorators_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t20">src\agent_event_processor\utils\timezone_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t20"><data value='get_client_timezone'>get_client_timezone</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t35">src\agent_event_processor\utils\timezone_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t35"><data value='convert_to_tenant_timezone'>convert_to_tenant_timezone</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t82">src\agent_event_processor\utils\timezone_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t82"><data value='generate_dimension_keys'>generate_dimension_keys</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t108">src\agent_event_processor\utils\timezone_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t108"><data value='get_shift_date_key'>get_shift_date_key</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html">src\agent_event_processor\utils\timezone_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_xml_parser_py.html#t20">src\agent_event_processor\utils\xml_parser.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_xml_parser_py.html#t20"><data value='xml_to_json'>AgentEventXMLParser.xml_to_json</data></a></td>
                <td>14</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="13 14">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_xml_parser_py.html#t74">src\agent_event_processor\utils\xml_parser.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_xml_parser_py.html#t74"><data value='extract_xml_from_sqs_message'>AgentEventXMLParser.extract_xml_from_sqs_message</data></a></td>
                <td>17</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="11 17">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_xml_parser_py.html">src\agent_event_processor\utils\xml_parser.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_xml_parser_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>695</td>
                <td>258</td>
                <td>0</td>
                <td class="right" data-ratio="437 695">63%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-08 14:44 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
