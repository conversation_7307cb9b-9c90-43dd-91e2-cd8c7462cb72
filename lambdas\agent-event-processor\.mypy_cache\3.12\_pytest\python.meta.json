{"data_mtime": 1757363959, "dep_lines": [36, 40, 58, 66, 32, 33, 34, 39, 41, 55, 59, 63, 64, 70, 72, 78, 79, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 31, 84, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest._io.saferepr", "_pytest.config.argparsing", "_pytest.mark.structures", "_pytest.fixtures", "_pytest.nodes", "_pytest._code", "_pytest._io", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.main", "_pytest.mark", "_pytest.outcomes", "_pytest.pathlib", "_pytest.scope", "_pytest.warning_types", "dataclasses", "enum", "fnmatch", "inspect", "itertools", "os", "sys", "types", "warnings", "collections", "functools", "pathlib", "typing", "_pytest", "typing_extensions", "builtins", "_collections_abc", "_pytest._io.terminalwriter", "_pytest.hookspec", "_typeshed", "abc", "pluggy", "pluggy._hooks", "pluggy._manager", "pytest"], "hash": "e415652a719bd368b118320e8b43c980028a9f1f1f03623382ced1d1f7e94f64", "id": "_pytest.python", "ignore_all": true, "interface_hash": "09f45f620ca2056ab933eff04136a3b70e12a11769815d4fe5d2611abfb26591", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\python.py", "plugin_data": null, "size": 71399, "suppressed": [], "version_id": "1.8.0"}