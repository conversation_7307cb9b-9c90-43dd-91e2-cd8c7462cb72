{".class": "MypyFile", "_fullname": "aws_lambda_powertools", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Logger": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.logging.logger.Logger", "kind": "Gdef"}, "Metrics": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.metrics.Metrics", "kind": "Gdef"}, "PACKAGE_PATH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.PACKAGE_PATH", "name": "PACKAGE_PATH", "type": "pathlib.Path"}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_public": false}, "Tracer": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.tracing.tracer.Tracer", "kind": "Gdef"}, "VERSION": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.shared.version.VERSION", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.__author__", "name": "__author__", "type": "builtins.str"}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.__version__", "name": "__version__", "type": "builtins.str"}}, "inject_user_agent": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.shared.user_agent.inject_user_agent", "kind": "Gdef", "module_public": false}, "set_package_logger_handler": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.package_logger.set_package_logger_handler", "kind": "Gdef", "module_public": false}, "single_metric": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.base.single_metric", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\__init__.py"}