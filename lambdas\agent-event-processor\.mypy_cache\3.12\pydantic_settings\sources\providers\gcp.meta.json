{"data_mtime": **********, "dep_lines": [7, 3, 14, 1, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 11, 12, 10], "dep_prios": [5, 5, 25, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 25, 25, 25], "dependencies": ["pydantic_settings.sources.providers.env", "collections.abc", "pydantic_settings.main", "__future__", "functools", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "_collections_abc", "abc", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings.sources.base"], "hash": "ddb161ef9699a7a9a69f5d958a143270d0fee422e09d85a0e8705f36f215dba5", "id": "pydantic_settings.sources.providers.gcp", "ignore_all": true, "interface_hash": "58438f9d6c1cb074380dbe3a44492bac21443bf4bc36b54bd47a46b0680215bf", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_settings\\sources\\providers\\gcp.py", "plugin_data": null, "size": 5644, "suppressed": ["google.auth.credentials", "google.cloud.secretmanager", "google.auth"], "version_id": "1.8.0"}