{"data_mtime": 1757364038, "dep_lines": [3, 4, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["json", "typing", "pytest", "requests", "builtins", "_pytest", "_pytest.config", "_pytest.fixtures", "_pytest.mark", "_pytest.mark.structures", "_pytest.outcomes", "_typeshed", "abc", "http", "http.cookiejar", "json.decoder", "json.encoder", "requests.api", "requests.auth", "requests.exceptions", "requests.models"], "hash": "11b99c32c145665e1da970d7355b6e509a135fd6222419b0a7a77d68e80c2a85", "id": "tests.integration.test_lambda_integration", "ignore_all": false, "interface_hash": "963457e13e77e4defa47f13b3aa59cee79b68cf9c2b96bb8dd229dd3dff3a2c7", "mtime": 1757359245, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "tests\\integration\\test_lambda_integration.py", "plugin_data": null, "size": 9039, "suppressed": [], "version_id": "1.8.0"}