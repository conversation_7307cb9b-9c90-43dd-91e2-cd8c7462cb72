{"data_mtime": 1757356836, "dep_lines": [26, 28, 31, 47, 51, 60, 72, 76, 80, 91, 662, 18, 19, 20, 21, 22, 23, 24, 25, 26, 89, 1, 1, 1, 1, 1, 1, 1, 1, 84], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 20, 25, 5, 20, 20, 30, 30, 30, 30, 30, 10], "dependencies": ["concurrent.futures", "tenacity._utils", "tenacity.retry", "tenacity.nap", "tenacity.stop", "tenacity.wait", "tenacity.before", "tenacity.after", "tenacity.before_sleep", "tenacity.asyncio", "tenacity.tornadoweb", "dataclasses", "functools", "sys", "threading", "time", "typing", "warnings", "abc", "concurrent", "types", "builtins", "pyexpat.errors", "pyexpat.model", "_collections_abc", "_typeshed", "concurrent.futures._base", "tenacity.asyncio.retry", "typing_extensions"], "hash": "544a9aef72ea9b07749169f27f59c3091be9e09b613fd4705223506667e74d3c", "id": "tenacity", "ignore_all": true, "interface_hash": "b664fd6f9aecf9febad5441b2b56870db97d1b4c32f9af8a0e8cf5c11d499a23", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\tenacity\\__init__.py", "plugin_data": null, "size": 24021, "suppressed": ["tornado"], "version_id": "1.8.0"}