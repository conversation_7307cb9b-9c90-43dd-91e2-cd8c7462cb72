{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.6", "globals": "8812dcb99ff2a11ac392948a615ac687", "files": {"z_145eef247bfb46b6___init___py": {"hash": "a46d56e69b4028fd9dba6ce1d02ddbd1", "index": {"url": "z_145eef247bfb46b6___init___py.html", "file": "src\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fbf9fece6c68d2c1___init___py": {"hash": "fb14e6a9fad55018cc3022edc328e07e", "index": {"url": "z_fbf9fece6c68d2c1___init___py.html", "file": "src\\agent_event_processor\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_20164841b8185614___init___py": {"hash": "5d854038c6905e9d74d2eb7a585b4ca1", "index": {"url": "z_20164841b8185614___init___py.html", "file": "src\\agent_event_processor\\config\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_20164841b8185614_settings_py": {"hash": "5b2d67a495927a33c7b7809ab3fba802", "index": {"url": "z_20164841b8185614_settings_py.html", "file": "src\\agent_event_processor\\config\\settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fbf9fece6c68d2c1_lambda_function_py": {"hash": "6fe6c1261f387c0be7b58688a143193c", "index": {"url": "z_fbf9fece6c68d2c1_lambda_function_py.html", "file": "src\\agent_event_processor\\lambda_function.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 71, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c15db3289e0f0aa___init___py": {"hash": "85d1db9f2c8ccb94f7fa9cd51c338073", "index": {"url": "z_5c15db3289e0f0aa___init___py.html", "file": "src\\agent_event_processor\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c15db3289e0f0aa_events_py": {"hash": "3ed99c6eee15f281e16c0acc22af1d69", "index": {"url": "z_5c15db3289e0f0aa_events_py.html", "file": "src\\agent_event_processor\\models\\events.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 51, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56c05ffb73869942___init___py": {"hash": "f13ee4e27f5c02d9967fc21b58afe4dd", "index": {"url": "z_56c05ffb73869942___init___py.html", "file": "src\\agent_event_processor\\services\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56c05ffb73869942_database_repository_py": {"hash": "95363e1a129c9aa7d900e5f36ba7e965", "index": {"url": "z_56c05ffb73869942_database_repository_py.html", "file": "src\\agent_event_processor\\services\\database_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56c05ffb73869942_database_service_py": {"hash": "4c78a8494a126852b50f72b6ea597d34", "index": {"url": "z_56c05ffb73869942_database_service_py.html", "file": "src\\agent_event_processor\\services\\database_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 342, "n_excluded": 0, "n_missing": 187, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56c05ffb73869942_event_processor_py": {"hash": "5bde9db7c02813a90443905f0ef6c0b2", "index": {"url": "z_56c05ffb73869942_event_processor_py.html", "file": "src\\agent_event_processor\\services\\event_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 67, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_09c0692e822942fe___init___py": {"hash": "805bd7868c6f2417640b4743a550fad3", "index": {"url": "z_09c0692e822942fe___init___py.html", "file": "src\\agent_event_processor\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_09c0692e822942fe_decorators_py": {"hash": "f272420b16d5b33b60cbd59d42f8dc6b", "index": {"url": "z_09c0692e822942fe_decorators_py.html", "file": "src\\agent_event_processor\\utils\\decorators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_09c0692e822942fe_timezone_utils_py": {"hash": "e854dd3863e516459c28617513ba5057", "index": {"url": "z_09c0692e822942fe_timezone_utils_py.html", "file": "src\\agent_event_processor\\utils\\timezone_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_09c0692e822942fe_xml_parser_py": {"hash": "fe1ca55701525d5ccdda6777e814f40a", "index": {"url": "z_09c0692e822942fe_xml_parser_py.html", "file": "src\\agent_event_processor\\utils\\xml_parser.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 41, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}