{".class": "MypyFile", "_fullname": "_pytest.logging", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractSet": {".class": "SymbolTableNode", "cross_ref": "typing.AbstractSet", "kind": "Gdef"}, "CaptureManager": {".class": "SymbolTableNode", "cross_ref": "_pytest.capture.CaptureManager", "kind": "Gdef"}, "ColoredLevelFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.logging.DatetimeFormatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.logging.ColoredLevelFormatter", "name": "ColoredLevelFormatter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.logging.ColoredLevelFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.logging", "mro": ["_pytest.logging.ColoredLevelFormatter", "_pytest.logging.DatetimeFormatter", "logging.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "LEVELNAME_FMT_REGEX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.logging.ColoredLevelFormatter.LEVELNAME_FMT_REGEX", "name": "LEVELNAME_FMT_REGEX", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "LOGLEVEL_COLOROPTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "_pytest.logging.ColoredLevelFormatter.LOGLEVEL_COLOROPTS", "name": "LOGLEVEL_COLOROPTS", "type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.AbstractSet"}], "type_ref": "typing.Mapping"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "terminalwriter", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.ColoredLevelFormatter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "terminalwriter", "args", "kwargs"], "arg_types": ["_pytest.logging.ColoredLevelFormatter", "_pytest._io.terminalwriter.TerminalWriter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ColoredLevelFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_level_to_fmt_mapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.logging.ColoredLevelFormatter._level_to_fmt_mapping", "name": "_level_to_fmt_mapping", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "type_ref": "builtins.dict"}}}, "_original_fmt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.ColoredLevelFormatter._original_fmt", "name": "_original_fmt", "type": "builtins.str"}}, "_terminalwriter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.ColoredLevelFormatter._terminalwriter", "name": "_terminalwriter", "type": "_pytest._io.terminalwriter.TerminalWriter"}}, "add_color_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "level", "color_opts"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.ColoredLevelFormatter.add_color_level", "name": "add_color_level", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "level", "color_opts"], "arg_types": ["_pytest.logging.ColoredLevelFormatter", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_color_level of ColoredLevelFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.ColoredLevelFormatter.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record"], "arg_types": ["_pytest.logging.ColoredLevelFormatter", "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of ColoredLevelFormatter", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging.ColoredLevelFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.logging.ColoredLevelFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Config": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.Config", "kind": "Gdef"}, "DEFAULT_LOG_DATE_FORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "_pytest.logging.DEFAULT_LOG_DATE_FORMAT", "name": "DEFAULT_LOG_DATE_FORMAT", "type": "builtins.str"}}, "DEFAULT_LOG_FORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "_pytest.logging.DEFAULT_LOG_FORMAT", "name": "DEFAULT_LOG_FORMAT", "type": "builtins.str"}}, "DatetimeFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["logging.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.logging.DatetimeFormatter", "name": "DatetimeFormatter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.logging.DatetimeFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.logging", "mro": ["_pytest.logging.DatetimeFormatter", "logging.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "formatTime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "record", "datefmt"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.DatetimeFormatter.formatTime", "name": "formatTime", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "record", "datefmt"], "arg_types": ["_pytest.logging.DatetimeFormatter", "logging.LogRecord", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatTime of DatetimeFormatter", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging.DatetimeFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.logging.DatetimeFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FixtureRequest": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures.FixtureRequest", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "LogCaptureFixture": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.logging.LogCaptureFixture", "name": "LogCaptureFixture", "type_vars": []}, "deletable_attributes": [], "flags": ["is_final"], "fullname": "_pytest.logging.LogCaptureFixture", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.logging", "mro": ["_pytest.logging.LogCaptureFixture", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "item", "_ispytest"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LogCaptureFixture.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "item", "_ispytest"], "arg_types": ["_pytest.logging.LogCaptureFixture", "_pytest.nodes.Node", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LogCaptureFixture", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LogCaptureFixture._finalize", "name": "_finalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureFixture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_finalize of LogCaptureFixture", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_force_enable_logging": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "level", "logger_obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LogCaptureFixture._force_enable_logging", "name": "_force_enable_logging", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "level", "logger_obj"], "arg_types": ["_pytest.logging.LogCaptureFixture", {".class": "UnionType", "items": ["builtins.int", "builtins.str"]}, "logging.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_force_enable_logging of LogCaptureFixture", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_initial_disabled_logging_level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LogCaptureFixture._initial_disabled_logging_level", "name": "_initial_disabled_logging_level", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "_initial_handler_level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LogCaptureFixture._initial_handler_level", "name": "_initial_handler_level", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "_initial_logger_levels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LogCaptureFixture._initial_logger_levels", "name": "_initial_logger_levels", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.int"], "type_ref": "builtins.dict"}}}, "_item": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.LogCaptureFixture._item", "name": "_item", "type": "_pytest.nodes.Node"}}, "at_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "level", "logger"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.logging.LogCaptureFixture.at_level", "name": "at_level", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "level", "logger"], "arg_types": ["_pytest.logging.LogCaptureFixture", {".class": "UnionType", "items": ["builtins.int", "builtins.str"]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "at_level of LogCaptureFixture", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LogCaptureFixture.at_level", "name": "at_level", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "level", "logger"], "arg_types": ["_pytest.logging.LogCaptureFixture", {".class": "UnionType", "items": ["builtins.int", "builtins.str"]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "at_level of LogCaptureFixture", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LogCaptureFixture.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureFixture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of LogCaptureFixture", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_records": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "when"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LogCaptureFixture.get_records", "name": "get_records", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "when"], "arg_types": ["_pytest.logging.LogCaptureFixture", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "setup"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "call"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "teardown"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_records of LogCaptureFixture", "ret_type": {".class": "Instance", "args": ["logging.LogRecord"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.logging.LogCaptureFixture.handler", "name": "handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureFixture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handler of LogCaptureFixture", "ret_type": "_pytest.logging.LogCaptureHandler", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LogCaptureFixture.handler", "name": "handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureFixture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handler of LogCaptureFixture", "ret_type": "_pytest.logging.LogCaptureHandler", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.logging.LogCaptureFixture.messages", "name": "messages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureFixture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "messages of LogCaptureFixture", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LogCaptureFixture.messages", "name": "messages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureFixture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "messages of LogCaptureFixture", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "record_tuples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.logging.LogCaptureFixture.record_tuples", "name": "record_tuples", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureFixture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "record_tuples of LogCaptureFixture", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LogCaptureFixture.record_tuples", "name": "record_tuples", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureFixture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "record_tuples of LogCaptureFixture", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "records": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.logging.LogCaptureFixture.records", "name": "records", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureFixture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "records of LogCaptureFixture", "ret_type": {".class": "Instance", "args": ["logging.LogRecord"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LogCaptureFixture.records", "name": "records", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureFixture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "records of LogCaptureFixture", "ret_type": {".class": "Instance", "args": ["logging.LogRecord"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "set_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "level", "logger"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LogCaptureFixture.set_level", "name": "set_level", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "level", "logger"], "arg_types": ["_pytest.logging.LogCaptureFixture", {".class": "UnionType", "items": ["builtins.int", "builtins.str"]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_level of LogCaptureFixture", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.logging.LogCaptureFixture.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureFixture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "text of LogCaptureFixture", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LogCaptureFixture.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureFixture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "text of LogCaptureFixture", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging.LogCaptureFixture.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.logging.LogCaptureFixture", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LogCaptureHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["io.StringIO"], "type_ref": "logging.StreamHandler"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.logging.LogCaptureHandler", "name": "LogCaptureHandler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.logging.LogCaptureHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.logging", "mro": ["_pytest.logging.LogCaptureHandler", "logging.StreamHandler", "logging.Handler", "<PERSON>.Filterer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LogCaptureHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LogCaptureHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LogCaptureHandler.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of LogCaptureHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "emit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LogCaptureHandler.emit", "name": "emit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record"], "arg_types": ["_pytest.logging.LogCaptureHandler", "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "emit of LogCaptureHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "handleError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LogCaptureHandler.handleError", "name": "handleError", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record"], "arg_types": ["_pytest.logging.LogCaptureHandler", "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handleError of LogCaptureHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "records": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LogCaptureHandler.records", "name": "records", "type": {".class": "Instance", "args": ["logging.LogRecord"], "type_ref": "builtins.list"}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LogCaptureHandler.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LogCaptureHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of LogCaptureHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging.LogCaptureHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.logging.LogCaptureHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LogRecord": {".class": "SymbolTableNode", "cross_ref": "logging.LogRecord", "kind": "Gdef"}, "LoggingPlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.logging.LoggingPlugin", "name": "LoggingPlugin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.logging.LoggingPlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.logging", "mro": ["_pytest.logging.LoggingPlugin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LoggingPlugin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["_pytest.logging.LoggingPlugin", "_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LoggingPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.LoggingPlugin._config", "name": "_config", "type": "_pytest.config.Config"}}, "_create_formatter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "log_format", "log_date_format", "auto_indent"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LoggingPlugin._create_formatter", "name": "_create_formatter", "type": null}}, "_disable_loggers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "loggers_to_disable"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LoggingPlugin._disable_loggers", "name": "_disable_loggers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "loggers_to_disable"], "arg_types": ["_pytest.logging.LoggingPlugin", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_disable_loggers of LoggingPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_log_cli_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LoggingPlugin._log_cli_enabled", "name": "_log_cli_enabled", "type": null}}, "_runtest_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "item", "when"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LoggingPlugin._runtest_for", "name": "_runtest_for", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "item", "when"], "arg_types": ["_pytest.logging.LoggingPlugin", "_pytest.nodes.Item", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_runtest_for of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "caplog_handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.caplog_handler", "name": "caplog_handler", "type": "_pytest.logging.LogCaptureHandler"}}, "formatter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.formatter", "name": "formatter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "log_cli_handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.log_cli_handler", "name": "log_cli_handler", "type": {".class": "UnionType", "items": ["_pytest.logging._LiveLoggingStreamHandler", "_pytest.logging._LiveLoggingNullHandler"]}}}, "log_cli_level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.log_cli_level", "name": "log_cli_level", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "log_file_handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.log_file_handler", "name": "log_file_handler", "type": "_pytest.logging._FileHandler"}}, "log_file_level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.log_file_level", "name": "log_file_level", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "log_level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.log_level", "name": "log_level", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "pytest_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.logging.LoggingPlugin.pytest_collection", "name": "pytest_collection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_collection of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.pytest_collection", "name": "pytest_collection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_collection of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_runtest_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtest_call", "name": "pytest_runtest_call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["_pytest.logging.LoggingPlugin", "_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_call of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtest_call", "name": "pytest_runtest_call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["_pytest.logging.LoggingPlugin", "_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_call of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_runtest_logfinish": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtest_logfinish", "name": "pytest_runtest_logfinish", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_logfinish of LoggingPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtest_logfinish", "name": "pytest_runtest_logfinish", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_logfinish of LoggingPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_runtest_logreport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtest_logreport", "name": "pytest_runtest_logreport", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_logreport of LoggingPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtest_logreport", "name": "pytest_runtest_logreport", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_logreport of LoggingPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_runtest_logstart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtest_logstart", "name": "pytest_runtest_logstart", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_logstart of LoggingPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtest_logstart", "name": "pytest_runtest_logstart", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_logstart of LoggingPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_runtest_setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtest_setup", "name": "pytest_runtest_setup", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["_pytest.logging.LoggingPlugin", "_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_setup of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtest_setup", "name": "pytest_runtest_setup", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["_pytest.logging.LoggingPlugin", "_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_setup of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_runtest_teardown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtest_teardown", "name": "pytest_runtest_teardown", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["_pytest.logging.LoggingPlugin", "_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_teardown of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtest_teardown", "name": "pytest_runtest_teardown", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["_pytest.logging.LoggingPlugin", "_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_teardown of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_runtestloop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "flags": ["is_generator", "is_decorated"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtestloop", "name": "pytest_runtestloop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "session"], "arg_types": ["_pytest.logging.LoggingPlugin", "_pytest.main.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtestloop of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.pytest_runtestloop", "name": "pytest_runtestloop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "session"], "arg_types": ["_pytest.logging.LoggingPlugin", "_pytest.main.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtestloop of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_sessionfinish": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.logging.LoggingPlugin.pytest_sessionfinish", "name": "pytest_sessionfinish", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_sessionfinish of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.pytest_sessionfinish", "name": "pytest_sessionfinish", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_sessionfinish of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_sessionstart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.logging.LoggingPlugin.pytest_sessionstart", "name": "pytest_sessionstart", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_sessionstart of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.pytest_sessionstart", "name": "pytest_sessionstart", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_sessionstart of LoggingPlugin", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_unconfigure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.logging.LoggingPlugin.pytest_unconfigure", "name": "pytest_unconfigure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_unconfigure of LoggingPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.pytest_unconfigure", "name": "pytest_unconfigure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging.LoggingPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_unconfigure of LoggingPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "report_handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.LoggingPlugin.report_handler", "name": "report_handler", "type": "_pytest.logging.LogCaptureHandler"}}, "set_log_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fname"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.LoggingPlugin.set_log_path", "name": "set_log_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fname"], "arg_types": ["_pytest.logging.LoggingPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_log_path of LoggingPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging.LoggingPlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.logging.LoggingPlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Parser": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.argparsing.Parser", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PercentStyleMultiline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["logging.PercentStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.logging.PercentStyleMultiline", "name": "PercentStyleMultiline", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.logging.PercentStyleMultiline", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.logging", "mro": ["_pytest.logging.PercentStyleMultiline", "logging.PercentStyle", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fmt", "auto_indent"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.PercentStyleMultiline.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fmt", "auto_indent"], "arg_types": ["_pytest.logging.PercentStyleMultiline", "builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str", "builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PercentStyleMultiline", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_auto_indent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.PercentStyleMultiline._auto_indent", "name": "_auto_indent", "type": "builtins.int"}}, "_get_auto_indent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["auto_indent_option"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "_pytest.logging.PercentStyleMultiline._get_auto_indent", "name": "_get_auto_indent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["auto_indent_option"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_auto_indent of PercentStyleMultiline", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "_pytest.logging.PercentStyleMultiline._get_auto_indent", "name": "_get_auto_indent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["auto_indent_option"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", "builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_auto_indent of PercentStyleMultiline", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.PercentStyleMultiline.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record"], "arg_types": ["_pytest.logging.PercentStyleMultiline", "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of PercentStyleMultiline", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging.PercentStyleMultiline.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.logging.PercentStyleMultiline", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Session": {".class": "SymbolTableNode", "cross_ref": "_pytest.main.Session", "kind": "Gdef"}, "StashKey": {".class": "SymbolTableNode", "cross_ref": "_pytest.stash.StashKey", "kind": "Gdef"}, "StringIO": {".class": "SymbolTableNode", "cross_ref": "io.StringIO", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TerminalReporter": {".class": "SymbolTableNode", "cross_ref": "_pytest.terminal.TerminalReporter", "kind": "Gdef"}, "TerminalWriter": {".class": "SymbolTableNode", "cross_ref": "_pytest._io.terminalwriter.TerminalWriter", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UsageError": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.exceptions.UsageError", "kind": "Gdef"}, "_ANSI_ESCAPE_SEQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.logging._ANSI_ESCAPE_SEQ", "name": "_ANSI_ESCAPE_SEQ", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "_FileHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["logging.FileHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.logging._FileHandler", "name": "_File<PERSON><PERSON>ler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.logging._FileHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.logging", "mro": ["_pytest.logging._FileHandler", "logging.FileHandler", "logging.StreamHandler", "logging.Handler", "<PERSON>.Filterer", "builtins.object"], "names": {".class": "SymbolTable", "handleError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging._FileHandler.handleError", "name": "handleError", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record"], "arg_types": ["_pytest.logging._FileHandler", "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handleError of _FileHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging._FileHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.logging._FileHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_HandlerType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging._HandlerType", "name": "_HandlerType", "upper_bound": "logging.Handler", "values": [], "variance": 0}}, "_LiveLoggingNullHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["logging.NullHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.logging._LiveLoggingNullHandler", "name": "_LiveLoggingNullHandler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.logging._LiveLoggingNullHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.logging", "mro": ["_pytest.logging._LiveLoggingNullHandler", "logging.NullHandler", "logging.Handler", "<PERSON>.Filterer", "builtins.object"], "names": {".class": "SymbolTable", "handleError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging._LiveLoggingNullHandler.handleError", "name": "handleError", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record"], "arg_types": ["_pytest.logging._LiveLoggingNullHandler", "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handleError of _LiveLoggingNullHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging._LiveLoggingNullHandler.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging._LiveLoggingNullHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of _LiveLoggingNullHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_when": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "when"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging._LiveLoggingNullHandler.set_when", "name": "set_when", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "when"], "arg_types": ["_pytest.logging._LiveLoggingNullHandler", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_when of _LiveLoggingNullHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging._LiveLoggingNullHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.logging._LiveLoggingNullHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_LiveLoggingStreamHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["io.StringIO"], "type_ref": "logging.StreamHandler"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.logging._LiveLoggingStreamHandler", "name": "_LiveLoggingStreamHandler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.logging._LiveLoggingStreamHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.logging", "mro": ["_pytest.logging._LiveLoggingStreamHandler", "logging.StreamHandler", "logging.Handler", "<PERSON>.Filterer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "terminal_reporter", "capture_manager"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging._LiveLoggingStreamHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "terminal_reporter", "capture_manager"], "arg_types": ["_pytest.logging._LiveLoggingStreamHandler", "_pytest.terminal.TerminalReporter", {".class": "UnionType", "items": ["_pytest.capture.CaptureManager", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _LiveLoggingStreamHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_first_record_emitted": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging._LiveLoggingStreamHandler._first_record_emitted", "name": "_first_record_emitted", "type": "builtins.bool"}}, "_section_name_shown": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging._LiveLoggingStreamHandler._section_name_shown", "name": "_section_name_shown", "type": "builtins.bool"}}, "_test_outcome_written": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging._LiveLoggingStreamHandler._test_outcome_written", "name": "_test_outcome_written", "type": "builtins.bool"}}, "_when": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging._LiveLoggingStreamHandler._when", "name": "_when", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "capture_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging._LiveLoggingStreamHandler.capture_manager", "name": "capture_manager", "type": {".class": "UnionType", "items": ["_pytest.capture.CaptureManager", {".class": "NoneType"}]}}}, "emit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging._LiveLoggingStreamHandler.emit", "name": "emit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record"], "arg_types": ["_pytest.logging._LiveLoggingStreamHandler", "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "emit of _LiveLoggingStreamHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "handleError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging._LiveLoggingStreamHandler.handleError", "name": "handleError", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record"], "arg_types": ["_pytest.logging._LiveLoggingStreamHandler", "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handleError of _LiveLoggingStreamHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging._LiveLoggingStreamHandler.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.logging._LiveLoggingStreamHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of _LiveLoggingStreamHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_when": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "when"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging._LiveLoggingStreamHandler.set_when", "name": "set_when", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "when"], "arg_types": ["_pytest.logging._LiveLoggingStreamHandler", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_when of _LiveLoggingStreamHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "_pytest.logging._LiveLoggingStreamHandler.stream", "name": "stream", "type": "_pytest.terminal.TerminalReporter"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging._LiveLoggingStreamHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.logging._LiveLoggingStreamHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.logging.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.logging.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.logging.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.logging.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.logging.__package__", "name": "__package__", "type": "builtins.str"}}, "_remove_ansi_escape_sequences": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging._remove_ansi_escape_sequences", "name": "_remove_ansi_escape_sequences", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_ansi_escape_sequences", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_strtobool": {".class": "SymbolTableNode", "cross_ref": "_pytest.config._strtobool", "kind": "Gdef"}, "caplog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["request"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.logging.caplog", "name": "caplog", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "caplog", "ret_type": {".class": "Instance", "args": ["_pytest.logging.LogCaptureFixture", {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.logging.caplog", "name": "caplog", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "caplog", "ret_type": {".class": "Instance", "args": ["_pytest.logging.LogCaptureFixture", {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "caplog_handler_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.logging.caplog_handler_key", "name": "caplog_handler_key", "type": {".class": "Instance", "args": ["_pytest.logging.LogCaptureHandler"], "type_ref": "_pytest.stash.StashKey"}}}, "caplog_records_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.logging.caplog_records_key", "name": "caplog_records_key", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["logging.LogRecord"], "type_ref": "builtins.list"}], "type_ref": "builtins.dict"}], "type_ref": "_pytest.stash.StashKey"}}}, "catching_logs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.logging.catching_logs", "name": "catching_logs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.logging.catching_logs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.logging", "mro": ["_pytest.logging.catching_logs", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.catching_logs.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.catching_logs.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "handler", "level"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.catching_logs.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "handler", "level"], "arg_types": ["_pytest.logging.catching_logs", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging._HandlerType", "id": -1, "name": "_HandlerType", "namespace": "", "upper_bound": "logging.Handler", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of catching_logs", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging._HandlerType", "id": -1, "name": "_HandlerType", "namespace": "", "upper_bound": "logging.Handler", "values": [], "variance": 0}]}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_pytest.logging.catching_logs.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.catching_logs.handler", "name": "handler", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging._HandlerType", "id": -1, "name": "_HandlerType", "namespace": "", "upper_bound": "logging.Handler", "values": [], "variance": 0}}}, "level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.catching_logs.level", "name": "level", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "orig_level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.logging.catching_logs.orig_level", "name": "orig_level", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.logging.catching_logs.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.logging.catching_logs", "values": [], "variance": 0}, "slots": ["handler", "level", "orig_level"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "check_ispytest": {".class": "SymbolTableNode", "cross_ref": "_pytest.deprecated.check_ispytest", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "create_terminal_writer": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.create_terminal_writer", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef"}, "fixture": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures.fixture", "kind": "Gdef"}, "get_log_level_for_setting": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["config", "setting_names"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.get_log_level_for_setting", "name": "get_log_level_for_setting", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["config", "setting_names"], "arg_types": ["_pytest.config.Config", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_log_level_for_setting", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_option_ini": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["config", "names"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.get_option_ini", "name": "get_option_ini", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["config", "names"], "arg_types": ["_pytest.config.Config", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_option_ini", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "hookimpl": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.hookimpl", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "logging_StreamHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "_pytest.logging.logging_StreamHandler", "line": 43, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["io.StringIO"], "type_ref": "logging.StreamHandler"}}}, "nodes": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes", "kind": "Gdef"}, "nullcontext": {".class": "SymbolTableNode", "cross_ref": "contextlib.nullcontext", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pytest_addoption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["parser"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.logging.pytest_addoption", "name": "pytest_addoption", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["parser"], "arg_types": ["_pytest.config.argparsing.Parser"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_addoption", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pytest_configure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.logging.pytest_configure", "name": "pytest_configure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_configure", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.logging.pytest_configure", "name": "pytest_configure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_configure", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "timezone": {".class": "SymbolTableNode", "cross_ref": "datetime.timezone", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\logging.py"}