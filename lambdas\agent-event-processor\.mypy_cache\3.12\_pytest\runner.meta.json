{"data_mtime": 1757363959, "dep_lines": [23, 27, 18, 22, 26, 28, 29, 32, 43, 44, 2, 3, 4, 5, 6, 22, 41, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 25, 25, 10, 10, 10, 10, 5, 20, 25, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.reports", "_pytest.timing", "_pytest.compat", "_pytest.deprecated", "_pytest.nodes", "_pytest.outcomes", "_pytest.main", "_pytest.terminal", "bdb", "dataclasses", "os", "sys", "typing", "_pytest", "typing_extensions", "builtins", "_pytest._code", "_pytest.config", "_pytest.hookspec", "_pytest.mark", "_typeshed", "abc"], "hash": "ec10f69be461a5fe5bd83953dded6ebd9516a941ad944e800c17dfea7db5b0ee", "id": "_pytest.runner", "ignore_all": true, "interface_hash": "7c6476c95a85a8cc51ac333b92d24f9a0c3bc57d4f4a32d372cb29f787e56045", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\runner.py", "plugin_data": null, "size": 18447, "suppressed": [], "version_id": "1.8.0"}