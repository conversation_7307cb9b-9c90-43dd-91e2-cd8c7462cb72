{"data_mtime": 1757356835, "dep_lines": [45, 46, 14, 19, 29, 31, 32, 33, 49, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25, 23, 177, 151], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 5, 10, 10, 10, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 10, 5], "dependencies": ["urllib3.util.response", "urllib3.util.retry", "http.client", "urllib3._base_connection", "urllib3.util", "urllib3._collections", "urllib3.connection", "urllib3.exceptions", "urllib3.connectionpool", "__future__", "collections", "io", "json", "logging", "re", "socket", "sys", "typing", "warnings", "zlib", "contextlib", "urllib3", "builtins", "pyexpat.errors", "pyexpat.model", "_collections_abc", "_typeshed", "abc", "email", "email.message", "http", "typing_extensions", "urllib3._request_methods"], "hash": "4d54d2bba43553453b8681d8308471c6e878ceba1e328f1be442380ce035dc4a", "id": "urllib3.response", "ignore_all": true, "interface_hash": "b855952a9b2332e46426562b5838e6521296ff994527d10901b73561a4ac5d05", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\response.py", "plugin_data": null, "size": 46480, "suppressed": ["brotli", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zstandard", "compression"], "version_id": "1.8.0"}