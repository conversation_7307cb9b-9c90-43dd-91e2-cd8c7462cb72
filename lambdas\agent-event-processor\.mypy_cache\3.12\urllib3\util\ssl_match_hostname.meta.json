{"data_mtime": 1757356835, "dep_lines": [15, 7, 9, 10, 11, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 10, 10, 5, 20, 20, 30], "dependencies": ["urllib3.util.ssl_", "__future__", "ipaddress", "re", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "abc"], "hash": "0e2ec353bce892896d6a94ff1744a3db57df631c1a4bf704e5aa4eb70b9e7b20", "id": "urllib3.util.ssl_match_hostname", "ignore_all": true, "interface_hash": "bbe0686bd19f9b924f714fc09d27d3e31dc298e2e59d4539f6dd56fc6aa68f4f", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py", "plugin_data": null, "size": 5845, "suppressed": [], "version_id": "1.8.0"}