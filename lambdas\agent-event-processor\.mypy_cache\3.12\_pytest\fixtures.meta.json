{"data_mtime": 1757363959, "dep_lines": [35, 54, 59, 33, 34, 37, 38, 52, 55, 57, 60, 63, 65, 67, 74, 75, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 32, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.mark.structures", "_pytest.nodes", "_pytest._code", "_pytest._io", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.mark", "_pytest.outcomes", "_pytest.pathlib", "_pytest.scope", "_pytest.stash", "_pytest.main", "_pytest.python", "dataclasses", "functools", "inspect", "os", "sys", "warnings", "collections", "contextlib", "pathlib", "types", "typing", "_pytest", "builtins", "_collections_abc", "_pytest._io.terminalwriter", "_pytest.hookspec", "abc", "enum", "pluggy", "pluggy._manager", "typing_extensions"], "hash": "29d5365c47549b455d98df7377da0cf150a49f3818607f284abc17bd30fa18fb", "id": "_pytest.fixtures", "ignore_all": true, "interface_hash": "49dfb5aa67a37a6eabb7690040ab015680b6bc5dcc43f38ec0a2dc1c3d6471d2", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\fixtures.py", "plugin_data": null, "size": 67085, "suppressed": [], "version_id": "1.8.0"}