{"data_mtime": 1757356838, "dep_lines": [18, 19, 20, 21, 22, 7, 14, 17, 25, 26, 3, 5, 6, 8, 9, 10, 11, 12, 14, 15, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 20, 25, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._internal_dataclass", "pydantic._internal._namespace_utils", "pydantic._internal._typing_extra", "pydantic._internal._utils", "collections.abc", "pydantic_core.core_schema", "pydantic.errors", "pydantic.fields", "pydantic.functional_validators", "__future__", "types", "collections", "dataclasses", "functools", "inspect", "itertools", "typing", "pydantic_core", "typing_extensions", "builtins", "pyexpat.errors", "pyexpat.model", "_collections_abc", "_typeshed", "abc", "pydantic_core._pydantic_core"], "hash": "352ed2290bed0e09ec01ddfb9a3aadc0f875f6d779ec527af4bb1c78ee52cb02", "id": "pydantic._internal._decorators", "ignore_all": true, "interface_hash": "ad85311cd49491b9a7f82ce3ab585268cfc14f827b50a9a1f08aa7f9b8d51323", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_decorators.py", "plugin_data": null, "size": 32638, "suppressed": [], "version_id": "1.8.0"}