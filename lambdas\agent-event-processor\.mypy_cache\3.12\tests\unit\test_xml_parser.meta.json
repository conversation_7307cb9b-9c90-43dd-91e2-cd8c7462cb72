{"data_mtime": 1757363959, "dep_lines": [9, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.agent_event_processor.utils.xml_parser", "pytest", "builtins", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.python_api", "abc", "contextlib", "re", "src", "src.agent_event_processor", "typing", "typing_extensions"], "hash": "1091d3aa0c6757212e0b863d177b811ccecedafa8d055340526b9370d788f544", "id": "tests.unit.test_xml_parser", "ignore_all": false, "interface_hash": "83e2f0ae5d2c363d44c246fd40274c921221dadf99e2425ff136b2add038ab8d", "mtime": 1757363441, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "tests\\unit\\test_xml_parser.py", "plugin_data": null, "size": 10346, "suppressed": [], "version_id": "1.8.0"}