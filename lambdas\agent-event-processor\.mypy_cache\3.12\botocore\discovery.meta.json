{"data_mtime": 1757356839, "dep_lines": [7, 11, 12, 15, 16, 18, 8, 9, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["collections.abc", "botocore.client", "botocore.exceptions", "botocore.hooks", "botocore.model", "botocore.utils", "logging", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "_random", "abc"], "hash": "b31b36bdc8d7a1494ec5197871db1a31dd7caf3e3ab3ef9795c1aca4bc78e261", "id": "botocore.discovery", "ignore_all": true, "interface_hash": "a88461cbbe082198efff5170f2a14e17b480697d1bdcf532dde5c0ea13945458", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\discovery.pyi", "plugin_data": null, "size": 2626, "suppressed": [], "version_id": "1.8.0"}