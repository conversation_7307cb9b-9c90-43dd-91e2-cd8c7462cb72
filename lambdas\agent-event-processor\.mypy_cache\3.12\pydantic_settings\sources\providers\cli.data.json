{".class": "MypyFile", "_fullname": "pydantic_settings.sources.providers.cli", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing.Annotated", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ArgumentParser": {".class": "SymbolTableNode", "cross_ref": "argparse.ArgumentParser", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "BaseSettings": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.main.BaseSettings", "kind": "Gdef"}, "BooleanOptionalAction": {".class": "SymbolTableNode", "cross_ref": "argparse.BooleanOptionalAction", "kind": "Gdef"}, "CLI_SUPPRESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic_settings.sources.providers.cli.CLI_SUPPRESS", "name": "CLI_SUPPRESS", "type": {".class": "UnionType", "items": ["argparse._SUPPRESS_T", "builtins.str"]}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CliExplicitFlag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli._CliBoolFlag", "id": 1, "name": "_CliBoolFlag", "namespace": "pydantic_settings.sources.providers.cli.CliExplicitFlag", "upper_bound": "builtins.bool", "values": [], "variance": 0}], "column": 0, "fullname": "pydantic_settings.sources.providers.cli.CliExplicitFlag", "line": 86, "no_args": false, "normalized": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli._CliBoolFlag", "id": 1, "name": "_CliBoolFlag", "namespace": "pydantic_settings.sources.providers.cli.CliExplicitFlag", "upper_bound": "builtins.bool", "values": [], "variance": 0}}}, "CliImplicitFlag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli._CliBoolFlag", "id": 1, "name": "_CliBoolFlag", "namespace": "pydantic_settings.sources.providers.cli.CliImplicitFlag", "upper_bound": "builtins.bool", "values": [], "variance": 0}], "column": 0, "fullname": "pydantic_settings.sources.providers.cli.CliImplicitFlag", "line": 85, "no_args": false, "normalized": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli._CliBoolFlag", "id": 1, "name": "_CliBoolFlag", "namespace": "pydantic_settings.sources.providers.cli.CliImplicitFlag", "upper_bound": "builtins.bool", "values": [], "variance": 0}}}, "CliMutuallyExclusiveGroup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.providers.cli.CliMutuallyExclusiveGroup", "name": "CliMutuallyExclusiveGroup", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliMutuallyExclusiveGroup", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 204, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 207, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 210, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic_settings.sources.providers.cli", "mro": ["pydantic_settings.sources.providers.cli.CliMutuallyExclusiveGroup", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic_settings.sources.providers.cli.CliMutuallyExclusiveGroup.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliMutuallyExclusiveGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_settings.sources.providers.cli.CliMutuallyExclusiveGroup"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CliMutuallyExclusiveGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic_settings.sources.providers.cli.CliMutuallyExclusiveGroup.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic_settings.sources.providers.cli.CliMutuallyExclusiveGroup.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CliMutuallyExclusiveGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic_settings.sources.providers.cli.CliMutuallyExclusiveGroup.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CliMutuallyExclusiveGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.CliMutuallyExclusiveGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.providers.cli.CliMutuallyExclusiveGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CliPositionalArg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliPositionalArg", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "pydantic_settings.sources.providers.cli.CliPositionalArg", "line": 83, "no_args": false, "normalized": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliPositionalArg", "upper_bound": "builtins.object", "values": [], "variance": 0}}}, "CliSettingsSource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic_settings.sources.providers.env.EnvSettingsSource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource", "name": "CliSettingsSource", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic_settings.sources.providers.cli", "mro": ["pydantic_settings.sources.providers.cli.CliSettingsSource", "pydantic_settings.sources.providers.env.EnvSettingsSource", "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource", "pydantic_settings.sources.base.PydanticBaseSettingsSource", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.__call__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "args", "parsed_args"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "args", "parsed_args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["argparse.Namespace", "types.SimpleNamespace", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "builtins.str"]}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of CliSettingsSource", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of CliSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of CliSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.bool"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of CliSettingsSource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.bool"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of CliSettingsSource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "parsed_args"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "parsed_args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "UnionType", "items": ["argparse.Namespace", "types.SimpleNamespace", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of CliSettingsSource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "parsed_args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "UnionType", "items": ["argparse.Namespace", "types.SimpleNamespace", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of CliSettingsSource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of CliSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.bool"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of CliSettingsSource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "parsed_args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "UnionType", "items": ["argparse.Namespace", "types.SimpleNamespace", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of CliSettingsSource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "settings_cls", "cli_prog_name", "cli_parse_args", "cli_parse_none_str", "cli_hide_none_type", "cli_avoid_json", "cli_enforce_required", "cli_use_class_docs_for_groups", "cli_exit_on_error", "cli_prefix", "cli_flag_prefix_char", "cli_implicit_flags", "cli_ignore_unknown_args", "cli_kebab_case", "cli_shortcuts", "case_sensitive", "root_parser", "parse_args_method", "add_argument_method", "add_argument_group_method", "add_parser_method", "add_subparsers_method", "formatter_class"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "settings_cls", "cli_prog_name", "cli_parse_args", "cli_parse_none_str", "cli_hide_none_type", "cli_avoid_json", "cli_enforce_required", "cli_use_class_docs_for_groups", "cli_exit_on_error", "cli_prefix", "cli_flag_prefix_char", "cli_implicit_flags", "cli_ignore_unknown_args", "cli_kebab_case", "cli_shortcuts", "case_sensitive", "root_parser", "parse_args_method", "add_argument_method", "add_argument_group_method", "add_parser_method", "add_subparsers_method", "formatter_class"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "TypeType", "item": "pydantic_settings.main.BaseSettings"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}]}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CliSettingsSource", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_add_argument": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._add_argument", "name": "_add_argument", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_add_group": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._add_group", "name": "_add_group", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_add_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._add_parser", "name": "_add_parser", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_add_parser_alias_paths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "parser", "alias_path_args", "added_args", "arg_prefix", "subcommand_prefix", "group"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._add_parser_alias_paths", "name": "_add_parser_alias_paths", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "parser", "alias_path_args", "added_args", "arg_prefix", "subcommand_prefix", "group"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_parser_alias_paths of CliSettingsSource", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_add_parser_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "parser", "model", "added_args", "arg_prefix", "subcommand_prefix", "group", "alias_prefixes", "model_default", "is_model_suppressed"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._add_parser_args", "name": "_add_parser_args", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "parser", "model", "added_args", "arg_prefix", "subcommand_prefix", "group", "alias_prefixes", "model_default", "is_model_suppressed"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_parser_args of CliSettingsSource", "ret_type": "argparse.ArgumentParser", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_add_parser_submodels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "parser", "model", "sub_models", "added_args", "arg_prefix", "subcommand_prefix", "flag_prefix", "arg_names", "kwargs", "field_name", "field_info", "alias_names", "model_default", "is_model_suppressed"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._add_parser_submodels", "name": "_add_parser_submodels", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "parser", "model", "sub_models", "added_args", "arg_prefix", "subcommand_prefix", "flag_prefix", "arg_names", "kwargs", "field_name", "field_info", "alias_names", "model_default", "is_model_suppressed"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "builtins.str", "pydantic.fields.FieldInfo", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_parser_submodels of CliSettingsSource", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_add_subparsers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._add_subparsers", "name": "_add_subparsers", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_check_kebab_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._check_kebab_name", "name": "_check_kebab_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_kebab_name of CliSettingsSource", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_cli_dict_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._cli_dict_args", "name": "_cli_dict_args", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}]}], "type_ref": "builtins.dict"}}}, "_cli_flag_prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._cli_flag_prefix", "name": "_cli_flag_prefix", "type": "builtins.str"}}, "_cli_subcommands": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._cli_subcommands", "name": "_cli_subcommands", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}], "type_ref": "collections.defaultdict"}}}, "_cli_unknown_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._cli_unknown_args", "name": "_cli_unknown_args", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], "type_ref": "builtins.dict"}}}, "_connect_group_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "add_argument_group_method"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._connect_group_method", "name": "_connect_group_method", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "add_argument_group_method"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_connect_group_method of CliSettingsSource", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_connect_parser_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "parser_method", "method_name", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._connect_parser_method", "name": "_connect_parser_method", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "parser_method", "method_name", "args", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_connect_parser_method of CliSettingsSource", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_connect_root_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "root_parser", "parse_args_method", "add_argument_method", "add_argument_group_method", "add_parser_method", "add_subparsers_method", "formatter_class"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._connect_root_parser", "name": "_connect_root_parser", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "root_parser", "parse_args_method", "add_argument_method", "add_argument_group_method", "add_parser_method", "add_subparsers_method", "formatter_class"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_connect_root_parser of CliSettingsSource", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_consume_comma": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "item", "merged_list", "is_last_consumed_a_value"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._consume_comma", "name": "_consume_comma", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "item", "merged_list", "is_last_consumed_a_value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_consume_comma of CliSettingsSource", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_consume_object_or_array": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "item", "merged_list"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._consume_object_or_array", "name": "_consume_object_or_array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "item", "merged_list"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_consume_object_or_array of CliSettingsSource", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_consume_string_or_number": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "item", "merged_list", "merge_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._consume_string_or_number", "name": "_consume_string_or_number", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "item", "merged_list", "merge_type"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_consume_string_or_number of CliSettingsSource", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_convert_append_action": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "kwargs", "field_info", "is_append_action"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._convert_append_action", "name": "_convert_append_action", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "kwargs", "field_info", "is_append_action"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "pydantic.fields.FieldInfo", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_convert_append_action of CliSettingsSource", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_convert_bool_flag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "kwargs", "field_info", "model_default"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._convert_bool_flag", "name": "_convert_bool_flag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "kwargs", "field_info", "model_default"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "pydantic.fields.FieldInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_convert_bool_flag of CliSettingsSource", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_convert_positional_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "kwargs", "field_info", "preferred_alias", "model_default"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._convert_positional_arg", "name": "_convert_positional_arg", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "kwargs", "field_info", "preferred_alias", "model_default"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "pydantic.fields.FieldInfo", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_convert_positional_arg of CliSettingsSource", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_formatter_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._formatter_class", "name": "_formatter_class", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_get_arg_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "arg_prefix", "subcommand_prefix", "alias_prefixes", "alias_names", "added_args"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._get_arg_names", "name": "_get_arg_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "arg_prefix", "subcommand_prefix", "alias_prefixes", "alias_names", "added_args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_arg_names of CliSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_merge_parsed_list_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parsed_list", "field_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._get_merge_parsed_list_types", "name": "_get_merge_parsed_list_types", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parsed_list", "field_name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_merge_parsed_list_types of CliSettingsSource", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_modified_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._get_modified_args", "name": "_get_modified_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_modified_args of CliSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_sub_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "field_name", "field_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._get_sub_models", "name": "_get_sub_models", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "field_name", "field_info"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "builtins.str", "pydantic.fields.FieldInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_sub_models of CliSettingsSource", "ret_type": {".class": "Instance", "args": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_help_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "field_name", "field_info", "model_default", "is_model_suppressed"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._help_format", "name": "_help_format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "field_name", "field_info", "model_default", "is_model_suppressed"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "builtins.str", "pydantic.fields.FieldInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_help_format of CliSettingsSource", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_is_field_suppressed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._is_field_suppressed", "name": "_is_field_suppressed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_info"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "pydantic.fields.FieldInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_field_suppressed of CliSettingsSource", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_load_env_vars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._load_env_vars", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "parsed_args"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._load_env_vars", "name": "_load_env_vars", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "parsed_args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "UnionType", "items": ["argparse.Namespace", "types.SimpleNamespace", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "builtins.str"]}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_env_vars of CliSettingsSource", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._load_env_vars", "name": "_load_env_vars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_env_vars of CliSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "type_ref": "typing.Mapping"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._load_env_vars", "name": "_load_env_vars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_env_vars of CliSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "type_ref": "typing.Mapping"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "parsed_args"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._load_env_vars", "name": "_load_env_vars", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "parsed_args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "UnionType", "items": ["argparse.Namespace", "types.SimpleNamespace", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_env_vars of CliSettingsSource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._load_env_vars", "name": "_load_env_vars", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "parsed_args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "UnionType", "items": ["argparse.Namespace", "types.SimpleNamespace", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_env_vars of CliSettingsSource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_env_vars of CliSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "type_ref": "typing.Mapping"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "parsed_args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "UnionType", "items": ["argparse.Namespace", "types.SimpleNamespace", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_env_vars of CliSettingsSource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "_merge_parsed_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parsed_list", "field_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._merge_parsed_list", "name": "_merge_parsed_list", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parsed_list", "field_name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_merge_parsed_list of CliSettingsSource", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_metavar_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._metavar_format", "name": "_metavar_format", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_metavar_format of CliSettingsSource", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_metavar_format_choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "args", "obj_qualname"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._metavar_format_choices", "name": "_metavar_format_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "args", "obj_qualname"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_metavar_format_choices of CliSettingsSource", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_metavar_format_recurse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._metavar_format_recurse", "name": "_metavar_format_recurse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_metavar_format_recurse of CliSettingsSource", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_parse_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._parse_args", "name": "_parse_args", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_root_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._root_parser", "name": "_root_parser", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}}}, "_sort_arg_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._sort_arg_fields", "name": "_sort_arg_fields", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "model"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sort_arg_fields of CliSettingsSource", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "pydantic.fields.FieldInfo"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_verify_cli_flag_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "field_name", "field_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource._verify_cli_flag_annotations", "name": "_verify_cli_flag_annotations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "field_name", "field_info"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "builtins.str", "pydantic.fields.FieldInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_verify_cli_flag_annotations of CliSettingsSource", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "cli_avoid_json": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_avoid_json", "name": "cli_avoid_json", "type": "builtins.bool"}}, "cli_enforce_required": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_enforce_required", "name": "cli_enforce_required", "type": "builtins.bool"}}, "cli_exit_on_error": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_exit_on_error", "name": "cli_exit_on_error", "type": "builtins.bool"}}, "cli_flag_prefix_char": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_flag_prefix_char", "name": "cli_flag_prefix_char", "type": "builtins.str"}}, "cli_hide_none_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_hide_none_type", "name": "cli_hide_none_type", "type": "builtins.bool"}}, "cli_ignore_unknown_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_ignore_unknown_args", "name": "cli_ignore_unknown_args", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "cli_implicit_flags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_implicit_flags", "name": "cli_implicit_flags", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "cli_kebab_case": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_kebab_case", "name": "cli_kebab_case", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "cli_parse_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_parse_args", "name": "cli_parse_args", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}}}, "cli_parse_none_str": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_parse_none_str", "name": "cli_parse_none_str", "type": "builtins.str"}}, "cli_prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_prefix", "name": "cli_prefix", "type": "builtins.str"}}, "cli_prog_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_prog_name", "name": "cli_prog_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "cli_shortcuts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_shortcuts", "name": "cli_shortcuts", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}]}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}}}, "cli_use_class_docs_for_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.cli_use_class_docs_for_groups", "name": "cli_use_class_docs_for_groups", "type": "builtins.bool"}}, "root_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.root_parser", "name": "root_parser", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_parser of CliSettingsSource", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.root_parser", "name": "root_parser", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_parser of CliSettingsSource", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.CliSettingsSource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSettingsSource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic_settings.sources.providers.cli.CliSettingsSource"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T"], "typeddict_type": null}}, "CliSubCommand": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSubCommand", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "pydantic_settings.sources.providers.cli.CliSubCommand", "line": 82, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSubCommand", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}]}}}, "CliSuppress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSuppress", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "pydantic_settings.sources.providers.cli.CliSuppress", "line": 88, "no_args": false, "normalized": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "id": 1, "name": "T", "namespace": "pydantic_settings.sources.providers.cli.CliSuppress", "upper_bound": "builtins.object", "values": [], "variance": 0}}}, "CliUnknownArgs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_settings.sources.providers.cli.CliUnknownArgs", "line": 89, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "EnvSettingsSource": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.providers.env.EnvSettingsSource", "kind": "Gdef"}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef"}, "FieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.FieldInfo", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Namespace": {".class": "SymbolTableNode", "cross_ref": "argparse.Namespace", "kind": "Gdef"}, "NoDecode": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.types.NoDecode", "kind": "Gdef"}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PydanticUndefined": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefined", "kind": "Gdef"}, "RawDescriptionHelpFormatter": {".class": "SymbolTableNode", "cross_ref": "argparse.RawDescriptionHelpFormatter", "kind": "Gdef"}, "Representation": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._repr.Representation", "kind": "Gdef"}, "SUPPRESS": {".class": "SymbolTableNode", "cross_ref": "argparse.SUPPRESS", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SettingsError": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.exceptions.SettingsError", "kind": "Gdef"}, "SimpleNamespace": {".class": "SymbolTableNode", "cross_ref": "types.SimpleNamespace", "kind": "Gdef"}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_CliBoolFlag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli._CliBoolFlag", "name": "_CliBoolFlag", "upper_bound": "builtins.bool", "values": [], "variance": 0}}, "_CliExplicitFlag": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.types._CliExplicitFlag", "kind": "Gdef"}, "_CliImplicitFlag": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.types._CliImplicitFlag", "kind": "Gdef"}, "_CliInternalArgParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["argparse.ArgumentParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.providers.cli._CliInternalArgParser", "name": "_CliInternalArgParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic_settings.sources.providers.cli._CliInternalArgParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_settings.sources.providers.cli", "mro": ["pydantic_settings.sources.providers.cli._CliInternalArgParser", "argparse.ArgumentParser", "argparse._AttributeHolder", "argparse._ActionsContainer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "cli_exit_on_error", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli._CliInternalArgParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "cli_exit_on_error", "kwargs"], "arg_types": ["pydantic_settings.sources.providers.cli._CliInternalArgParser", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _CliInternalArgParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_cli_exit_on_error": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.providers.cli._CliInternalArgParser._cli_exit_on_error", "name": "_cli_exit_on_error", "type": "builtins.bool"}}, "error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic_settings.sources.providers.cli._CliInternalArgParser.error", "name": "error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["pydantic_settings.sources.providers.cli._CliInternalArgParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "error of _CliInternalArgParser", "ret_type": {".class": "UninhabitedType", "is_noreturn": true}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.providers.cli._CliInternalArgParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.providers.cli._CliInternalArgParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CliPositionalArg": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.types._CliPositionalArg", "kind": "Gdef"}, "_CliSubCommand": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.types._CliSubCommand", "kind": "Gdef"}, "_CliUnknownArgs": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.types._CliUnknownArgs", "kind": "Gdef"}, "_SubParsersAction": {".class": "SymbolTableNode", "cross_ref": "argparse._SubParsersAction", "kind": "Gdef"}, "_WithArgsTypes": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.utils._WithArgsTypes", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.providers.cli.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.providers.cli.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.providers.cli.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.providers.cli.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.providers.cli.__package__", "name": "__package__", "type": "builtins.str"}}, "_annotation_contains_types": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.utils._annotation_contains_types", "kind": "Gdef"}, "_annotation_enum_val_to_name": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.utils._annotation_enum_val_to_name", "kind": "Gdef"}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "_get_alias_names": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.utils._get_alias_names", "kind": "Gdef"}, "_get_model_fields": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.utils._get_model_fields", "kind": "Gdef"}, "_is_function": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.utils._is_function", "kind": "Gdef"}, "_lenient_issubclass": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.utils._lenient_issubclass", "kind": "Gdef"}, "_strip_annotated": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.utils._strip_annotated", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "dedent": {".class": "SymbolTableNode", "cross_ref": "textwrap.dedent", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_args", "kind": "Gdef"}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_origin", "kind": "Gdef"}, "is_model_class": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.is_model_class", "kind": "Gdef"}, "is_pydantic_dataclass": {".class": "SymbolTableNode", "cross_ref": "pydantic.dataclasses.is_pydantic_dataclass", "kind": "Gdef"}, "is_union_origin": {".class": "SymbolTableNode", "cross_ref": "typing_inspection.introspection.is_union_origin", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "parse_env_vars": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.utils.parse_env_vars", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "shlex": {".class": "SymbolTableNode", "cross_ref": "shlex", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef"}, "typing_objects": {".class": "SymbolTableNode", "cross_ref": "typing_inspection.typing_objects", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_settings\\sources\\providers\\cli.py"}