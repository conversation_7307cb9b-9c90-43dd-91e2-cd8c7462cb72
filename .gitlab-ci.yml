variables:
  # Project configuration
  DOCKER_PYTHON_TAG: python:3.12-slim
  PACKAGE_REGISTRY_URL: "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic"
  PACKAGE_NAME: "smartanalytics-processors"

  # Cache configuration
  PIP_CACHE_DIR: "${CI_PROJECT_DIR}/.cache/pip"
  RUFF_CACHE_DIR: "${CI_PROJECT_DIR}/.cache/ruff"
  MYPY_CACHE_DIR: "${CI_PROJECT_DIR}/.cache/mypy"

  # Lambda configuration - Add new lambdas here
  LAMBDA_FUNCTIONS: "agent-event-processor"

  # Quality gates
  COVERAGE_THRESHOLD: "80"

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    - if: $CI_COMMIT_BRANCH
    - if: $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/

default:
  image: $DOCKER_PYTHON_TAG
  cache:
    - key: pip-cache
      paths:
        - ${PIP_CACHE_DIR}
        - ${RUFF_CACHE_DIR}
        - ${MYPY_CACHE_DIR}
    - key:
        files:
          - "**/requirements*.txt"
          - "**/pyproject.toml"
      paths:
        - ${PIP_CACHE_DIR}
        - ${RUFF_CACHE_DIR}
        - ${MYPY_CACHE_DIR}

.before_script_template: &before_script_template
  before_script:
    - apt-get update -qq && apt-get install -y -qq git curl zip unzip jq bc
    - python --version
    - pip install --upgrade pip
    - mkdir -p ${PIP_CACHE_DIR} ${RUFF_CACHE_DIR} ${MYPY_CACHE_DIR}
    # Install global CI dependencies
    - pip install -r requirements-ci.txt

stages:
  - validate
  - test
  - build
  - publish

# Validation stage - check code quality and formatting
validate:
  stage: validate
  <<: *before_script_template
  script:
    - echo "Validating all Lambda functions..."
    - |
      for lambda_func in $LAMBDA_FUNCTIONS; do
        echo "Validating $lambda_func..."
        cd lambdas/$lambda_func

        # Install function-specific dependencies
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

        # Run code quality checks
        echo "Running ruff formatter check..."
        ruff format --check --diff .

        echo "Running ruff linting..."
        ruff check .

        echo "Running flake8 linting..."
        flake8 src/ tests/

        echo "Running mypy type checking..."
        export MYPY_CACHE_DIR=${MYPY_CACHE_DIR}
        mypy src/

        echo "Running bandit security check..."
        bandit -r src/

        cd ../..
      done
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH

# Test stage - run unit and integration tests
.test_template: &test_template
  stage: test
  <<: *before_script_template
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    reports:
      junit: lambdas/${LAMBDA_FUNCTION}/test-results.xml
      coverage_report:
        coverage_format: cobertura
        path: lambdas/${LAMBDA_FUNCTION}/coverage.xml
    paths:
      - lambdas/${LAMBDA_FUNCTION}/htmlcov/
      - lambdas/${LAMBDA_FUNCTION}/test-results.xml
      - lambdas/${LAMBDA_FUNCTION}/coverage.xml
    expire_in: 1 week
  script:
    - echo "Testing $LAMBDA_FUNCTION..."
    - cd lambdas/$LAMBDA_FUNCTION

    # Install dependencies
    - pip install -r requirements.txt
    - pip install -r requirements-dev.txt

    # Run tests with coverage
    - pytest tests/ --junitxml=test-results.xml --cov=src --cov-report=xml --cov-report=html --cov-report=term

    # Check coverage threshold
    - coverage report --fail-under=${COVERAGE_THRESHOLD}

    - cd ../..

test:agent-event-processor:
  <<: *test_template
  variables:
    LAMBDA_FUNCTION: "agent-event-processor"
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH

# Build stage - create Lambda deployment packages
.build_template: &build_template
  stage: build
  <<: *before_script_template
  artifacts:
    paths:
      - "*.zip"
    expire_in: 1 week
  script:
    - echo "Building $LAMBDA_FUNCTION..."
    - cd lambdas/$LAMBDA_FUNCTION
    
    # Create build directory
    - mkdir -p build
    - cd build
    
    # Copy source code
    - cp -r ../src .
    
    # Install global dependencies
    - pip install -r ../requirements.txt -t . --no-deps --upgrade
    
    # Create deployment package
    - zip -r "../../../${LAMBDA_FUNCTION}.zip" . -x "*.pyc" "*/__pycache__/*"
    
    # Verify package
    - cd ../../..
    - ls -la ${LAMBDA_FUNCTION}.zip
    - unzip -l ${LAMBDA_FUNCTION}.zip | head -20

build:agent-event-processor:
  <<: *build_template
  variables:
    LAMBDA_FUNCTION: "agent-event-processor"
  rules:
    - if: $CI_COMMIT_BRANCH
    - if: $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/

# Publish stage - upload packages to GitLab Package Registry
publish:
  stage: publish
  <<: *before_script_template
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/
  script:
    - echo "Publishing Lambda packages..."
    - |
      # Determine version
      if [ -n "$CI_COMMIT_TAG" ]; then
        PACKAGE_VERSION=$CI_COMMIT_TAG
      else
        PACKAGE_VERSION="latest"
      fi

      echo "Publishing version: $PACKAGE_VERSION"

      # Upload each Lambda package
      for lambda_func in $LAMBDA_FUNCTIONS; do
        if [ -f "${lambda_func}.zip" ]; then
          echo "Uploading ${lambda_func}.zip..."
          curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
               --upload-file "${lambda_func}.zip" \
               "${PACKAGE_REGISTRY_URL}/${PACKAGE_NAME}/${PACKAGE_VERSION}/${lambda_func}.zip"
          echo "Successfully uploaded ${lambda_func}.zip"
        else
          echo "Warning: ${lambda_func}.zip not found"
        fi
      done

      echo "All packages published successfully!"

      # Create a manifest file with package information
      cat > package-manifest.json << EOF
      {
        "package_name": "${PACKAGE_NAME}",
        "version": "${PACKAGE_VERSION}",
        "commit": "${CI_COMMIT_SHA}",
        "pipeline_id": "${CI_PIPELINE_ID}",
        "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "functions": [
      EOF

      first=true
      for lambda_func in $LAMBDA_FUNCTIONS; do
        if [ -f "${lambda_func}.zip" ]; then
          if [ "$first" = true ]; then
            first=false
          else
            echo "," >> package-manifest.json
          fi
          echo "      {" >> package-manifest.json
          echo "        \"name\": \"${lambda_func}\"," >> package-manifest.json
          echo "        \"filename\": \"${lambda_func}.zip\"," >> package-manifest.json
          echo "        \"size\": $(stat -c%s "${lambda_func}.zip" 2>/dev/null || stat -f%z "${lambda_func}.zip" 2>/dev/null || echo "0")" >> package-manifest.json
          echo "      }" >> package-manifest.json
        fi
      done

      cat >> package-manifest.json << EOF
        ]
      }
      EOF

      # Upload manifest
      curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
           --upload-file "package-manifest.json" \
           "${PACKAGE_REGISTRY_URL}/${PACKAGE_NAME}/${PACKAGE_VERSION}/package-manifest.json"

      echo "Package manifest uploaded successfully!"
  artifacts:
    reports:
      dotenv: package-info.env
    paths:
      - package-manifest.json
    expire_in: 1 week
  needs:
    - build:agent-event-processor
