{".class": "MypyFile", "_fullname": "tests.unit.test_lambda_function", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "SQSEvent": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_typing.events.sqs.SQSEvent", "kind": "Gdef"}, "TestLambdaHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.unit.test_lambda_function.TestLambdaHandler", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.unit.test_lambda_function", "mro": ["tests.unit.test_lambda_function.TestLambdaHandler", "builtins.object"], "names": {".class": "SymbolTable", "create_sqs_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "xml_content", "message_id"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.create_sqs_event", "name": "create_sqs_event", "type": null}}, "test_acd_login_event_processing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_event_processor", "mock_lambda_context"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_acd_login_event_processing", "name": "test_acd_login_event_processing", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_acd_login_event_processing", "name": "test_acd_login_event_processing", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_acd_login_event_processing of TestLambdaHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "test_batch_processing_multiple_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_event_processor", "mock_lambda_context"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_batch_processing_multiple_events", "name": "test_batch_processing_multiple_events", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_batch_processing_multiple_events", "name": "test_batch_processing_multiple_events", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_batch_processing_multiple_events of TestLambdaHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "test_empty_records": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_event_processor", "mock_lambda_context"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_empty_records", "name": "test_empty_records", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_empty_records", "name": "test_empty_records", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_empty_records of TestLambdaHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "test_invalid_xml_handling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_event_processor", "mock_lambda_context"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_invalid_xml_handling", "name": "test_invalid_xml_handling", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_invalid_xml_handling", "name": "test_invalid_xml_handling", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_invalid_xml_handling of TestLambdaHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "test_partial_batch_failure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_event_processor", "mock_lambda_context"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_partial_batch_failure", "name": "test_partial_batch_failure", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_partial_batch_failure", "name": "test_partial_batch_failure", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_partial_batch_failure of TestLambdaHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "test_sns_wrapped_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_event_processor", "mock_lambda_context"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_sns_wrapped_message", "name": "test_sns_wrapped_message", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_sns_wrapped_message", "name": "test_sns_wrapped_message", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_sns_wrapped_message of TestLambdaHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "test_successful_login_event_processing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_event_processor", "mock_lambda_context"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_successful_login_event_processing", "name": "test_successful_login_event_processing", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.test_successful_login_event_processing", "name": "test_successful_login_event_processing", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_successful_login_event_processing of TestLambdaHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.unit.test_lambda_function.TestLambdaHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.unit.test_lambda_function.TestLambdaHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_lambda_function.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_lambda_function.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_lambda_function.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_lambda_function.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_lambda_function.__package__", "name": "__package__", "type": "builtins.str"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "lambda_handler": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.lambda_function.lambda_handler", "kind": "Gdef"}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}}, "path": "tests\\unit\\test_lambda_function.py"}