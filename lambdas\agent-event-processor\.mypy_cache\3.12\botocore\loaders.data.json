{".class": "MypyFile", "_fullname": "botocore.loaders", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DataNotFoundError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.DataNotFoundError", "kind": "Gdef"}, "ExtrasProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.loaders.ExtrasProcessor", "name": "ExtrasProcessor", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.loaders.ExtrasProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.loaders", "mro": ["botocore.loaders.ExtrasProcessor", "builtins.object"], "names": {".class": "SymbolTable", "process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "original_model", "extra_models"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.loaders.ExtrasProcessor.process", "name": "process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "original_model", "extra_models"], "arg_types": ["botocore.loaders.ExtrasProcessor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process of ExtrasProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.loaders.ExtrasProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.loaders.ExtrasProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HAS_GZIP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.loaders.HAS_GZIP", "name": "HAS_GZIP", "type": "builtins.bool"}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "JSONFileLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.loaders.JSONFileLoader", "name": "JSONFile<PERSON><PERSON>der", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.loaders.JSONFileLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.loaders", "mro": ["botocore.loaders.JSONFileLoader", "builtins.object"], "names": {".class": "SymbolTable", "exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.loaders.JSONFileLoader.exists", "name": "exists", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "arg_types": ["botocore.loaders.JSONFileLoader", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exists of JSONFileLoader", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "load_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.loaders.JSONFileLoader.load_file", "name": "load_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "arg_types": ["botocore.loaders.JSONFileLoader", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_file of JSONFileLoader", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.loaders.JSONFileLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.loaders.JSONFileLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Loader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.loaders.Loader", "name": "Loader", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.loaders.Loader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.loaders", "mro": ["botocore.loaders.Loader", "builtins.object"], "names": {".class": "SymbolTable", "BUILTIN_DATA_PATH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.loaders.Loader.BUILTIN_DATA_PATH", "name": "BUILTIN_DATA_PATH", "type": "builtins.str"}}, "BUILTIN_EXTRAS_TYPES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.loaders.Loader.BUILTIN_EXTRAS_TYPES", "name": "BUILTIN_EXTRAS_TYPES", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "CUSTOMER_DATA_PATH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.loaders.Loader.CUSTOMER_DATA_PATH", "name": "CUSTOMER_DATA_PATH", "type": "builtins.str"}}, "FILE_LOADER_CLASS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.loaders.Loader.FILE_LOADER_CLASS", "name": "FILE_LOADER_CLASS", "type": {".class": "TypeType", "item": "botocore.loaders.JSONFileLoader"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "extra_search_paths", "file_loader", "cache", "include_default_search_paths", "include_default_extras"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.loaders.Loader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "extra_search_paths", "file_loader", "cache", "include_default_search_paths", "include_default_extras"], "arg_types": ["botocore.loaders.Loader", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["botocore.loaders.JSONFileLoader", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Loader", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "determine_latest_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "service_name", "type_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.loaders.Loader.determine_latest_version", "name": "determine_latest_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "service_name", "type_name"], "arg_types": ["botocore.loaders.Loader", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "determine_latest_version of Loader", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "extras_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "botocore.loaders.Loader.extras_types", "name": "extras_types", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.loaders.Loader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extras_types of Loader", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "botocore.loaders.Loader.extras_types", "name": "extras_types", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.loaders.Loader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extras_types of Loader", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "file_loader": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.loaders.Loader.file_loader", "name": "file_loader", "type": "botocore.loaders.JSONFileLoader"}}, "is_builtin_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.loaders.Loader.is_builtin_path", "name": "is_builtin_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["botocore.loaders.Loader", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_builtin_path of Loader", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "list_api_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "service_name", "type_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.loaders.Loader.list_api_versions", "name": "list_api_versions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "service_name", "type_name"], "arg_types": ["botocore.loaders.Loader", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_api_versions of Loader", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "list_available_services": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "type_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.loaders.Loader.list_available_services", "name": "list_available_services", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "type_name"], "arg_types": ["botocore.loaders.Loader", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_available_services of Loader", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "load_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.loaders.Loader.load_data", "name": "load_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["botocore.loaders.Loader", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_data of Loader", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "load_data_with_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.loaders.Loader.load_data_with_path", "name": "load_data_with_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["botocore.loaders.Loader", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_data_with_path of Loader", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "load_service_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "service_name", "type_name", "api_version"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.loaders.Loader.load_service_model", "name": "load_service_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "service_name", "type_name", "api_version"], "arg_types": ["botocore.loaders.Loader", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_service_model of Loader", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "search_paths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "botocore.loaders.Loader.search_paths", "name": "search_paths", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.loaders.Loader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search_paths of Loader", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "botocore.loaders.Loader.search_paths", "name": "search_paths", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.loaders.Loader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search_paths of Loader", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.loaders.Loader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.loaders.Loader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "UnknownServiceError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.UnknownServiceError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.loaders.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.loaders.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.loaders.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.loaders.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.loaders.__package__", "name": "__package__", "type": "builtins.str"}}, "create_loader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["search_path_string"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.loaders.create_loader", "name": "create_loader", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["search_path_string"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_loader", "ret_type": "botocore.loaders.Loader", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "deep_merge": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.deep_merge", "kind": "Gdef"}, "instance_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.loaders.instance_cache", "name": "instance_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_cache", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.loaders.logger", "name": "logger", "type": "logging.Logger"}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\loaders.pyi"}