{".class": "MypyFile", "_fullname": "tests.unit.test_models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AgentEvent": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.models.events.AgentEvent", "kind": "Gdef"}, "EventType": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.models.events.EventType", "kind": "Gdef"}, "TestAgentEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.unit.test_models.TestAgentEvent", "name": "TestAgentEvent", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tests.unit.test_models.TestAgentEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.unit.test_models", "mro": ["tests.unit.test_models.TestAgentEvent", "builtins.object"], "names": {".class": "SymbolTable", "test_acd_event_validation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_models.TestAgentEvent.test_acd_event_validation", "name": "test_acd_event_validation", "type": null}}, "test_extra_fields_rejected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_models.TestAgentEvent.test_extra_fields_rejected", "name": "test_extra_fields_rejected", "type": null}}, "test_invalid_agent_uri": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_models.TestAgentEvent.test_invalid_agent_uri", "name": "test_invalid_agent_uri", "type": null}}, "test_missing_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_models.TestAgentEvent.test_missing_required_fields", "name": "test_missing_required_fields", "type": null}}, "test_timestamp_parsing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_models.TestAgentEvent.test_timestamp_parsing", "name": "test_timestamp_parsing", "type": null}}, "test_valid_acd_login_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sample_acd_login_event"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_models.TestAgentEvent.test_valid_acd_login_event", "name": "test_valid_acd_login_event", "type": null}}, "test_valid_login_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sample_login_event"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_models.TestAgentEvent.test_valid_login_event", "name": "test_valid_login_event", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.unit.test_models.TestAgentEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.unit.test_models.TestAgentEvent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.ValidationError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_models.__package__", "name": "__package__", "type": "builtins.str"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}}, "path": "tests\\unit\\test_models.py"}