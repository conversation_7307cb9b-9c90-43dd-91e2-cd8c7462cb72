{"data_mtime": 1757363959, "dep_lines": [34, 25, 26, 27, 28, 29, 35, 36, 38, 39, 40, 2, 3, 4, 5, 6, 8, 13, 14, 25, 45, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest.nodes", "_pytest._io", "_pytest.capture", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.main", "_pytest.stash", "_pytest.terminal", "io", "logging", "os", "re", "contextlib", "datetime", "pathlib", "typing", "_pytest", "typing_extensions", "builtins", "_collections_abc", "_pytest._io.terminalwriter", "_pytest.hookspec", "_pytest.mark", "_typeshed", "abc", "<PERSON><PERSON><PERSON><PERSON>", "enum", "pluggy", "pluggy._hooks", "pluggy._manager"], "hash": "7dacc5c887999241fcd762aa3815abcfe314adaec0458f52aed464f44b575bcf", "id": "_pytest.logging", "ignore_all": true, "interface_hash": "d1a9d7d061fe60ae9f7e355eb9251f718a2c5973a0c616f6a56742ebb1e6c493", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\logging.py", "plugin_data": null, "size": 34130, "suppressed": [], "version_id": "1.8.0"}