{"data_mtime": 1757363959, "dep_lines": [33, 563, 23, 24, 25, 27, 34, 35, 36, 41, 43, 688, 2, 3, 4, 5, 6, 7, 8, 9, 10, 23, 48, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 20, 10, 10, 5, 5, 5, 5, 5, 5, 5, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest.config.compat", "_pytest._code", "_pytest.nodes", "_pytest.compat", "_pytest.config", "_pytest.fixtures", "_pytest.outcomes", "_pytest.pathlib", "_pytest.reports", "_pytest.runner", "_pytest.python", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "fnmatch", "functools", "importlib", "os", "sys", "pathlib", "typing", "_pytest", "typing_extensions", "builtins", "_pytest.config.exceptions", "_pytest.mark", "abc", "enum", "pluggy", "pluggy._hooks", "pluggy._manager", "pluggy._tracing"], "hash": "f814505d7c0cadb4ccb52743d1c858c831c71f3b873ec2140509f309c918c6fd", "id": "_pytest.main", "ignore_all": true, "interface_hash": "1f0c72e445272e223ed1aefa4557121943ebe39bef1c15d2cea6639bf3f21cbf", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\main.py", "plugin_data": null, "size": 32658, "suppressed": [], "version_id": "1.8.0"}