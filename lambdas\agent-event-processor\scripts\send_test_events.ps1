#!/usr/bin/env pwsh
# PowerShell script to send all 6 test events to LocalStack SQS

param(
    [string]$Profile = $env:AWS_PROFILE,
    [string]$QueueName = "agent-events-queue",
    [string]$LocalStackEndpoint = "http://localhost:4566",
    [string]$Region = "us-east-1",
    [switch]$Help
)

if ($Help) {
    Write-Host "Usage: .\send_test_events.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Profile <profile>     AWS profile to use (default: `$env:AWS_PROFILE)"
    Write-Host "  -QueueName <name>      SQS queue name (default: agent-events-queue)"
    Write-Host "  -LocalStackEndpoint    LocalStack endpoint (default: http://localhost:4566)"
    Write-Host "  -Region <region>       AWS region (default: us-east-1)"
    Write-Host "  -Help                  Show this help message"
    Write-Host ""
    Write-Host "Example:"
    Write-Host "  `$env:AWS_PROFILE='admin-memo'; .\send_test_events.ps1"
    exit 0
}

$ErrorActionPreference = "Stop"

Write-Host "Sending test events to LocalStack SQS..." -ForegroundColor Green
Write-Host "Profile: $Profile" -ForegroundColor Cyan
Write-Host "Queue: $QueueName" -ForegroundColor Cyan
Write-Host "Endpoint: $LocalStackEndpoint" -ForegroundColor Cyan

# Set AWS environment variables
if ($Profile) {
    $env:AWS_PROFILE = $Profile
    Write-Host "Using AWS Profile: $Profile" -ForegroundColor Yellow
} else {
    # Use test credentials for LocalStack
    $env:AWS_ACCESS_KEY_ID = "test"
    $env:AWS_SECRET_ACCESS_KEY = "test"
    Write-Host "Using test credentials for LocalStack" -ForegroundColor Yellow
}

$env:AWS_DEFAULT_REGION = $Region

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition
$ProjectDir = Split-Path -Parent $ScriptDir

# Function to create XML event
function Create-EventXML {
    param(
        [string]$EventType,
        [string]$AgentName,
        [hashtable]$AdditionalData = @{}
    )
    
    $timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    
    $xml = @"
<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>$timestamp</timestamp>
    <eventType>$EventType</eventType>
    <agencyOrElement>TestAgency</agencyOrElement>
    <agent>$AgentName</agent>
    <reason>normal</reason>
    <workstation>TEST_WS_$EventType</workstation>
    <operatorId>TEST_OP_$AgentName</operatorId>
    <agentRole>Test Agent</agentRole>
    <agentUri>tel:+1234567890</agentUri>
    <mediaLabel>TEST_MEDIA_$EventType</mediaLabel>
    <tenantGroup>TestAgency</tenantGroup>
    <deviceName>TestDevice_$EventType</deviceName>
"@

    # Add additional data
    foreach ($key in $AdditionalData.Keys) {
        $xml += "`n    <$key>$($AdditionalData[$key])</$key>"
    }
    
    $xml += "`n</LogEvent>"
    return $xml
}

# Function to send message to SQS
function Send-SQSMessage {
    param(
        [string]$MessageBody,
        [string]$EventType,
        [string]$AgentName
    )
    
    try {
        Write-Host "Sending $EventType event for agent $AgentName..." -ForegroundColor Yellow
        
        # Get queue URL
        $queueUrl = aws --endpoint-url=$LocalStackEndpoint sqs get-queue-url --queue-name $QueueName --region $Region --query 'QueueUrl' --output text
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to get queue URL"
        }
        
        # Send message
        $messageId = aws --endpoint-url=$LocalStackEndpoint sqs send-message --queue-url $queueUrl --message-body $MessageBody --region $Region --query 'MessageId' --output text
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to send message"
        }
        
        Write-Host "  ✓ Sent $EventType event (MessageId: $messageId)" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "  ✗ Failed to send $EventType event: $_" -ForegroundColor Red
        return $false
    }
}

# Define test events
$testEvents = @(
    @{
        EventType = "Login"
        AgentName = "test_agent_login"
        AdditionalData = @{}
    },
    @{
        EventType = "Logout"
        AgentName = "test_agent_logout"
        AdditionalData = @{}
    },
    @{
        EventType = "ACDLogin"
        AgentName = "test_agent_acd_login"
        AdditionalData = @{
            acdId = "TEST_ACD_001"
            ringGroupName = "TEST_RING_GROUP"
        }
    },
    @{
        EventType = "ACDLogout"
        AgentName = "test_agent_acd_logout"
        AdditionalData = @{
            acdId = "TEST_ACD_002"
            ringGroupName = "TEST_RING_GROUP_2"
        }
    },
    @{
        EventType = "AgentBusiedOut"
        AgentName = "test_agent_busied"
        AdditionalData = @{
            busiedOutAction = "Manual"
            busiedOutDuration = "300"
        }
    },
    @{
        EventType = "AgentAvailable"
        AgentName = "test_agent_available"
        AdditionalData = @{
            busiedOutAction = "Manual"
        }
    }
)

# Send all events
$successCount = 0
$totalCount = $testEvents.Count

Write-Host "`nSending $totalCount test events..." -ForegroundColor Cyan

foreach ($event in $testEvents) {
    $xml = Create-EventXML -EventType $event.EventType -AgentName $event.AgentName -AdditionalData $event.AdditionalData
    $success = Send-SQSMessage -MessageBody $xml -EventType $event.EventType -AgentName $event.AgentName
    
    if ($success) {
        $successCount++
    }
    
    # Small delay between messages
    Start-Sleep -Milliseconds 500
}

# Summary
Write-Host "`n" + "="*60 -ForegroundColor Cyan
Write-Host "Test Events Summary" -ForegroundColor Cyan
Write-Host "="*60 -ForegroundColor Cyan
Write-Host "Total events: $totalCount" -ForegroundColor White
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $($totalCount - $successCount)" -ForegroundColor Red

if ($successCount -eq $totalCount) {
    Write-Host "`nAll events sent successfully! 🎉" -ForegroundColor Green
    Write-Host "The Lambda function should now process these events." -ForegroundColor Yellow
    Write-Host "Check the Lambda logs for processing results." -ForegroundColor Yellow
} else {
    Write-Host "`nSome events failed to send. Check the errors above." -ForegroundColor Red
    exit 1
}

Write-Host "`nTo check Lambda logs:" -ForegroundColor Cyan
Write-Host "  docker logs agent-event-processor-lambda" -ForegroundColor White
Write-Host "`nTo check LocalStack logs:" -ForegroundColor Cyan
Write-Host "  docker logs agent-event-processor-localstack" -ForegroundColor White
