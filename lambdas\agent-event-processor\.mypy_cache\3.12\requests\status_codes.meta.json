{"data_mtime": 1757364036, "dep_lines": [1, 1, 1], "dep_prios": [5, 5, 30], "dependencies": ["typing", "builtins", "abc"], "hash": "9a23b734408efb7c9098ae7ace0ebb1f36526f1d5e111d7198a0018e80ccb97b", "id": "requests.status_codes", "ignore_all": true, "interface_hash": "4f64eaaf699f166cb670f7d8ca628cfbde247e8a0835f44ca8ab9c4b36ed85ca", "mtime": 1757363991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\requests-stubs\\status_codes.pyi", "plugin_data": null, "size": 35, "suppressed": [], "version_id": "1.8.0"}