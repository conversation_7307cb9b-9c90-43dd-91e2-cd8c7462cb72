{"data_mtime": 1757364037, "dep_lines": [1, 1, 1, 2, 12, 24, 25, 26, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["requests.__version__", "requests.packages", "requests.utils", "requests.api", "requests.exceptions", "requests.models", "requests.sessions", "requests.status_codes", "builtins", "abc", "typing"], "hash": "a906af825a4d2e7ce781eb550e5081222f3e2ae1247b1cd1988e83aaaa72f809", "id": "requests", "ignore_all": true, "interface_hash": "c898dc09071c65b487d5c1eb639f16bcc761821267b634cf88427efdd29a0d85", "mtime": 1757363991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\requests-stubs\\__init__.pyi", "plugin_data": null, "size": 1351, "suppressed": [], "version_id": "1.8.0"}