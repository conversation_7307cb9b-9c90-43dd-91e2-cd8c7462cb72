{"data_mtime": **********, "dep_lines": [13, 14, 17, 20, 21, 23, 24, 25, 28, 31, 32, 2, 3, 4, 5, 6, 11, 35, 1, 1, 1, 1, 1, 1, 1, 1, 37], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["_pytest.cacheprovider", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.main", "_pytest.monkeypatch", "_pytest.nodes", "_pytest.pytester", "_pytest.terminal", "_pytest.tmpdir", "dataclasses", "shlex", "subprocess", "pathlib", "typing", "iniconfig", "typing_extensions", "builtins", "_pytest.hookspec", "abc", "enum", "os", "pluggy", "pluggy._hooks", "pluggy._manager"], "hash": "09105f848b93a10353143cc0e9f7614e09d015537e57f11038f398eca524d871", "id": "_pytest.legacypath", "ignore_all": true, "interface_hash": "d3de1b6b480d370198e8bbabd600c5b44ca7bedeec09c9ff3b25d36929bab3b4", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\legacypath.py", "plugin_data": null, "size": 16929, "suppressed": ["pexpect"], "version_id": "1.8.0"}