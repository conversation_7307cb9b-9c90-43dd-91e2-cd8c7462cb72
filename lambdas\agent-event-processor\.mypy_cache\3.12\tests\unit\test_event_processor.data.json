{".class": "MypyFile", "_fullname": "tests.unit.test_event_processor", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AgentEvent": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.models.events.AgentEvent", "kind": "Gdef"}, "EventProcessor": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.services.event_processor.EventProcessor", "kind": "Gdef"}, "EventType": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.models.events.EventType", "kind": "Gdef"}, "Mock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.Mock", "kind": "Gdef"}, "Settings": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.config.settings.Settings", "kind": "Gdef"}, "TestEventProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.unit.test_event_processor.TestEventProcessor", "name": "TestEventProcessor", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tests.unit.test_event_processor.TestEventProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.unit.test_event_processor", "mro": ["tests.unit.test_event_processor.TestEventProcessor", "builtins.object"], "names": {".class": "SymbolTable", "event_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "mock_settings", "mock_db_service", "mock_dimension_manager", "mock_fact_manager"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.event_processor", "name": "event_processor", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.event_processor", "name": "event_processor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "mock_settings", "mock_db_service", "mock_dimension_manager", "mock_fact_manager"], "arg_types": ["tests.unit.test_event_processor.TestEventProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "event_processor of TestEventProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "mock_db_service": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.mock_db_service", "name": "mock_db_service", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.mock_db_service", "name": "mock_db_service", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tests.unit.test_event_processor.TestEventProcessor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mock_db_service of TestEventProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "mock_dimension_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.mock_dimension_manager", "name": "mock_dimension_manager", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.mock_dimension_manager", "name": "mock_dimension_manager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tests.unit.test_event_processor.TestEventProcessor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mock_dimension_manager of TestEventProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "mock_fact_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.mock_fact_manager", "name": "mock_fact_manager", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.mock_fact_manager", "name": "mock_fact_manager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tests.unit.test_event_processor.TestEventProcessor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mock_fact_manager of TestEventProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "mock_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.mock_settings", "name": "mock_settings", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.mock_settings", "name": "mock_settings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tests.unit.test_event_processor.TestEventProcessor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mock_settings of TestEventProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "sample_login_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.sample_login_event", "name": "sample_login_event", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.sample_login_event", "name": "sample_login_event", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tests.unit.test_event_processor.TestEventProcessor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sample_login_event of TestEventProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "test_process_single_event_failure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_processor", "sample_login_event"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_event_processor.TestEventProcessor.test_process_single_event_failure", "name": "test_process_single_event_failure", "type": null}}, "test_process_single_event_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "mock_repo_timezone", "mock_proc_timezone", "event_processor", "sample_login_event"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.test_process_single_event_success", "name": "test_process_single_event_success", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.unit.test_event_processor.TestEventProcessor.test_process_single_event_success", "name": "test_process_single_event_success", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_process_single_event_success of TestEventProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "test_repository_dimension_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_processor", "sample_login_event"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_event_processor.TestEventProcessor.test_repository_dimension_keys", "name": "test_repository_dimension_keys", "type": null}}, "test_timestamp_parsing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_processor", "sample_login_event"], "dataclass_transform_spec": null, "flags": [], "fullname": "tests.unit.test_event_processor.TestEventProcessor.test_timestamp_parsing", "name": "test_timestamp_parsing", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.unit.test_event_processor.TestEventProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.unit.test_event_processor.TestEventProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_event_processor.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_event_processor.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_event_processor.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_event_processor.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.unit.test_event_processor.__package__", "name": "__package__", "type": "builtins.str"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}}, "path": "tests\\unit\\test_event_processor.py"}