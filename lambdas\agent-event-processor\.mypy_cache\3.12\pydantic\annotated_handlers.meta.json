{"data_mtime": 1757356838, "dep_lines": [10, 7, 11, 3, 5, 7, 1, 1, 1, 1, 1], "dep_prios": [25, 10, 25, 5, 5, 20, 5, 20, 20, 30, 30], "dependencies": ["pydantic._internal._namespace_utils", "pydantic_core.core_schema", "pydantic.json_schema", "__future__", "typing", "pydantic_core", "builtins", "pyexpat.errors", "pyexpat.model", "abc", "pydantic._internal"], "hash": "59fc854aac28108157061ed3ef73f270a9682350e25f8e465a2d3e24eb024786", "id": "pydantic.annotated_handlers", "ignore_all": true, "interface_hash": "faa714aa1760831f0ed410b0fe3fb61572d3e4e477604cd609eeeada4c3510f1", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\annotated_handlers.py", "plugin_data": null, "size": 4407, "suppressed": [], "version_id": "1.8.0"}