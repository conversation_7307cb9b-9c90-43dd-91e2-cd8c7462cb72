{"data_mtime": 1757356835, "dep_lines": [7, 6, 1, 3, 4, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 20, 20, 30, 30], "dependencies": ["urllib3.util.util", "urllib3.exceptions", "__future__", "re", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "abc", "enum"], "hash": "59187e4cc617a2c9a0a7c9bc953e07e6ca681f0e7252395c3027d4e77024a00b", "id": "urllib3.util.url", "ignore_all": true, "interface_hash": "c3d311dc852e4fc64417ab1309bca1248361de5b70d9985e8a2163eb5c8d8148", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\util\\url.py", "plugin_data": null, "size": 15205, "suppressed": [], "version_id": "1.8.0"}