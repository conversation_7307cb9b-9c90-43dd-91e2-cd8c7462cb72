{"data_mtime": 1757356836, "dep_lines": [4, 8, 9, 1, 2, 3, 5, 6, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "asyncio.events", "asyncio.selector_events", "sys", "types", "abc", "typing", "typing_extensions", "builtins", "pyexpat.errors", "pyexpat.model", "_typeshed"], "hash": "db943b225d442670f4f681aa18f55444c15fcb2bcd421d3a7743983509df1c9e", "id": "asyncio.unix_events", "ignore_all": true, "interface_hash": "f68eda0a4bf481508e46110ad2f22c88f60e0f7314700fe56936429f138917e5", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\asyncio\\unix_events.pyi", "plugin_data": null, "size": 8664, "suppressed": [], "version_id": "1.8.0"}