{"data_mtime": 1757356838, "dep_lines": [21, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 38, 18, 20, 22, 41, 44, 164, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 25, 25, 20, 5, 10, 10, 10, 5, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._fields", "pydantic._internal._generate_schema", "pydantic._internal._generics", "pydantic._internal._import_utils", "pydantic._internal._mock_val_ser", "pydantic._internal._namespace_utils", "pydantic._internal._signature", "pydantic._internal._typing_extra", "pydantic._internal._utils", "typing_inspection.typing_objects", "pydantic.errors", "pydantic.warnings", "pydantic.fields", "pydantic.main", "pydantic.root_model", "__future__", "builtins", "operator", "sys", "typing", "warnings", "weakref", "abc", "functools", "types", "pydantic_core", "typing_extensions", "typing_inspection", "pyexpat.errors", "pyexpat.model", "_collections_abc", "_typeshed", "_warnings", "_weakref", "annotated_types", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.types", "re"], "hash": "d906b96381200688e486c547bb43a3a69854225598b955cc835282da8a5cd34b", "id": "pydantic._internal._model_construction", "ignore_all": true, "interface_hash": "7e3f2a251158bf0ced7047491d56bd53ff2c4deab692f086acf9991abababb2b", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_model_construction.py", "plugin_data": null, "size": 35228, "suppressed": [], "version_id": "1.8.0"}