{".class": "MypyFile", "_fullname": "pydantic._internal._known_annotated_metadata", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ALLOW_INF_NAN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.ALLOW_INF_NAN", "name": "ALLOW_INF_NAN", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BOOL_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.BOOL_CONSTRAINTS", "name": "BOOL_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "BYTES_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.BYTES_CONSTRAINTS", "name": "BYTES_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "COMPLEX_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.COMPLEX_CONSTRAINTS", "name": "COMPLEX_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "CONSTRAINTS_TO_ALLOWED_SCHEMAS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.CONSTRAINTS_TO_ALLOWED_SCHEMAS", "name": "CONSTRAINTS_TO_ALLOWED_SCHEMAS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}], "type_ref": "builtins.dict"}}}, "CoreSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreSchema", "kind": "Gdef"}, "DATE_TIME_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.DATE_TIME_CONSTRAINTS", "name": "DATE_TIME_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "DECIMAL_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.DECIMAL_CONSTRAINTS", "name": "DECIMAL_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "DICT_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.DICT_CONSTRAINTS", "name": "DICT_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "_decimal.Decimal", "kind": "Gdef"}, "ENUM_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.ENUM_CONSTRAINTS", "name": "ENUM_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "FAIL_FAST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.FAIL_FAST", "name": "FAIL_FAST", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "FLOAT_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.FLOAT_CONSTRAINTS", "name": "FLOAT_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "GENERATOR_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.GENERATOR_CONSTRAINTS", "name": "GENERATOR_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "INEQUALITY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.INEQUALITY", "name": "INEQUALITY", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "INT_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.INT_CONSTRAINTS", "name": "INT_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LAX_OR_STRICT_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.LAX_OR_STRICT_CONSTRAINTS", "name": "LAX_OR_STRICT_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "LENGTH_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.LENGTH_CONSTRAINTS", "name": "LENGTH_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "LIST_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.LIST_CONSTRAINTS", "name": "LIST_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "NUMERIC_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.NUMERIC_CONSTRAINTS", "name": "NUMERIC_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "NUMERIC_SCHEMA_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.NUMERIC_SCHEMA_TYPES", "name": "NUMERIC_SCHEMA_TYPES", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "PydanticCustomError": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticCustomError", "kind": "Gdef"}, "PydanticMetadata": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._fields.PydanticMetadata", "kind": "Gdef"}, "SEQUENCE_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.SEQUENCE_CONSTRAINTS", "name": "SEQUENCE_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "SEQUENCE_SCHEMA_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.SEQUENCE_SCHEMA_TYPES", "name": "SEQUENCE_SCHEMA_TYPES", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "SET_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.SET_CONSTRAINTS", "name": "SET_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "STRICT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.STRICT", "name": "STRICT", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "STR_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.STR_CONSTRAINTS", "name": "STR_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "TEXT_SCHEMA_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.TEXT_SCHEMA_TYPES", "name": "TEXT_SCHEMA_TYPES", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "TIMEDELTA_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.TIMEDELTA_CONSTRAINTS", "name": "TIMEDELTA_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "TIME_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.TIME_CONSTRAINTS", "name": "TIME_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "TUPLE_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.TUPLE_CONSTRAINTS", "name": "TUPLE_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "UNION_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.UNION_CONSTRAINTS", "name": "UNION_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "URL_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.URL_CONSTRAINTS", "name": "URL_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "UUID_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.UUID_CONSTRAINTS", "name": "UUID_CONSTRAINTS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.ValidationError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._known_annotated_metadata.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._known_annotated_metadata.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._known_annotated_metadata.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._known_annotated_metadata.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._known_annotated_metadata.__package__", "name": "__package__", "type": "builtins.str"}}, "_get_at_to_constraint_map": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "pydantic._internal._known_annotated_metadata._get_at_to_constraint_map", "name": "_get_at_to_constraint_map", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_at_to_constraint_map", "ret_type": {".class": "Instance", "args": ["builtins.type", "builtins.str"], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic._internal._known_annotated_metadata._get_at_to_constraint_map", "name": "_get_at_to_constraint_map", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.type", "builtins.str"], "type_ref": "builtins.dict"}], "type_ref": "functools._lru_cache_wrapper"}}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "apply_known_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["annotation", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._known_annotated_metadata.apply_known_metadata", "name": "apply_known_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["annotation", "schema"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_known_metadata", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "as_jsonable_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["v"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._known_annotated_metadata.as_jsonable_value", "name": "as_jsonable_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["v"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_jsonable_value", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "c": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._known_annotated_metadata.c", "name": "c", "type": "builtins.str"}}, "check_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["metadata", "allowed", "source_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._known_annotated_metadata.check_metadata", "name": "check_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["metadata", "allowed", "source_type"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_metadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "collect_known_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["annotations"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._known_annotated_metadata.collect_known_metadata", "name": "collect_known_metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["annotations"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collect_known_metadata", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "constraint_schema_pairings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.constraint_schema_pairings", "name": "constraint_schema_pairings", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "constraints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.constraints", "name": "constraints", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy.copy", "kind": "Gdef"}, "cs": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "expand_grouped_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["annotations"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._known_annotated_metadata.expand_grouped_metadata", "name": "expand_grouped_metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["annotations"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand_grouped_metadata", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "import_cached_field_info": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._import_utils.import_cached_field_info", "kind": "Gdef"}, "lru_cache": {".class": "SymbolTableNode", "cross_ref": "functools.lru_cache", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "schemas": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._known_annotated_metadata.schemas", "name": "schemas", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}}, "to_jsonable_python": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.to_jsonable_python", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py"}