{"data_mtime": 1757363959, "dep_lines": [9, 12, 13, 14, 1, 2, 3, 4, 8, 17, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 10, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config", "_pytest.main", "_pytest.nodes", "_pytest.terminal", "sys", "warnings", "contextlib", "typing", "pytest", "typing_extensions", "builtins", "_pytest.assertion", "_pytest.hookspec", "abc", "pluggy", "pluggy._hooks"], "hash": "a41637848ace6686da5a4f6f1e05bf69ce388d762197252e7deac33a1c1a3062", "id": "_pytest.warnings", "ignore_all": true, "interface_hash": "fcd01d0828332e25aadc7df5f0cfbbf5041a48a126c41fbee47e9e4af9eeee6c", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\warnings.py", "plugin_data": null, "size": 5070, "suppressed": [], "version_id": "1.8.0"}