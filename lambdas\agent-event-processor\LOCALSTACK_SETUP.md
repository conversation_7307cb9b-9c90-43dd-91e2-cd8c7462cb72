# LocalStack Setup Guide for Agent Event Processor

This guide shows how to set up and test the Agent Event Processor with LocalStack, including SQS triggers and Redshift Data API integration.

## Prerequisites

1. **Docker and Docker Compose** installed
2. **Python 3.12+** with pip
3. **AWS CLI** installed
4. **PowerShell** (Windows) or **Bash** (Linux/Mac)

## Quick Start

### 1. Start LocalStack

```powershell
# Navigate to the project directory
cd lambdas/agent-event-processor

# Start LocalStack with docker-compose
docker-compose up -d
```

### 2. Set AWS Profile (Optional)

If you want to use your AWS credentials instead of test credentials:

```powershell
# PowerShell
$env:AWS_PROFILE = "admin-memo"

# Bash
export AWS_PROFILE="admin-memo"
```

### 3. Run Complete Setup

```powershell
# PowerShell
.\scripts\setup_localstack_complete.ps1

# Bash
./scripts/setup_localstack_complete.sh
```

This script will:
- Wait for LocalStack to be ready
- Create SQS queue and DLQ
- Set up Redshift cluster (mock)
- Build and deploy Lambda function
- Create SQS trigger for Lambda

### 4. Test the Integration

```powershell
# Test with your AWS profile
$env:AWS_PROFILE = "admin-memo"
python .\test_busied_out.py

# Or test with specific profile
python .\test_busied_out.py --profile admin-memo

# Or test with default credentials
python .\test_busied_out.py
```

### 5. Send All Test Events

```powershell
# PowerShell
.\scripts\send_test_events.ps1

# Bash
./scripts/send_test_events.sh
```

## Manual Testing Steps

### 1. Check LocalStack Health

```bash
curl http://localhost:4566/_localstack/health
```

### 2. List SQS Queues

```bash
aws --endpoint-url=http://localhost:4566 sqs list-queues --region us-east-1
```

### 3. Check Lambda Function

```bash
aws --endpoint-url=http://localhost:4566 lambda list-functions --region us-east-1
```

### 4. View Lambda Logs

```bash
# Docker logs
docker logs agent-event-processor-lambda

# AWS CLI logs
aws --endpoint-url=http://localhost:4566 logs describe-log-groups --region us-east-1
```

## Configuration

### Environment Variables

The Lambda function uses these environment variables:

- `AWS_ENDPOINT_URL`: LocalStack endpoint (http://localhost:4566)
- `DATABASE_CLUSTER_IDENTIFIER`: Redshift cluster ID (localstack-redshift)
- `DATABASE_REDSHIFT_DATABASE`: Database name (dev)
- `DATABASE_REDSHIFT_USER`: Database user (solacom)
- `CLIENT_NAME`: Client name (TestClient)
- `CLIENT_TIMEZONE`: Client timezone (America/New_York)

### Docker Compose Configuration

The `docker-compose.yml` includes:

- **LocalStack**: AWS services simulation
- **Lambda Container**: For direct testing
- **Volume Mounts**: AWS credentials from host

## Troubleshooting

### LocalStack Not Starting

```bash
# Check Docker logs
docker logs agent-event-processor-localstack

# Restart LocalStack
docker-compose down
docker-compose up -d
```

### Lambda Build Issues

```bash
# Clean build
.\scripts\build_lambda.ps1 -Clean

# Check Python dependencies
pip install -r requirements.txt
```

### SQS Messages Not Processing

1. Check Lambda function exists:
   ```bash
   aws --endpoint-url=http://localhost:4566 lambda list-functions
   ```

2. Check SQS trigger:
   ```bash
   aws --endpoint-url=http://localhost:4566 lambda list-event-source-mappings
   ```

3. Check Lambda logs:
   ```bash
   docker logs agent-event-processor-lambda
   ```

### Redshift Data API Issues

LocalStack's Redshift Data API support is limited. The Lambda will:
- Connect to LocalStack's Redshift Data API endpoint
- Execute SQL statements (may be mocked)
- Handle errors gracefully

## File Structure

```
lambdas/agent-event-processor/
├── docker-compose.yml          # LocalStack and Lambda containers
├── Dockerfile                  # Lambda container image
├── test_busied_out.py         # Main test script
├── scripts/
│   ├── build_lambda.ps1       # Build Lambda package (PowerShell)
│   ├── build_lambda.sh        # Build Lambda package (Bash)
│   ├── setup_localstack_complete.ps1  # Complete setup (PowerShell)
│   ├── setup_localstack_complete.sh   # Complete setup (Bash)
│   ├── send_test_events.ps1   # Send all test events (PowerShell)
│   └── send_test_events.sh    # Send all test events (Bash)
└── src/                       # Lambda source code
```

## Event Types Supported

The system supports these agent event types:

1. **Login** - Agent login events
2. **Logout** - Agent logout events  
3. **ACDLogin** - ACD system login events
4. **ACDLogout** - ACD system logout events
5. **AgentBusiedOut** - Agent busied out events
6. **AgentAvailable** - Agent available events

## Next Steps

1. **Database Schema**: Set up actual Redshift tables for production
2. **Monitoring**: Add CloudWatch metrics and alarms
3. **Error Handling**: Enhance error handling and retry logic
4. **Performance**: Optimize for high-volume event processing

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Docker and Lambda logs
3. Verify LocalStack health and service availability
