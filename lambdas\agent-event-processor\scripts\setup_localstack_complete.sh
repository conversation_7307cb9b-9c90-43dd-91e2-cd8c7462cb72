#!/bin/bash
# Complete LocalStack setup script for Agent Event Processor with Redshift Data API

set -e

# Default configuration
PROFILE="${AWS_PROFILE:-}"
LOCALSTACK_ENDPOINT="http://localhost:4566"
REGION="us-east-1"
SKIP_BUILD=false

# Function to show help
show_help() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --profile <profile>     AWS profile to use (default: \$AWS_PROFILE)"
    echo "  --endpoint <url>        LocalStack endpoint (default: http://localhost:4566)"
    echo "  --region <region>       AWS region (default: us-east-1)"
    echo "  --skip-build           Skip building Lambda package"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Example:"
    echo "  AWS_PROFILE='admin-memo' $0"
    echo "  $0 --profile admin-memo"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --profile)
            PROFILE="$2"
            shift 2
            ;;
        --endpoint)
            LOCALSTACK_ENDPOINT="$2"
            shift 2
            ;;
        --region)
            REGION="$2"
            shift 2
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

echo "Setting up complete LocalStack environment for Agent Event Processor..."
echo "Profile: $PROFILE"
echo "Endpoint: $LOCALSTACK_ENDPOINT"

# Configuration
QUEUE_NAME="agent-events-queue"
DLQ_NAME="agent-events-dlq"
LAMBDA_NAME="agent-event-processor"
REDSHIFT_CLUSTER="localstack-redshift"
REDSHIFT_DATABASE="dev"
REDSHIFT_USER="solacom"

# Set AWS environment variables
if [ -n "$PROFILE" ]; then
    export AWS_PROFILE="$PROFILE"
    echo "Using AWS Profile: $PROFILE"
else
    # Use test credentials for LocalStack
    export AWS_ACCESS_KEY_ID="test"
    export AWS_SECRET_ACCESS_KEY="test"
    echo "Using test credentials for LocalStack"
fi

export AWS_DEFAULT_REGION="$REGION"

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Function to wait for LocalStack
wait_for_localstack() {
    echo "Waiting for LocalStack to be ready..."
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s "$LOCALSTACK_ENDPOINT/_localstack/health" | grep -q '"sqs": "available"' && \
           curl -s "$LOCALSTACK_ENDPOINT/_localstack/health" | grep -q '"lambda": "available"'; then
            echo "LocalStack is ready!"
            return 0
        fi
        
        attempt=$((attempt + 1))
        echo "Waiting for LocalStack... (attempt $attempt/$max_attempts)"
        sleep 2
    done
    
    echo "LocalStack failed to start within timeout"
    return 1
}

# Function to create SQS resources
setup_sqs_resources() {
    echo "Setting up SQS resources..."
    
    # Create Dead Letter Queue
    echo "Creating SQS Dead Letter Queue..."
    aws --endpoint-url="$LOCALSTACK_ENDPOINT" sqs create-queue --queue-name "$DLQ_NAME" --region "$REGION"
    
    local dlq_url
    dlq_url=$(aws --endpoint-url="$LOCALSTACK_ENDPOINT" sqs get-queue-url --queue-name "$DLQ_NAME" --region "$REGION" --query 'QueueUrl' --output text)
    local dlq_arn
    dlq_arn=$(aws --endpoint-url="$LOCALSTACK_ENDPOINT" sqs get-queue-attributes --queue-url "$dlq_url" --attribute-names QueueArn --region "$REGION" --query 'Attributes.QueueArn' --output text)
    
    echo "Created DLQ: $dlq_arn"
    
    # Create main queue with DLQ configuration
    echo "Creating main SQS Queue..."
    local redrive_policy="{\"deadLetterTargetArn\":\"$dlq_arn\",\"maxReceiveCount\":3}"
    
    aws --endpoint-url="$LOCALSTACK_ENDPOINT" sqs create-queue --queue-name "$QUEUE_NAME" --attributes "RedrivePolicy=$redrive_policy" --region "$REGION"
    
    local queue_url
    queue_url=$(aws --endpoint-url="$LOCALSTACK_ENDPOINT" sqs get-queue-url --queue-name "$QUEUE_NAME" --region "$REGION" --query 'QueueUrl' --output text)
    local queue_arn
    queue_arn=$(aws --endpoint-url="$LOCALSTACK_ENDPOINT" sqs get-queue-attributes --queue-url "$queue_url" --attribute-names QueueArn --region "$REGION" --query 'Attributes.QueueArn' --output text)
    
    echo "Created Queue: $queue_url"
    
    # Export for use in other functions
    export QUEUE_URL="$queue_url"
    export QUEUE_ARN="$queue_arn"
    export DLQ_URL="$dlq_url"
    export DLQ_ARN="$dlq_arn"
}

# Function to create Redshift cluster
setup_redshift_cluster() {
    echo "Setting up Redshift cluster..."
    
    if aws --endpoint-url="$LOCALSTACK_ENDPOINT" redshift create-cluster \
        --cluster-identifier "$REDSHIFT_CLUSTER" \
        --db-name "$REDSHIFT_DATABASE" \
        --master-username "$REDSHIFT_USER" \
        --master-user-password "testpassword" \
        --node-type dc2.large \
        --region "$REGION" 2>/dev/null; then
        echo "Redshift cluster created: $REDSHIFT_CLUSTER"
    else
        echo "Redshift cluster may already exist or LocalStack doesn't support it fully"
        echo "Continuing with setup..."
    fi
}

# Function to create IAM role
setup_iam_role() {
    echo "Setting up IAM role..."
    
    local assume_role_policy='{
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Principal": {
                    "Service": "lambda.amazonaws.com"
                },
                "Action": "sts:AssumeRole"
            }
        ]
    }'
    
    if aws --endpoint-url="$LOCALSTACK_ENDPOINT" iam create-role \
        --role-name lambda-execution-role \
        --assume-role-policy-document "$assume_role_policy" \
        --region "$REGION" 2>/dev/null; then
        echo "IAM role created: lambda-execution-role"
    else
        echo "IAM role may already exist"
    fi
}

# Function to build Lambda package
build_lambda_package() {
    if [ "$SKIP_BUILD" = true ]; then
        echo "Skipping Lambda build..."
        return
    fi
    
    echo "Building Lambda package..."
    
    local build_script="$SCRIPT_DIR/build_lambda.sh"
    if [ -f "$build_script" ]; then
        "$build_script" --clean
    else
        echo "Error: Build script not found: $build_script"
        exit 1
    fi
}

# Function to deploy Lambda
deploy_lambda() {
    echo "Deploying Lambda function..."
    
    local zip_path="$PROJECT_DIR/agent-event-processor.zip"
    if [ ! -f "$zip_path" ]; then
        echo "Error: Lambda package not found: $zip_path"
        exit 1
    fi
    
    # Create CloudWatch Log Group
    if aws --endpoint-url="$LOCALSTACK_ENDPOINT" logs create-log-group \
        --log-group-name "/aws/lambda/$LAMBDA_NAME" \
        --region "$REGION" 2>/dev/null; then
        echo "Log group created"
    else
        echo "Log group may already exist"
    fi
    
    # Deploy Lambda function
    local env_vars="AWS_ENDPOINT_URL=$LOCALSTACK_ENDPOINT,DATABASE_CLUSTER_IDENTIFIER=$REDSHIFT_CLUSTER,DATABASE_REDSHIFT_DATABASE=$REDSHIFT_DATABASE,DATABASE_REDSHIFT_USER=$REDSHIFT_USER,CLIENT_NAME=TestClient,CLIENT_TIMEZONE=America/New_York"
    
    if aws --endpoint-url="$LOCALSTACK_ENDPOINT" lambda create-function \
        --function-name "$LAMBDA_NAME" \
        --runtime python3.12 \
        --role "arn:aws:iam::123456789012:role/lambda-execution-role" \
        --handler "agent_event_processor.lambda_function.lambda_handler" \
        --zip-file "fileb://$zip_path" \
        --timeout 300 \
        --memory-size 512 \
        --environment "Variables={$env_vars}" \
        --region "$REGION" 2>/dev/null; then
        echo "Lambda function deployed: $LAMBDA_NAME"
    else
        echo "Updating existing Lambda function..."
        aws --endpoint-url="$LOCALSTACK_ENDPOINT" lambda update-function-code \
            --function-name "$LAMBDA_NAME" \
            --zip-file "fileb://$zip_path" \
            --region "$REGION"
    fi
    
    # Create SQS trigger
    if aws --endpoint-url="$LOCALSTACK_ENDPOINT" lambda create-event-source-mapping \
        --event-source-arn "$QUEUE_ARN" \
        --function-name "$LAMBDA_NAME" \
        --batch-size 10 \
        --maximum-batching-window-in-seconds 5 \
        --region "$REGION" 2>/dev/null; then
        echo "SQS trigger created"
    else
        echo "SQS trigger may already exist"
    fi
}

# Main execution
echo "Starting LocalStack setup..."

# Wait for LocalStack
if ! wait_for_localstack; then
    echo "Error: LocalStack is not ready"
    exit 1
fi

# Setup resources
setup_sqs_resources
setup_redshift_cluster
setup_iam_role
build_lambda_package
deploy_lambda

# Summary
echo ""
echo "======================================================================"
echo "LocalStack setup completed successfully!"
echo "======================================================================"
echo "Resources created:"
echo "  - SQS Queue: $QUEUE_URL"
echo "  - SQS DLQ: $DLQ_URL"
echo "  - Redshift Cluster: $REDSHIFT_CLUSTER"
echo "  - Lambda Function: $LAMBDA_NAME"
echo "  - Log Group: /aws/lambda/$LAMBDA_NAME"
echo ""
echo "Next steps:"
echo "  1. Send test events: ./scripts/send_test_events.sh"
echo "  2. Check logs: docker logs agent-event-processor-lambda"
echo "  3. Run test script: python test_busied_out.py"
