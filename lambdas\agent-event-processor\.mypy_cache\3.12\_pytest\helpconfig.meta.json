{"data_mtime": 1757363959, "dep_lines": [13, 10, 14, 2, 3, 4, 5, 9, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest.config", "_pytest.terminal", "os", "sys", "<PERSON><PERSON><PERSON><PERSON>", "typing", "pytest", "builtins", "_pytest.hookspec", "_pytest.mark", "abc", "enum", "pluggy", "pluggy._hooks"], "hash": "81bb637dc37e6a763309b5530b8de3b0e7b7a6c32358db31ef1074cf4496ee44", "id": "_pytest.helpconfig", "ignore_all": true, "interface_hash": "592a598ffbf85f580fd55422fec3d0b8aef288641f81f1e774be43805bd75569", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\helpconfig.py", "plugin_data": null, "size": 8658, "suppressed": [], "version_id": "1.8.0"}