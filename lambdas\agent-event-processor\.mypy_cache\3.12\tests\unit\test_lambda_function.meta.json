{"data_mtime": 1757364118, "dep_lines": [12, 8, 10, 7, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.agent_event_processor.lambda_function", "unittest.mock", "aws_lambda_typing.events", "json", "builtins", "abc", "aws_lambda_typing", "aws_lambda_typing.events.sqs", "json.decoder", "json.encoder", "typing", "unittest"], "hash": "08714b75d2a4772f96083c94049554b2d9b5275aa4b15eef7da9a7c3834542b8", "id": "tests.unit.test_lambda_function", "ignore_all": false, "interface_hash": "99524846954e121564236f80746054921ed18752717311814cae08f5ad4658d2", "mtime": 1757364113, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "tests\\unit\\test_lambda_function.py", "plugin_data": null, "size": 13375, "suppressed": [], "version_id": "1.8.0"}