{"data_mtime": 1757363959, "dep_lines": [23, 32, 21, 26, 28, 30, 35, 36, 38, 39, 43, 351, 445, 1, 2, 3, 4, 5, 21, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.mark.structures", "_pytest._code", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.outcomes", "_pytest.pathlib", "_pytest.stash", "_pytest.warning_types", "_pytest.main", "_pytest.mark", "_pytest.fixtures", "os", "warnings", "inspect", "pathlib", "typing", "_pytest", "builtins", "_collections_abc", "abc", "types", "typing_extensions"], "hash": "bb732b0e091c571f7a660c412eac02920b49b2fe0ca617aea8e24a3112d3edaa", "id": "_pytest.nodes", "ignore_all": true, "interface_hash": "429cecab8fcff324b9cff3eb8289540769350f1284212a7c6a359736cdd7891e", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\nodes.py", "plugin_data": null, "size": 26687, "suppressed": [], "version_id": "1.8.0"}