{"data_mtime": 1757356836, "dep_lines": [15, 16, 18, 1, 1, 1, 1, 1, 1, 1, 26, 23], "dep_prios": [10, 10, 5, 5, 20, 20, 30, 30, 30, 30, 25, 5], "dependencies": ["sys", "typing", "tenacity", "builtins", "pyexpat.errors", "pyexpat.model", "abc", "tenacity.retry", "tenacity.stop", "tenacity.wait"], "hash": "bd2d4e35f3d8a06ccfc756ac41a55b7e8a3a0fdb4f2334b2b098a99bad58ab0f", "id": "tenacity.tornadoweb", "ignore_all": true, "interface_hash": "cb887a64ab7374e375d7cd5f81ca862561c6cdd0d53fb4f8d1001586eb1a188a", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\tenacity\\tornadoweb.py", "plugin_data": null, "size": 2125, "suppressed": ["tornado.concurrent", "tornado"], "version_id": "1.8.0"}