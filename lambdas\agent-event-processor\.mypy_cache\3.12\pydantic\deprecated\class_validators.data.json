{".class": "MypyFile", "_fullname": "pydantic.deprecated.class_validators", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "FunctionType": {".class": "SymbolTableNode", "cross_ref": "types.FunctionType", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "PydanticDeprecatedSince20": {".class": "SymbolTableNode", "cross_ref": "pydantic.warnings.PydanticDeprecatedSince20", "kind": "Gdef"}, "PydanticUserError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUserError", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "V1RootValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.deprecated.class_validators.V1RootValidator", "line": 53, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["pydantic.deprecated.class_validators._V1RootValidatorClsMethod", "pydantic._internal._decorators_v1.V1RootValidatorFunction"]}}}, "V1Validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.deprecated.class_validators.V1Validator", "line": 41, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["pydantic.deprecated.class_validators._OnlyValueValidatorClsMethod", "pydantic.deprecated.class_validators._V1ValidatorWithValuesClsMethod", "pydantic.deprecated.class_validators._V1ValidatorWithValuesKwOnlyClsMethod", "pydantic.deprecated.class_validators._V1ValidatorWithKwargsClsMethod", "pydantic.deprecated.class_validators._V1ValidatorWithValuesAndKwargsClsMethod", "pydantic._internal._decorators_v1.V1ValidatorWithValues", "pydantic._internal._decorators_v1.V1ValidatorWithValuesKwOnly", "pydantic._internal._decorators_v1.V1ValidatorWithKwargs", "pydantic._internal._decorators_v1.V1ValidatorWithValuesAndKwargs"]}}}, "_ALLOW_REUSE_WARNING_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.deprecated.class_validators._ALLOW_REUSE_WARNING_MESSAGE", "name": "_ALLOW_REUSE_WARNING_MESSAGE", "type": "builtins.str"}}, "_OnlyValueValidatorClsMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.deprecated.class_validators._OnlyValueValidatorClsMethod", "name": "_OnlyValueValidatorClsMethod", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.deprecated.class_validators._OnlyValueValidatorClsMethod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.deprecated.class_validators", "mro": ["pydantic.deprecated.class_validators._OnlyValueValidatorClsMethod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "dataclass_transform_spec": null, "flags": ["is_trivial_body", "is_mypy_only"], "fullname": "pydantic.deprecated.class_validators._OnlyValueValidatorClsMethod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "arg_types": ["pydantic.deprecated.class_validators._OnlyValueValidatorClsMethod", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _OnlyValueValidatorClsMethod", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._OnlyValueValidatorClsMethod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.deprecated.class_validators._OnlyValueValidatorClsMethod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PartialClsOrStaticMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod", "line": 58, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.classmethod"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.staticmethod"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "functools.partialmethod"}]}}}, "_V1RootValidatorClsMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", "name": "_V1RootValidatorClsMethod", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.deprecated.class_validators", "mro": ["pydantic.deprecated.class_validators._V1RootValidatorClsMethod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "dataclass_transform_spec": null, "flags": ["is_trivial_body", "is_mypy_only"], "fullname": "pydantic.deprecated.class_validators._V1RootValidatorClsMethod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "arg_types": ["pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators_v1.RootValidatorValues"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _V1RootValidatorClsMethod", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators_v1.RootValidatorValues"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorClsMethod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_V1RootValidatorFunctionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "name": "_V1RootValidatorFunctionType", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}}, "_V1ValidatorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1ValidatorType", "name": "_V1ValidatorType", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators.V1Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}}, "_V1ValidatorWithKwargsClsMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithKwargsClsMethod", "name": "_V1ValidatorWithKwargsClsMethod", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithKwargsClsMethod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.deprecated.class_validators", "mro": ["pydantic.deprecated.class_validators._V1ValidatorWithKwargsClsMethod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 4], "arg_names": ["self", null, "kwargs"], "dataclass_transform_spec": null, "flags": ["is_trivial_body", "is_mypy_only"], "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithKwargsClsMethod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", null, "kwargs"], "arg_types": ["pydantic.deprecated.class_validators._V1ValidatorWithKwargsClsMethod", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _V1ValidatorWithKwargsClsMethod", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithKwargsClsMethod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.deprecated.class_validators._V1ValidatorWithKwargsClsMethod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_V1ValidatorWithValuesAndKwargsClsMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithValuesAndKwargsClsMethod", "name": "_V1ValidatorWithValuesAndKwargsClsMethod", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithValuesAndKwargsClsMethod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.deprecated.class_validators", "mro": ["pydantic.deprecated.class_validators._V1ValidatorWithValuesAndKwargsClsMethod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", null, "values", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_trivial_body", "is_mypy_only"], "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithValuesAndKwargsClsMethod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", null, "values", "kwargs"], "arg_types": ["pydantic.deprecated.class_validators._V1ValidatorWithValuesAndKwargsClsMethod", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _V1ValidatorWithValuesAndKwargsClsMethod", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithValuesAndKwargsClsMethod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.deprecated.class_validators._V1ValidatorWithValuesAndKwargsClsMethod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_V1ValidatorWithValuesClsMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithValuesClsMethod", "name": "_V1ValidatorWithValuesClsMethod", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithValuesClsMethod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.deprecated.class_validators", "mro": ["pydantic.deprecated.class_validators._V1ValidatorWithValuesClsMethod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", null, null, "values"], "dataclass_transform_spec": null, "flags": ["is_trivial_body", "is_mypy_only"], "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithValuesClsMethod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", null, null, "values"], "arg_types": ["pydantic.deprecated.class_validators._V1ValidatorWithValuesClsMethod", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _V1ValidatorWithValuesClsMethod", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithValuesClsMethod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.deprecated.class_validators._V1ValidatorWithValuesClsMethod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_V1ValidatorWithValuesKwOnlyClsMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithValuesKwOnlyClsMethod", "name": "_V1ValidatorWithValuesKwOnlyClsMethod", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithValuesKwOnlyClsMethod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.deprecated.class_validators", "mro": ["pydantic.deprecated.class_validators._V1ValidatorWithValuesKwOnlyClsMethod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 3], "arg_names": ["self", null, null, "values"], "dataclass_transform_spec": null, "flags": ["is_trivial_body", "is_mypy_only"], "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithValuesKwOnlyClsMethod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3], "arg_names": ["self", null, null, "values"], "arg_types": ["pydantic.deprecated.class_validators._V1ValidatorWithValuesKwOnlyClsMethod", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _V1ValidatorWithValuesKwOnlyClsMethod", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1ValidatorWithValuesKwOnlyClsMethod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.deprecated.class_validators._V1ValidatorWithValuesKwOnlyClsMethod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.deprecated.class_validators.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.deprecated.class_validators.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.deprecated.class_validators.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.deprecated.class_validators.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.deprecated.class_validators.__package__", "name": "__package__", "type": "builtins.str"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "_decorators": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators", "kind": "Gdef"}, "_decorators_v1": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators_v1", "kind": "Gdef"}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "partialmethod": {".class": "SymbolTableNode", "cross_ref": "functools.partialmethod", "kind": "Gdef"}, "root_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "pydantic.deprecated.class_validators.root_validator", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5, 5, 5], "arg_names": [null, "pre", "skip_on_failure", "allow_reuse"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "pydantic.deprecated.class_validators.root_validator", "name": "root_validator", "type": {".class": "CallableType", "arg_kinds": [2, 5, 5, 5], "arg_names": [null, "pre", "skip_on_failure", "allow_reuse"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.deprecated.class_validators.root_validator", "name": "root_validator", "type": {".class": "CallableType", "arg_kinds": [2, 5, 5, 5], "arg_names": [null, "pre", "skip_on_failure", "allow_reuse"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 5], "arg_names": ["skip_on_failure", "allow_reuse"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.deprecated.class_validators.root_validator", "name": "root_validator", "type": {".class": "CallableType", "arg_kinds": [3, 5], "arg_names": ["skip_on_failure", "allow_reuse"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.deprecated.class_validators.root_validator", "name": "root_validator", "type": {".class": "CallableType", "arg_kinds": [3, 5], "arg_names": ["skip_on_failure", "allow_reuse"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 5], "arg_names": ["pre", "allow_reuse"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.deprecated.class_validators.root_validator", "name": "root_validator", "type": {".class": "CallableType", "arg_kinds": [3, 5], "arg_names": ["pre", "allow_reuse"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.deprecated.class_validators.root_validator", "name": "root_validator", "type": {".class": "CallableType", "arg_kinds": [3, 5], "arg_names": ["pre", "allow_reuse"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 5], "arg_names": ["pre", "skip_on_failure", "allow_reuse"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.deprecated.class_validators.root_validator", "name": "root_validator", "type": {".class": "CallableType", "arg_kinds": [3, 3, 5], "arg_names": ["pre", "skip_on_failure", "allow_reuse"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.deprecated.class_validators.root_validator", "name": "root_validator", "type": {".class": "CallableType", "arg_kinds": [3, 3, 5], "arg_names": ["pre", "skip_on_failure", "allow_reuse"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [3, 5], "arg_names": ["skip_on_failure", "allow_reuse"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 5], "arg_names": ["pre", "allow_reuse"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 5], "arg_names": ["pre", "skip_on_failure", "allow_reuse"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1RootValidatorFunctionType", "id": -1, "name": "_V1RootValidatorFunctionType", "namespace": "", "upper_bound": "builtins.object", "values": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "pydantic.deprecated.class_validators._V1RootValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5], "arg_names": [null, "fields", "pre", "each_item", "always", "check_fields", "allow_reuse"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "pydantic.deprecated.class_validators.validator", "name": "validator", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5], "arg_names": [null, "fields", "pre", "each_item", "always", "check_fields", "allow_reuse"], "arg_types": ["builtins.str", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1ValidatorType", "id": -1, "name": "_V1ValidatorType", "namespace": "", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators.V1Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1ValidatorType", "id": -1, "name": "_V1ValidatorType", "namespace": "", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators.V1Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1ValidatorType", "id": -1, "name": "_V1ValidatorType", "namespace": "", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators.V1Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.deprecated.class_validators.validator", "name": "validator", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5], "arg_names": [null, "fields", "pre", "each_item", "always", "check_fields", "allow_reuse"], "arg_types": ["builtins.str", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1ValidatorType", "id": -1, "name": "_V1ValidatorType", "namespace": "", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators.V1Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1ValidatorType", "id": -1, "name": "_V1ValidatorType", "namespace": "", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators.V1Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.class_validators._V1ValidatorType", "id": -1, "name": "_V1ValidatorType", "namespace": "", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators.V1Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.class_validators._PartialClsOrStaticMethod"}], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\deprecated\\class_validators.py"}