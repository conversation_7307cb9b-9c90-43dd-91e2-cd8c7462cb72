{"data_mtime": 1757356835, "dep_lines": [7, 11, 8, 1, 1, 1, 1, 1, 10], "dep_prios": [5, 5, 5, 5, 20, 20, 30, 30, 10], "dependencies": ["collections.abc", "urllib3.exceptions", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "abc", "urllib3"], "hash": "9449ae5267f836ebf1c3b6a4ffc0c2a8879172127cf16faa74f6466ac6bf5626", "id": "botocore.exceptions", "ignore_all": true, "interface_hash": "3e9e856d074b33d85a3a7f5c3dd359134d5e19b2cb12a91aa8a4462acbea77b5", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\exceptions.pyi", "plugin_data": null, "size": 21646, "suppressed": ["requests"], "version_id": "1.8.0"}