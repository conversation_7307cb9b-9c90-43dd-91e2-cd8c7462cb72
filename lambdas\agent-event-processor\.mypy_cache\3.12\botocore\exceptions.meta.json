{"data_mtime": 1757364037, "dep_lines": [7, 11, 8, 10, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 30, 30, 30], "dependencies": ["collections.abc", "urllib3.exceptions", "typing", "requests", "builtins", "abc", "requests.exceptions", "urllib3"], "hash": "9449ae5267f836ebf1c3b6a4ffc0c2a8879172127cf16faa74f6466ac6bf5626", "id": "botocore.exceptions", "ignore_all": true, "interface_hash": "d667c83fe9f97143a5bdb25b218e835b32177f05040cd64c6438637a9f81a670", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\exceptions.pyi", "plugin_data": null, "size": 21646, "suppressed": [], "version_id": "1.8.0"}