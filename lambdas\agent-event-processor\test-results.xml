<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="0" tests="47" time="1.810" timestamp="2025-09-08T14:44:20.324389" hostname="HYB-d7HUHw58Q86"><testcase classname="tests.unit.test_database_service.TestRedshiftDataAPIConnection" name="test_init" time="0.005" /><testcase classname="tests.unit.test_database_service.TestRedshiftDataAPIConnection" name="test_cursor_creation" time="0.001" /><testcase classname="tests.unit.test_database_service.TestRedshiftDataAPICursor" name="test_prepare_sql_with_params_no_params" time="0.001" /><testcase classname="tests.unit.test_database_service.TestRedshiftDataAPICursor" name="test_prepare_sql_with_params_with_params" time="0.001" /><testcase classname="tests.unit.test_database_service.TestRedshiftDataAPICursor" name="test_prepare_sql_with_none_params" time="0.001" /><testcase classname="tests.unit.test_database_service.TestRedshiftDataAPICursor" name="test_convert_parameter_value_types" time="0.002" /><testcase classname="tests.unit.test_database_service.TestRedshiftDataAPICursor" name="test_build_request_params" time="0.002" /><testcase classname="tests.unit.test_database_service.TestRedshiftDataAPICursor" name="test_extract_field_value_types" time="0.001" /><testcase classname="tests.unit.test_database_service.TestRedshiftDataAPICursor" name="test_convert_record_to_dict" time="0.001" /><testcase classname="tests.unit.test_database_service.TestRedshiftDataAPICursor" name="test_fetchone_with_results" time="0.001" /><testcase classname="tests.unit.test_database_service.TestRedshiftDataAPICursor" name="test_fetchone_no_results" time="0.001" /><testcase classname="tests.unit.test_database_service.TestRedshiftDataAPICursor" name="test_fetchall" time="0.001" /><testcase classname="tests.unit.test_database_service.TestRedshiftDataAPICursor" name="test_rowcount" time="0.002" /><testcase classname="tests.unit.test_database_service.TestDatabaseService" name="test_init_success" time="0.004" /><testcase classname="tests.unit.test_database_service.TestDatabaseService" name="test_init_missing_settings_attributes" time="0.002" /><testcase classname="tests.unit.test_database_service.TestDatabaseService" name="test_get_connection_context_manager" time="0.005" /><testcase classname="tests.unit.test_database_service.TestDimensionManager" name="test_get_date_key" time="0.002" /><testcase classname="tests.unit.test_database_service.TestDimensionManager" name="test_get_time_key" time="0.002" /><testcase classname="tests.unit.test_database_service.TestFactManager" name="test_init" time="0.002" /><testcase classname="tests.unit.test_event_processor.TestEventProcessor" name="test_repository_dimension_keys" time="0.004" /><testcase classname="tests.unit.test_event_processor.TestEventProcessor" name="test_timestamp_parsing" time="0.102" /><testcase classname="tests.unit.test_event_processor.TestEventProcessor" name="test_process_single_event_success" time="0.006" /><testcase classname="tests.unit.test_event_processor.TestEventProcessor" name="test_process_single_event_failure" time="0.005" /><testcase classname="tests.unit.test_lambda_function.TestLambdaHandler" name="test_successful_login_event_processing" time="0.005" /><testcase classname="tests.unit.test_lambda_function.TestLambdaHandler" name="test_acd_login_event_processing" time="0.003" /><testcase classname="tests.unit.test_lambda_function.TestLambdaHandler" name="test_batch_processing_multiple_events" time="0.005" /><testcase classname="tests.unit.test_lambda_function.TestLambdaHandler" name="test_partial_batch_failure" time="0.016" /><testcase classname="tests.unit.test_lambda_function.TestLambdaHandler" name="test_invalid_xml_handling" time="0.008" /><testcase classname="tests.unit.test_lambda_function.TestLambdaHandler" name="test_empty_records" time="0.003" /><testcase classname="tests.unit.test_lambda_function.TestLambdaHandler" name="test_sns_wrapped_message" time="0.003" /><testcase classname="tests.unit.test_models.TestAgentEvent" name="test_valid_login_event" time="0.001" /><testcase classname="tests.unit.test_models.TestAgentEvent" name="test_valid_acd_login_event" time="0.001" /><testcase classname="tests.unit.test_models.TestAgentEvent" name="test_timestamp_parsing" time="0.001" /><testcase classname="tests.unit.test_models.TestAgentEvent" name="test_invalid_agent_uri" time="0.001" /><testcase classname="tests.unit.test_models.TestAgentEvent" name="test_missing_required_fields" time="0.001" /><testcase classname="tests.unit.test_models.TestAgentEvent" name="test_acd_event_validation" time="0.001" /><testcase classname="tests.unit.test_models.TestAgentEvent" name="test_extra_fields_rejected" time="0.001" /><testcase classname="tests.unit.test_xml_parser.TestAgentEventXMLParser" name="test_parse_login_event" time="0.001" /><testcase classname="tests.unit.test_xml_parser.TestAgentEventXMLParser" name="test_parse_logout_event_with_voice_qos" time="0.001" /><testcase classname="tests.unit.test_xml_parser.TestAgentEventXMLParser" name="test_parse_acd_login_event" time="0.002" /><testcase classname="tests.unit.test_xml_parser.TestAgentEventXMLParser" name="test_parse_agent_available_event" time="0.001" /><testcase classname="tests.unit.test_xml_parser.TestAgentEventXMLParser" name="test_parse_agent_busied_out_event" time="0.001" /><testcase classname="tests.unit.test_xml_parser.TestAgentEventXMLParser" name="test_extract_xml_from_sqs_json_message" time="0.001" /><testcase classname="tests.unit.test_xml_parser.TestAgentEventXMLParser" name="test_extract_xml_from_raw_sqs_message" time="0.001" /><testcase classname="tests.unit.test_xml_parser.TestAgentEventXMLParser" name="test_parse_invalid_xml" time="0.001" /><testcase classname="tests.unit.test_xml_parser.TestAgentEventXMLParser" name="test_parse_xml_missing_required_fields" time="0.002" /><testcase classname="tests.unit.test_xml_parser.TestAgentEventXMLParser" name="test_parse_unknown_event_type" time="0.002" /></testsuite></testsuites>