{".class": "MypyFile", "_fullname": "_pytest.python", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<callable subtype of object>": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python.<callable subtype of object>", "name": "object", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.python.<callable subtype of object>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.python", "mro": ["_pytest.python.<callable subtype of object>", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.<callable subtype of object>.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<callable subtype of object>1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python.<callable subtype of object>1", "name": "object", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.python.<callable subtype of object>1", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.python", "mro": ["_pytest.python.<callable subtype of object>1", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.<callable subtype of object>1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CallSpec2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python.CallSpec2", "name": "CallSpec2", "type_vars": []}, "deletable_attributes": [], "flags": ["is_final"], "fullname": "_pytest.python.CallSpec2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1125, "name": "funcargs", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1128, "name": "params", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1130, "name": "indices", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1132, "name": "_arg2scope", "type": {".class": "Instance", "args": ["builtins.str", "_pytest.scope.Scope"], "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1134, "name": "_idlist", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1136, "name": "marks", "type": {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "type_ref": "builtins.list"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "_pytest.python", "mro": ["_pytest.python.CallSpec2", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "_pytest.python.CallSpec2.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "funcargs", "params", "indices", "_arg2scope", "_idlist", "marks"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.CallSpec2.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "funcargs", "params", "indices", "_arg2scope", "_idlist", "marks"], "arg_types": ["_pytest.python.CallSpec2", {".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.int"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "_pytest.scope.Scope"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CallSpec2", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "_pytest.python.CallSpec2.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "funcargs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "params"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "indices"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "_arg2scope"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "_idlist"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "marks"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["funcargs", "params", "indices", "_arg2scope", "_idlist", "marks"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "_pytest.python.CallSpec2.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["funcargs", "params", "indices", "_arg2scope", "_idlist", "marks"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.int"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "_pytest.scope.Scope"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CallSpec2", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "_pytest.python.CallSpec2.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["funcargs", "params", "indices", "_arg2scope", "_idlist", "marks"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.int"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "_pytest.scope.Scope"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CallSpec2", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_arg2scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "_pytest.python.CallSpec2._arg2scope", "name": "_arg2scope", "type": {".class": "Instance", "args": ["builtins.str", "_pytest.scope.Scope"], "type_ref": "builtins.dict"}}}, "_idlist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "_pytest.python.CallSpec2._idlist", "name": "_idlist", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "funcargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "_pytest.python.CallSpec2.funcargs", "name": "funcargs", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}}}, "getparam": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.CallSpec2.getparam", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["_pytest.python.CallSpec2", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getparam of CallSpec2", "ret_type": "builtins.object", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.python.CallSpec2.id", "name": "id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.CallSpec2"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "id of CallSpec2", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.python.CallSpec2.id", "name": "id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.CallSpec2"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "id of CallSpec2", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "indices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "_pytest.python.CallSpec2.indices", "name": "indices", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "type_ref": "builtins.dict"}}}, "marks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "_pytest.python.CallSpec2.marks", "name": "marks", "type": {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "type_ref": "builtins.list"}}}, "params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "_pytest.python.CallSpec2.params", "name": "params", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}}}, "setmulti": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "valtypes", "argnames", "valset", "id", "marks", "scope", "param_index"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.CallSpec2.setmulti", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "valtypes", "argnames", "valset", "id", "marks", "scope", "param_index"], "arg_types": ["_pytest.python.CallSpec2", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "params"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "funcargs"}]}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.object"], "type_ref": "typing.Iterable"}, "builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.mark.structures.Mark", "_pytest.mark.structures.MarkDecorator"]}], "type_ref": "typing.Iterable"}, "_pytest.scope.Scope", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set<PERSON>lti of CallSpec2", "ret_type": "_pytest.python.CallSpec2", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.python.CallSpec2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.python.CallSpec2", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.python.PyCollector"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python.Class", "name": "Class", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.python.Class", "has_param_spec_type": false, "metaclass_type": "_pytest.nodes.NodeMeta", "metadata": {}, "module_name": "_pytest.python", "mro": ["_pytest.python.Class", "_pytest.python.PyCollector", "_pytest.python.PyobjMixin", "_pytest.nodes.Collector", "_pytest.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "_inject_setup_class_fixture": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Class._inject_setup_class_fixture", "name": "_inject_setup_class_fixture", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.Class"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inject_setup_class_fixture of Class", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_inject_setup_method_fixture": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Class._inject_setup_method_fixture", "name": "_inject_setup_method_fixture", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.Class"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inject_setup_method_fixture of Class", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "collect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Class.collect", "name": "collect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.Class"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collect of Class", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector"]}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "from_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5, 4], "arg_names": ["cls", "parent", "name", "obj", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "_pytest.python.Class.from_parent", "name": "from_parent", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.python.Class.from_parent", "name": "from_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 4], "arg_names": ["cls", "parent", "name", "obj", "kw"], "arg_types": [{".class": "TypeType", "item": "_pytest.python.Class"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_parent of Class", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "newinstance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Class.newinstance", "name": "newinstance", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.python.Class.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.python.Class", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Config": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.Config", "kind": "Gdef"}, "Counter": {".class": "SymbolTableNode", "cross_ref": "collections.Counter", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ExceptionInfo": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.ExceptionInfo", "kind": "Gdef"}, "ExitCode": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.ExitCode", "kind": "Gdef"}, "FuncFixtureInfo": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures.FuncFixtureInfo", "kind": "Gdef"}, "Function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.python.PyobjMixin", "_pytest.nodes.Item"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python.Function", "name": "Function", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.python.Function", "has_param_spec_type": false, "metaclass_type": "_pytest.nodes.NodeMeta", "metadata": {}, "module_name": "_pytest.python", "mro": ["_pytest.python.Function", "_pytest.python.PyobjMixin", "_pytest.nodes.Item", "_pytest.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "_ALLOW_MARKERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.python.Function._ALLOW_MARKERS", "name": "_ALLOW_MARKERS", "type": "builtins.bool"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "parent", "config", "callspec", "<PERSON><PERSON><PERSON>", "keywords", "session", "fixtureinfo", "originalname"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Function.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "parent", "config", "callspec", "<PERSON><PERSON><PERSON>", "keywords", "session", "fixtureinfo", "originalname"], "arg_types": ["_pytest.python.Function", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["_pytest.python.CallSpec2", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["_pytest.main.Session", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["_pytest.fixtures.FuncFixtureInfo", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Function", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_fixtureinfo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.python.Function._fixtureinfo", "name": "_fixtureinfo", "type": "_pytest.fixtures.FuncFixtureInfo"}}, "_getobj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Function._getobj", "name": "_getobj", "type": null}}, "_initrequest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Function._initrequest", "name": "_initrequest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.Function"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_initrequest of Function", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_pyfuncitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.python.Function._pyfuncitem", "name": "_pyfuncitem", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.python.Function._pyfuncitem", "name": "_pyfuncitem", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.Function"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pyfuncitem of Function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.python.Function._request", "name": "_request", "type": "_pytest.fixtures.FixtureRequest"}}, "_traceback_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "excinfo"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Function._traceback_filter", "name": "_traceback_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "excinfo"], "arg_types": ["_pytest.python.Function", {".class": "Instance", "args": ["builtins.BaseException"], "type_ref": "_pytest._code.code.ExceptionInfo"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_traceback_filter of Function", "ret_type": "_pytest._code.code.Traceback", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "callspec": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.python.Function.callspec", "name": "callspec", "type": "_pytest.python.CallSpec2"}}, "fixturenames": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.python.Function.fixturenames", "name": "fixturenames", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "from_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "parent", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "_pytest.python.Function.from_parent", "name": "from_parent", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.python.Function.from_parent", "name": "from_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "parent", "kw"], "arg_types": [{".class": "TypeType", "item": "_pytest.python.Function"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_parent of Function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "funcargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.python.Function.funcargs", "name": "funcargs", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}}}, "function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.python.Function.function", "name": "function", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.python.Function.function", "name": "function", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.Function"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "function of Function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "originalname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.python.Function.originalname", "name": "originalname", "type": "builtins.str"}}, "repr_failure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "excinfo"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Function.repr_failure", "name": "repr_failure", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "excinfo"], "arg_types": ["_pytest.python.Function", {".class": "Instance", "args": ["builtins.BaseException"], "type_ref": "_pytest._code.code.ExceptionInfo"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repr_failure of Function", "ret_type": {".class": "UnionType", "items": ["builtins.str", "_pytest._code.code.TerminalRepr"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "runtest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Function.runtest", "name": "runtest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.Function"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "runtest of Function", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Function.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.Function"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup of Function", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.python.Function.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.python.Function", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FunctionDefinition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.python.Function"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python.FunctionDefinition", "name": "FunctionDefinition", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.python.FunctionDefinition", "has_param_spec_type": false, "metaclass_type": "_pytest.nodes.NodeMeta", "metadata": {}, "module_name": "_pytest.python", "mro": ["_pytest.python.FunctionDefinition", "_pytest.python.Function", "_pytest.python.PyobjMixin", "_pytest.nodes.Item", "_pytest.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "runtest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.FunctionDefinition.runtest", "name": "runtest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.FunctionDefinition"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "runtest of FunctionDefinition", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.python.FunctionDefinition.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.FunctionDefinition"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.python.FunctionDefinition.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.python.FunctionDefinition", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "IGNORED_ATTRIBUTES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.python.IGNORED_ATTRIBUTES", "name": "IGNORED_ATTRIBUTES", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.frozenset"}}}, "INSTANCE_COLLECTOR": {".class": "SymbolTableNode", "cross_ref": "_pytest.deprecated.INSTANCE_COLLECTOR", "kind": "Gdef"}, "IdMaker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python.IdMaker", "name": "IdMaker", "type_vars": []}, "deletable_attributes": [], "flags": ["is_final"], "fullname": "_pytest.python.IdMaker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 966, "name": "argnames", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 968, "name": "parametersets", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.mark.structures.ParameterSet"}], "type_ref": "typing.Sequence"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 971, "name": "idfn", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 973, "name": "ids", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 977, "name": "config", "type": {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 980, "name": "nodeid", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 983, "name": "func_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "_pytest.python", "mro": ["_pytest.python.IdMaker", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "_pytest.python.IdMaker.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "argnames", "parametersets", "idfn", "ids", "config", "nodeid", "func_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.IdMaker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "argnames", "parametersets", "idfn", "ids", "config", "nodeid", "func_name"], "arg_types": ["_pytest.python.IdMaker", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.mark.structures.ParameterSet"}], "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IdMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "_pytest.python.IdMaker.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "argnames"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "parametersets"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "idfn"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ids"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nodeid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "func_name"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["argnames", "parametersets", "idfn", "ids", "config", "nodeid", "func_name"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "_pytest.python.IdMaker.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["argnames", "parametersets", "idfn", "ids", "config", "nodeid", "func_name"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.mark.structures.ParameterSet"}], "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of IdMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "_pytest.python.IdMaker.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["argnames", "parametersets", "idfn", "ids", "config", "nodeid", "func_name"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.mark.structures.ParameterSet"}], "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of IdMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_pytest.python.IdMaker.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_idval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "val", "argname", "idx"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.IdMaker._idval", "name": "_idval", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "val", "argname", "idx"], "arg_types": ["_pytest.python.IdMaker", "builtins.object", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_idval of IdMaker", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_idval_from_argname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["argname", "idx"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "_pytest.python.IdMaker._idval_from_argname", "name": "_idval_from_argname", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["argname", "idx"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_idval_from_argname of IdMaker", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "_pytest.python.IdMaker._idval_from_argname", "name": "_idval_from_argname", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["argname", "idx"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_idval_from_argname of IdMaker", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_idval_from_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "val", "argname", "idx"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.IdMaker._idval_from_function", "name": "_idval_from_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "val", "argname", "idx"], "arg_types": ["_pytest.python.IdMaker", "builtins.object", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_idval_from_function of IdMaker", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_idval_from_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "val", "argname"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.IdMaker._idval_from_hook", "name": "_idval_from_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "val", "argname"], "arg_types": ["_pytest.python.IdMaker", "builtins.object", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_idval_from_hook of IdMaker", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_idval_from_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.IdMaker._idval_from_value", "name": "_idval_from_value", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["_pytest.python.IdMaker", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_idval_from_value of IdMaker", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_idval_from_value_required": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "val", "idx"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.IdMaker._idval_from_value_required", "name": "_idval_from_value_required", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "val", "idx"], "arg_types": ["_pytest.python.IdMaker", "builtins.object", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_idval_from_value_required of IdMaker", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.IdMaker._resolve_ids", "name": "_resolve_ids", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.IdMaker"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_ids of IdMaker", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "argnames": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.python.IdMaker.argnames", "name": "argnames", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.python.IdMaker.config", "name": "config", "type": {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}]}}}, "func_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.python.IdMaker.func_name", "name": "func_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "idfn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.python.IdMaker.idfn", "name": "idfn", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.python.IdMaker.ids", "name": "ids", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}}}, "make_unique_parameterset_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.IdMaker.make_unique_parameterset_ids", "name": "make_unique_parameterset_ids", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.IdMaker"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_unique_parameterset_ids of IdMaker", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "nodeid": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.python.IdMaker.nodeid", "name": "nodeid", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "parametersets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.python.IdMaker.parametersets", "name": "parametersets", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.mark.structures.ParameterSet"}], "type_ref": "typing.Sequence"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.python.IdMaker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.python.IdMaker", "values": [], "variance": 0}, "slots": ["argnames", "config", "func_name", "idfn", "ids", "nodeid", "parametersets"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImportPathMismatchError": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.ImportPathMismatchError", "kind": "Gdef"}, "InstanceDummy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python.InstanceDummy", "name": "InstanceDummy", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.python.InstanceDummy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.python", "mro": ["_pytest.python.InstanceDummy", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.python.InstanceDummy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.python.InstanceDummy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "LEGACY_PATH": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.LEGACY_PATH", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "MARK_GEN": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.MARK_GEN", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Mark": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.Mark", "kind": "Gdef"}, "MarkDecorator": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.MarkDecorator", "kind": "Gdef"}, "Metafunc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python.Metafunc", "name": "Metafunc", "type_vars": []}, "deletable_attributes": [], "flags": ["is_final"], "fullname": "_pytest.python.Metafunc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.python", "mro": ["_pytest.python.Metafunc", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 5], "arg_names": ["self", "definition", "fixtureinfo", "config", "cls", "module", "_ispytest"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Metafunc.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 5], "arg_names": ["self", "definition", "fixtureinfo", "config", "cls", "module", "_ispytest"], "arg_types": ["_pytest.python.Metafunc", "_pytest.python.FunctionDefinition", "_pytest.fixtures.FuncFixtureInfo", "_pytest.config.Config", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Metafunc", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_arg2fixturedefs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.python.Metafunc._arg2fixturedefs", "name": "_arg2fixturedefs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "_pytest.fixtures.FixtureDef"}], "type_ref": "typing.Sequence"}], "type_ref": "builtins.dict"}}}, "_calls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.python.Metafunc._calls", "name": "_calls", "type": {".class": "Instance", "args": ["_pytest.python.CallSpec2"], "type_ref": "builtins.list"}}}, "_resolve_arg_value_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "argnames", "indirect"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Metafunc._resolve_arg_value_types", "name": "_resolve_arg_value_types", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "argnames", "indirect"], "arg_types": ["_pytest.python.Metafunc", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_arg_value_types of Metafunc", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "params"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "funcargs"}]}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_parameter_set_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "argnames", "ids", "parametersets", "nodeid"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Metafunc._resolve_parameter_set_ids", "name": "_resolve_parameter_set_ids", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "argnames", "ids", "parametersets", "nodeid"], "arg_types": ["_pytest.python.Metafunc", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}], "type_ref": "typing.Iterable"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.mark.structures.ParameterSet"}], "type_ref": "typing.Sequence"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_parameter_set_ids of Metafunc", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_validate_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ids", "parametersets", "func_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Metafunc._validate_ids", "name": "_validate_ids", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ids", "parametersets", "func_name"], "arg_types": ["_pytest.python.Metafunc", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.mark.structures.ParameterSet"}], "type_ref": "typing.Sequence"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_ids of Metafunc", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_validate_if_using_arg_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "argnames", "indirect"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Metafunc._validate_if_using_arg_names", "name": "_validate_if_using_arg_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "argnames", "indirect"], "arg_types": ["_pytest.python.Metafunc", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_if_using_arg_names of Metafunc", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.python.Metafunc.cls", "name": "cls", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.python.Metafunc.config", "name": "config", "type": "_pytest.config.Config"}}, "definition": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.python.Metafunc.definition", "name": "definition", "type": "_pytest.python.FunctionDefinition"}}, "fixturenames": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.python.Metafunc.fixturenames", "name": "fixturenames", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "function": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.python.Metafunc.function", "name": "function", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "module": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.python.Metafunc.module", "name": "module", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "parametrize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 5], "arg_names": ["self", "argnames", "arg<PERSON><PERSON>", "indirect", "ids", "scope", "_param_mark"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Metafunc.parametrize", "name": "parametrize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 5], "arg_names": ["self", "argnames", "arg<PERSON><PERSON>", "indirect", "ids", "scope", "_param_mark"], "arg_types": ["_pytest.python.Metafunc", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.mark.structures.ParameterSet"}, {".class": "Instance", "args": ["builtins.object"], "type_ref": "typing.Sequence"}, "builtins.object"]}], "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}], "type_ref": "typing.Iterable"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "session"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "package"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "module"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "class"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "function"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["_pytest.mark.structures.Mark", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parametrize of Metafunc", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.python.Metafunc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.python.Metafunc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Module": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.nodes.File", "_pytest.python.PyCollector"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python.Module", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.python.Module", "has_param_spec_type": false, "metaclass_type": "_pytest.nodes.NodeMeta", "metadata": {}, "module_name": "_pytest.python", "mro": ["_pytest.python.Module", "_pytest.nodes.File", "_pytest.nodes.FSCollector", "_pytest.python.PyCollector", "_pytest.python.PyobjMixin", "_pytest.nodes.Collector", "_pytest.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "_getobj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Module._getobj", "name": "_getobj", "type": null}}, "_importtestmodule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Module._importtestmodule", "name": "_importtestmodule", "type": null}}, "_inject_setup_function_fixture": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Module._inject_setup_function_fixture", "name": "_inject_setup_function_fixture", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.Module"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inject_setup_function_fixture of Module", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_inject_setup_module_fixture": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Module._inject_setup_module_fixture", "name": "_inject_setup_module_fixture", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.Module"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inject_setup_module_fixture of Module", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "collect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Module.collect", "name": "collect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.Module"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collect of Module", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector"]}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.python.Module.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.python.Module", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NOSE_SUPPORT_METHOD": {".class": "SymbolTableNode", "cross_ref": "_pytest.deprecated.NOSE_SUPPORT_METHOD", "kind": "Gdef"}, "NOTSET": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.NOTSET", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Package": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.python.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python.Package", "name": "Package", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.python.Package", "has_param_spec_type": false, "metaclass_type": "_pytest.nodes.NodeMeta", "metadata": {}, "module_name": "_pytest.python", "mro": ["_pytest.python.Package", "_pytest.python.Module", "_pytest.nodes.File", "_pytest.nodes.FSCollector", "_pytest.python.PyCollector", "_pytest.python.PyobjMixin", "_pytest.nodes.Collector", "_pytest.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "fspath", "parent", "config", "session", "nodeid", "path"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Package.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "fspath", "parent", "config", "session", "nodeid", "path"], "arg_types": ["_pytest.python.Package", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}]}, "_pytest.nodes.Collector", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Package", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_collectfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "fspath", "handle_dupes"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Package._collectfile", "name": "_collectfile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "fspath", "handle_dupes"], "arg_types": ["_pytest.python.Package", "pathlib.Path", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_collectfile of Package", "ret_type": {".class": "Instance", "args": ["_pytest.nodes.Collector"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_recurse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "direntry"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Package._recurse", "name": "_recurse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "direntry"], "arg_types": ["_pytest.python.Package", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.<PERSON><PERSON><PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_recurse of Package", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "collect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Package.collect", "name": "collect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.Package"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collect of Package", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector"]}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.Package.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.Package"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup of Package", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.python.Package.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.python.Package", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParameterSet": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.ParameterSet", "kind": "Gdef"}, "Parser": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.argparsing.Parser", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef"}, "PyCollector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.python.PyobjMixin", "_pytest.nodes.Collector"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python.PyCollector", "name": "PyCollector", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.python.PyCollector", "has_param_spec_type": false, "metaclass_type": "_pytest.nodes.NodeMeta", "metadata": {}, "module_name": "_pytest.python", "mro": ["_pytest.python.PyCollector", "_pytest.python.PyobjMixin", "_pytest.nodes.Collector", "_pytest.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "_genfunctions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "funcobj"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.PyCollector._genfunctions", "name": "_genfunctions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "funcobj"], "arg_types": ["_pytest.python.PyCollector", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_genfunctions of PyCollector", "ret_type": {".class": "Instance", "args": ["_pytest.python.Function"], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_matches_prefix_or_glob_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "option_name", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.PyCollector._matches_prefix_or_glob_option", "name": "_matches_prefix_or_glob_option", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "option_name", "name"], "arg_types": ["_pytest.python.PyCollector", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_matches_prefix_or_glob_option of PyCollector", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "classnamefilter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.PyCollector.classnamefilter", "name": "classnamefilter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["_pytest.python.PyCollector", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "classnamefilter of PyCollector", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "collect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.PyCollector.collect", "name": "collect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.PyCollector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collect of PyCollector", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector"]}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "funcnamefilter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.PyCollector.funcnamefilter", "name": "funcnamefilter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["_pytest.python.PyCollector", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "funcnamefilter of PyCollector", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "isnosetest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.PyCollector.isnosetest", "name": "isnosetest", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["_pytest.python.PyCollector", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isnosetest of PyCollector", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "istestclass": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.PyCollector.istestclass", "name": "istestclass", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "name"], "arg_types": ["_pytest.python.PyCollector", "builtins.object", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "istestclass of PyCollector", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "istestfunction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.PyCollector.istestfunction", "name": "istestfunction", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "name"], "arg_types": ["_pytest.python.PyCollector", "builtins.object", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "istestfunction of PyCollector", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.python.PyCollector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.python.PyCollector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PyobjMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.nodes.Node"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python.PyobjMixin", "name": "PyobjMixin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.python.PyobjMixin", "has_param_spec_type": false, "metaclass_type": "_pytest.nodes.NodeMeta", "metadata": {}, "module_name": "_pytest.python", "mro": ["_pytest.python.PyobjMixin", "_pytest.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "_ALLOW_MARKERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_pytest.python.PyobjMixin._ALLOW_MARKERS", "name": "_ALLOW_MARKERS", "type": "builtins.bool"}}, "_getobj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.PyobjMixin._getobj", "name": "_getobj", "type": null}}, "_obj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.python.PyobjMixin._obj", "name": "_obj", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.python.PyobjMixin.cls", "name": "cls", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.python.PyobjMixin.cls", "name": "cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.PyobjMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cls of PyobjMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "getmodpath": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "stopatmodule", "<PERSON><PERSON>dule"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.PyobjMixin.getmodpath", "name": "getmodpath", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "stopatmodule", "<PERSON><PERSON>dule"], "arg_types": ["_pytest.python.PyobjMixin", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getmodpath of PyobjMixin", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.python.PyobjMixin.instance", "name": "instance", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.python.PyobjMixin.instance", "name": "instance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.PyobjMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance of PyobjMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.python.PyobjMixin.module", "name": "module", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.python.PyobjMixin.module", "name": "module", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.PyobjMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module of PyobjMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "obj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_property"], "fullname": "_pytest.python.PyobjMixin.obj", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "_pytest.python.PyobjMixin.obj", "name": "obj", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "_pytest.python.PyobjMixin.obj", "name": "obj", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.PyobjMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj of PyobjMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.python.PyobjMixin.obj", "name": "obj", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "obj", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.PyobjMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "reportinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.PyobjMixin.reportinfo", "name": "reportinfo", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.python.PyobjMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reportinfo of PyobjMixin", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}, "builtins.str"]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.python.PyobjMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.python.PyobjMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestCollectionWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestCollectionWarning", "kind": "Gdef"}, "PytestReturnNotNoneWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestReturnNotNoneWarning", "kind": "Gdef"}, "PytestUnhandledCoroutineWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestUnhandledCoroutineWarning", "kind": "Gdef"}, "STRING_TYPES": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.STRING_TYPES", "kind": "Gdef"}, "Scope": {".class": "SymbolTableNode", "cross_ref": "_pytest.scope.Scope", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "_pytest.main.Session", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TerminalRepr": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.TerminalRepr", "kind": "Gdef"}, "TerminalWriter": {".class": "SymbolTableNode", "cross_ref": "_pytest._io.terminalwriter.TerminalWriter", "kind": "Gdef"}, "Traceback": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.Traceback", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_EmptyClass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.python._EmptyClass", "name": "_EmptyClass", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.python._EmptyClass", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.python", "mro": ["_pytest.python._EmptyClass", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.python._EmptyClass.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.python._EmptyClass", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PYTEST_DIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.python._PYTEST_DIR", "name": "_PYTEST_DIR", "type": "pathlib.Path"}}, "_ScopeName": {".class": "SymbolTableNode", "cross_ref": "_pytest.scope._ScopeName", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.python.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.python.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.python.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": "builtins.object", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.python.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.python.__package__", "name": "__package__", "type": "builtins.str"}}, "_ascii_escaped_by_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["val", "config"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python._ascii_escaped_by_config", "name": "_ascii_escaped_by_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["val", "config"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}, {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ascii_escaped_by_config", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_call_with_optional_argument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["func", "arg"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python._call_with_optional_argument", "name": "_call_with_optional_argument", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["func", "arg"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_call_with_optional_argument", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_find_parametrized_scope": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["argnames", "arg2fixturedefs", "indirect"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python._find_parametrized_scope", "name": "_find_parametrized_scope", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["argnames", "arg2fixturedefs", "indirect"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.object"], "type_ref": "_pytest.fixtures.FixtureDef"}], "type_ref": "typing.Sequence"}], "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_parametrized_scope", "ret_type": "_pytest.scope.Scope", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_first_non_fixture_func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["obj", "names"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python._get_first_non_fixture_func", "name": "_get_first_non_fixture_func", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["obj", "names"], "arg_types": ["builtins.object", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_first_non_fixture_func", "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_pretty_fixture_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python._pretty_fixture_path", "name": "_pretty_fixture_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pretty_fixture_path", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_pytest": {".class": "SymbolTableNode", "cross_ref": "_pytest", "kind": "Gdef"}, "_show_fixtures_per_test": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "session"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python._show_fixtures_per_test", "name": "_show_fixtures_per_test", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "session"], "arg_types": ["_pytest.config.Config", "_pytest.main.Session"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_show_fixtures_per_test", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_showfixtures_main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "session"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python._showfixtures_main", "name": "_showfixtures_main", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "session"], "arg_types": ["_pytest.config.Config", "_pytest.main.Session"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_showfixtures_main", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "ascii_escaped": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.ascii_escaped", "kind": "Gdef"}, "assert_never": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.assert_never", "kind": "Gdef"}, "async_warn_and_skip": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["nodeid"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.async_warn_and_skip", "name": "async_warn_and_skip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["nodeid"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "async_warn_and_skip", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "bestrelpath": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.bestrelpath", "kind": "Gdef"}, "check_ispytest": {".class": "SymbolTableNode", "cross_ref": "_pytest.deprecated.check_ispytest", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "fail": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.fail", "kind": "Gdef"}, "filter_traceback": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.filter_traceback", "kind": "Gdef"}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef"}, "fixtures": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures", "kind": "Gdef"}, "fnmatch": {".class": "SymbolTableNode", "cross_ref": "fnmatch", "kind": "Gdef"}, "fnmatch_ex": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.fnmatch_ex", "kind": "Gdef"}, "get_default_arg_names": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.get_default_arg_names", "kind": "Gdef"}, "get_real_func": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.get_real_func", "kind": "Gdef"}, "get_unpacked_marks": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.get_unpacked_marks", "kind": "Gdef"}, "getfslineno": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.getfslineno", "kind": "Gdef"}, "getimfunc": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.getimfunc", "kind": "Gdef"}, "getlocation": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.getlocation", "kind": "Gdef"}, "hasinit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.hasinit", "name": "<PERSON><PERSON>t", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON>t", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "hasnew": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.hasnew", "name": "<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "hookimpl": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.hookimpl", "kind": "Gdef"}, "import_path": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.import_path", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_async_function": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.is_async_function", "kind": "Gdef"}, "is_generator": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.is_generator", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "nodes": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes", "kind": "Gdef"}, "normalize_mark_list": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.normalize_mark_list", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "parts": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.parts", "kind": "Gdef"}, "path_matches_patterns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["path", "patterns"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.path_matches_patterns", "name": "path_matches_patterns", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["path", "patterns"], "arg_types": ["pathlib.Path", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path_matches_patterns", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pytest_addoption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["parser"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.pytest_addoption", "name": "pytest_addoption", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["parser"], "arg_types": ["_pytest.config.argparsing.Parser"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_addoption", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pytest_cmdline_main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.pytest_cmdline_main", "name": "pytest_cmdline_main", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_cmdline_main", "ret_type": {".class": "UnionType", "items": ["builtins.int", "_pytest.config.ExitCode", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pytest_collect_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["file_path", "parent"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.pytest_collect_file", "name": "pytest_collect_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["file_path", "parent"], "arg_types": ["pathlib.Path", "_pytest.nodes.Collector"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_collect_file", "ret_type": {".class": "UnionType", "items": ["_pytest.python.Module", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pytest_configure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.pytest_configure", "name": "pytest_configure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_configure", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pytest_generate_tests": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["metafunc"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.pytest_generate_tests", "name": "pytest_generate_tests", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["metafunc"], "arg_types": ["_pytest.python.Metafunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_generate_tests", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pytest_pycollect_makeitem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["collector", "name", "obj"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.python.pytest_pycollect_makeitem", "name": "pytest_pycollect_makeitem", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["collector", "name", "obj"], "arg_types": [{".class": "UnionType", "items": ["_pytest.python.Module", "_pytest.python.Class"]}, "builtins.str", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_pycollect_makeitem", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "_pytest.nodes.Item", "_pytest.nodes.Collector", {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector"]}], "type_ref": "builtins.list"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.python.pytest_pycollect_makeitem", "name": "pytest_pycollect_makeitem", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["collector", "name", "obj"], "arg_types": [{".class": "UnionType", "items": ["_pytest.python.Module", "_pytest.python.Class"]}, "builtins.str", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_pycollect_makeitem", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "_pytest.nodes.Item", "_pytest.nodes.Collector", {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector"]}], "type_ref": "builtins.list"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_pycollect_makemodule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module_path", "parent"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.pytest_pycollect_makemodule", "name": "pytest_pycollect_makemodule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["module_path", "parent"], "arg_types": ["pathlib.Path", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_pycollect_makemodule", "ret_type": "_pytest.python.Module", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pytest_pyfunc_call": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pyfuncitem"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.python.pytest_pyfunc_call", "name": "pytest_pyfunc_call", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pyfuncitem"], "arg_types": ["_pytest.python.Function"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_pyfunc_call", "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.python.pytest_pyfunc_call", "name": "pytest_pyfunc_call", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pyfuncitem"], "arg_types": ["_pytest.python.Function"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_pyfunc_call", "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "safe_getattr": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.safe_getattr", "kind": "Gdef"}, "safe_isclass": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.safe_isclass", "kind": "Gdef"}, "saferepr": {".class": "SymbolTableNode", "cross_ref": "_pytest._io.saferepr.saferepr", "kind": "Gdef"}, "show_fixtures_per_test": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.show_fixtures_per_test", "name": "show_fixtures_per_test", "type": null}}, "showfixtures": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.showfixtures", "name": "showfixtures", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "showfixtures", "ret_type": {".class": "UnionType", "items": ["builtins.int", "_pytest.config.ExitCode"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "skip": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.skip", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "visit": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.visit", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "write_docstring": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["tw", "doc", "indent"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.python.write_docstring", "name": "write_docstring", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["tw", "doc", "indent"], "arg_types": ["_pytest._io.terminalwriter.TerminalWriter", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_docstring", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\python.py"}