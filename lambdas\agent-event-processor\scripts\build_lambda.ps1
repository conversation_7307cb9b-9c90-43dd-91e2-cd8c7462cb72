#!/usr/bin/env pwsh
# PowerShell script to build Lambda function for LocalStack deployment

param(
    [string]$OutputDir = "build",
    [string]$ZipName = "agent-event-processor.zip",
    [switch]$Clean = $false
)

$ErrorActionPreference = "Stop"

Write-Host "Building Lambda function for LocalStack deployment..." -ForegroundColor Green

# Get script directory and project root
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition
$ProjectDir = Split-Path -Parent $ScriptDir
$BuildDir = Join-Path $ProjectDir $OutputDir

# Clean build directory if requested
if ($Clean -and (Test-Path $BuildDir)) {
    Write-Host "Cleaning build directory..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force $BuildDir
}

# Create build directory
if (-not (Test-Path $BuildDir)) {
    New-Item -ItemType Directory -Path $BuildDir | Out-Null
}

Write-Host "Build directory: $BuildDir" -ForegroundColor Cyan

# Copy source code
Write-Host "Copying source code..." -ForegroundColor Yellow
$SourceDir = Join-Path $ProjectDir "src"
$TargetSourceDir = Join-Path $BuildDir "src"

if (Test-Path $TargetSourceDir) {
    Remove-Item -Recurse -Force $TargetSourceDir
}

Copy-Item -Recurse $SourceDir $TargetSourceDir

# Install dependencies
Write-Host "Installing dependencies..." -ForegroundColor Yellow
$RequirementsFile = Join-Path $ProjectDir "requirements.txt"

if (-not (Test-Path $RequirementsFile)) {
    Write-Error "Requirements file not found: $RequirementsFile"
    exit 1
}

# Install to build directory
Push-Location $BuildDir
try {
    # Use pip to install dependencies to current directory
    python -m pip install -r $RequirementsFile -t . --no-deps --upgrade
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to install dependencies"
        exit 1
    }
} finally {
    Pop-Location
}

# Create deployment package
Write-Host "Creating deployment package..." -ForegroundColor Yellow
$ZipPath = Join-Path $ProjectDir $ZipName

if (Test-Path $ZipPath) {
    Remove-Item $ZipPath
}

# Create zip file
Push-Location $BuildDir
try {
    # Use PowerShell's Compress-Archive
    $FilesToZip = Get-ChildItem -Recurse | Where-Object { -not $_.PSIsContainer }
    
    # Create relative paths for the zip
    $ZipEntries = @()
    foreach ($file in $FilesToZip) {
        $relativePath = $file.FullName.Substring($BuildDir.Length + 1)
        $ZipEntries += @{
            Path = $file.FullName
            DestinationPath = $relativePath
        }
    }
    
    # Use 7zip if available, otherwise use PowerShell
    if (Get-Command 7z -ErrorAction SilentlyContinue) {
        Write-Host "Using 7zip for compression..." -ForegroundColor Cyan
        7z a -tzip $ZipPath * -r
    } else {
        Write-Host "Using PowerShell compression..." -ForegroundColor Cyan
        Compress-Archive -Path * -DestinationPath $ZipPath -CompressionLevel Optimal
    }
    
    if ($LASTEXITCODE -ne 0 -and $LASTEXITCODE -ne $null) {
        Write-Error "Failed to create zip file"
        exit 1
    }
} finally {
    Pop-Location
}

# Verify the package
if (Test-Path $ZipPath) {
    $ZipSize = (Get-Item $ZipPath).Length
    Write-Host "Lambda package created successfully!" -ForegroundColor Green
    Write-Host "Package: $ZipPath" -ForegroundColor Cyan
    Write-Host "Size: $([math]::Round($ZipSize / 1MB, 2)) MB" -ForegroundColor Cyan
    
    # List contents
    Write-Host "`nPackage contents:" -ForegroundColor Yellow
    if (Get-Command 7z -ErrorAction SilentlyContinue) {
        7z l $ZipPath | Select-Object -Last 20
    } else {
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        $zip = [System.IO.Compression.ZipFile]::OpenRead($ZipPath)
        $zip.Entries | Select-Object -First 10 | Format-Table Name, Length
        $zip.Dispose()
    }
} else {
    Write-Error "Failed to create Lambda package"
    exit 1
}

Write-Host "`nBuild completed successfully!" -ForegroundColor Green
Write-Host "You can now deploy this package to LocalStack using:" -ForegroundColor Cyan
Write-Host "  docker-compose up -d" -ForegroundColor White
Write-Host "  .\scripts\setup_localstack_complete.ps1" -ForegroundColor White
