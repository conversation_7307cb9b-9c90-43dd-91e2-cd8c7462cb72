{".class": "MypyFile", "_fullname": "rich.box", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASCII": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.ASCII", "name": "ASCII", "type": "rich.box.Box"}}, "ASCII2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.ASCII2", "name": "ASCII2", "type": "rich.box.Box"}}, "ASCII_DOUBLE_HEAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.ASCII_DOUBLE_HEAD", "name": "ASCII_DOUBLE_HEAD", "type": "rich.box.Box"}}, "BOXES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.box.BOXES", "name": "BOXES", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "Box": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.box.Box", "name": "Box", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "rich.box.Box", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.box", "mro": ["rich.box.Box", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "box", "ascii"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.box.Box.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "box", "ascii"], "arg_types": ["rich.box.Box", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Box", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.box.Box.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["rich.box.Box"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Box", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.box.Box.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["rich.box.Box"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of Box", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_box": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box._box", "name": "_box", "type": "builtins.str"}}, "ascii": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.ascii", "name": "ascii", "type": "builtins.bool"}}, "bottom": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.bottom", "name": "bottom", "type": "builtins.str"}}, "bottom_divider": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.bottom_divider", "name": "bottom_divider", "type": "builtins.str"}}, "bottom_left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.bottom_left", "name": "bottom_left", "type": "builtins.str"}}, "bottom_right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.bottom_right", "name": "bottom_right", "type": "builtins.str"}}, "foot_left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.foot_left", "name": "foot_left", "type": "builtins.str"}}, "foot_right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.foot_right", "name": "foot_right", "type": "builtins.str"}}, "foot_row_cross": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.foot_row_cross", "name": "foot_row_cross", "type": "builtins.str"}}, "foot_row_horizontal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.foot_row_horizontal", "name": "foot_row_horizontal", "type": "builtins.str"}}, "foot_row_left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.foot_row_left", "name": "foot_row_left", "type": "builtins.str"}}, "foot_row_right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.foot_row_right", "name": "foot_row_right", "type": "builtins.str"}}, "foot_vertical": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.foot_vertical", "name": "foot_vertical", "type": "builtins.str"}}, "get_bottom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "widths"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.box.Box.get_bottom", "name": "get_bottom", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "widths"], "arg_types": ["rich.box.Box", {".class": "Instance", "args": ["builtins.int"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bottom of Box", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_plain_headed_box": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.box.Box.get_plain_headed_box", "name": "get_plain_headed_box", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.box.Box"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_plain_headed_box of Box", "ret_type": "rich.box.Box", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_row": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "widths", "level", "edge"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.box.Box.get_row", "name": "get_row", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "widths", "level", "edge"], "arg_types": ["rich.box.Box", {".class": "Instance", "args": ["builtins.int"], "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "head"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "row"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "foot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mid"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_row of Box", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_top": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "widths"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.box.Box.get_top", "name": "get_top", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "widths"], "arg_types": ["rich.box.Box", {".class": "Instance", "args": ["builtins.int"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_top of Box", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "head_left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.head_left", "name": "head_left", "type": "builtins.str"}}, "head_right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.head_right", "name": "head_right", "type": "builtins.str"}}, "head_row_cross": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.head_row_cross", "name": "head_row_cross", "type": "builtins.str"}}, "head_row_horizontal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.head_row_horizontal", "name": "head_row_horizontal", "type": "builtins.str"}}, "head_row_left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.head_row_left", "name": "head_row_left", "type": "builtins.str"}}, "head_row_right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.head_row_right", "name": "head_row_right", "type": "builtins.str"}}, "head_vertical": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.head_vertical", "name": "head_vertical", "type": "builtins.str"}}, "mid_left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.mid_left", "name": "mid_left", "type": "builtins.str"}}, "mid_right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.mid_right", "name": "mid_right", "type": "builtins.str"}}, "mid_vertical": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.mid_vertical", "name": "mid_vertical", "type": "builtins.str"}}, "row_cross": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.row_cross", "name": "row_cross", "type": "builtins.str"}}, "row_horizontal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.row_horizontal", "name": "row_horizontal", "type": "builtins.str"}}, "row_left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.row_left", "name": "row_left", "type": "builtins.str"}}, "row_right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.row_right", "name": "row_right", "type": "builtins.str"}}, "substitute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "options", "safe"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.box.Box.substitute", "name": "substitute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "options", "safe"], "arg_types": ["rich.box.Box", "rich.console.ConsoleOptions", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "substitute of <PERSON>", "ret_type": "rich.box.Box", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "top": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.top", "name": "top", "type": "builtins.str"}}, "top_divider": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.top_divider", "name": "top_divider", "type": "builtins.str"}}, "top_left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.top_left", "name": "top_left", "type": "builtins.str"}}, "top_right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.Box.top_right", "name": "top_right", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.box.Box.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.box.Box", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Columns": {".class": "SymbolTableNode", "cross_ref": "rich.columns.Columns", "kind": "Gdef"}, "Console": {".class": "SymbolTableNode", "cross_ref": "rich.console.Console", "kind": "Gdef"}, "ConsoleOptions": {".class": "SymbolTableNode", "cross_ref": "rich.console.ConsoleOptions", "kind": "Gdef"}, "DOUBLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.DOUBLE", "name": "DOUBLE", "type": "rich.box.Box"}}, "DOUBLE_EDGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.DOUBLE_EDGE", "name": "DOUBLE_EDGE", "type": "rich.box.Box"}}, "HEAVY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.HEAVY", "name": "HEAVY", "type": "rich.box.Box"}}, "HEAVY_EDGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.HEAVY_EDGE", "name": "HEAVY_EDGE", "type": "rich.box.Box"}}, "HEAVY_HEAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.HEAVY_HEAD", "name": "HEAVY_HEAD", "type": "rich.box.Box"}}, "HORIZONTALS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.HORIZONTALS", "name": "HORIZONTALS", "type": "rich.box.Box"}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LEGACY_WINDOWS_SUBSTITUTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.box.LEGACY_WINDOWS_SUBSTITUTIONS", "name": "LEGACY_WINDOWS_SUBSTITUTIONS", "type": {".class": "Instance", "args": ["rich.box.Box", "rich.box.Box"], "type_ref": "builtins.dict"}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MARKDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.MARKDOWN", "name": "MARKDOWN", "type": "rich.box.Box"}}, "MINIMAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.MINIMAL", "name": "MINIMAL", "type": "rich.box.Box"}}, "MINIMAL_DOUBLE_HEAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.MINIMAL_DOUBLE_HEAD", "name": "MINIMAL_DOUBLE_HEAD", "type": "rich.box.Box"}}, "MINIMAL_HEAVY_HEAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.MINIMAL_HEAVY_HEAD", "name": "MINIMAL_HEAVY_HEAD", "type": "rich.box.Box"}}, "PLAIN_HEADED_SUBSTITUTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.box.PLAIN_HEADED_SUBSTITUTIONS", "name": "PLAIN_HEADED_SUBSTITUTIONS", "type": {".class": "Instance", "args": ["rich.box.Box", "rich.box.Box"], "type_ref": "builtins.dict"}}}, "Panel": {".class": "SymbolTableNode", "cross_ref": "rich.panel.Panel", "kind": "Gdef"}, "ROUNDED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.ROUNDED", "name": "ROUNDED", "type": "rich.box.Box"}}, "SIMPLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.SIMPLE", "name": "SIMPLE", "type": "rich.box.Box"}}, "SIMPLE_HEAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.SIMPLE_HEAD", "name": "SIMPLE_HEAD", "type": "rich.box.Box"}}, "SIMPLE_HEAVY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.SIMPLE_HEAVY", "name": "SIMPLE_HEAVY", "type": "rich.box.Box"}}, "SQUARE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.SQUARE", "name": "SQUARE", "type": "rich.box.Box"}}, "SQUARE_DOUBLE_HEAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.box.SQUARE_DOUBLE_HEAD", "name": "SQUARE_DOUBLE_HEAD", "type": "rich.box.Box"}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "rich.table.Table", "kind": "Gdef"}, "Text": {".class": "SymbolTableNode", "cross_ref": "rich.text.Text", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.box.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.box.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.box.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.box.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.box.__package__", "name": "__package__", "type": "builtins.str"}}, "box": {".class": "SymbolTableNode", "cross_ref": "rich.box", "kind": "Gdef"}, "box_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.box.box_name", "name": "box_name", "type": "builtins.str"}}, "columns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.box.columns", "name": "columns", "type": "rich.columns.Columns"}}, "console": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.box.console", "name": "console", "type": "rich.console.Console"}}, "loop_last": {".class": "SymbolTableNode", "cross_ref": "rich._loop.loop_last", "kind": "Gdef"}, "table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.box.table", "name": "table", "type": "rich.table.Table"}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\box.py"}