{"data_mtime": 1757356840, "dep_lines": [1, 2, 3, 29, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["pydantic_settings.exceptions", "pydantic_settings.main", "pydantic_settings.sources", "pydantic_settings.version", "builtins", "pyexpat.errors", "pyexpat.model", "abc", "typing"], "hash": "21490ee53914bba798811261035a624f0e23a7af8206157b91a9b4ac4875165a", "id": "pydantic_settings", "ignore_all": true, "interface_hash": "a7a2b21fb17673ddf0008bd3f544b70559ac3d3ce2be2aae123635acec2e216d", "mtime": 1757091892, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_settings\\__init__.py", "plugin_data": null, "size": 1563, "suppressed": [], "version_id": "1.8.0"}