{"data_mtime": 1757363959, "dep_lines": [18, 19, 20, 2, 3, 4, 5, 6, 7, 227, 355, 366, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 5, 5, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.compat", "_pytest.fixtures", "_pytest.warning_types", "os", "re", "sys", "warnings", "contextlib", "typing", "inspect", "pkg_resources", "importlib", "builtins", "_collections_abc", "_pytest.config", "_typeshed", "abc", "enum", "types", "typing_extensions"], "hash": "bd3d3073be41816e04955b4385f94faaf6f211cde77716b3dbc11872efc72ccd", "id": "_pytest.monkeypatch", "ignore_all": true, "interface_hash": "07d231c34482e5436ba1fb95b7cf70feb6dc9296431cc74d6cf3bcd806337a5c", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\monkeypatch.py", "plugin_data": null, "size": 14857, "suppressed": [], "version_id": "1.8.0"}