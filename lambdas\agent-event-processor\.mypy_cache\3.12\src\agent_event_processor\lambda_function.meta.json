{"data_mtime": 1757364948, "dep_lines": [16, 17, 18, 19, 14, 12, 13, 7, 8, 9, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.agent_event_processor.config.settings", "src.agent_event_processor.models.events", "src.agent_event_processor.services.event_processor", "src.agent_event_processor.utils.xml_parser", "aws_lambda_typing.events.sqs", "aws_lambda_typing.context", "aws_lambda_typing.events", "json", "time", "typing", "aws_lambda_powertools", "aws_lambda_typing", "builtins", "_collections_abc", "_typeshed", "abc", "aws_lambda_powertools.logging", "aws_lambda_powertools.logging.logger", "aws_lambda_powertools.tracing", "aws_lambda_powertools.tracing.base", "aws_lambda_powertools.tracing.tracer", "aws_lambda_typing.context.context", "datetime", "enum", "functools", "json.encoder", "logging", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings", "pydantic_settings.main", "src.agent_event_processor.config", "src.agent_event_processor.models", "src.agent_event_processor.services", "src.agent_event_processor.utils", "types", "typing_extensions"], "hash": "2487efc6dc009e8c7534acacab9c7c57a111c6607de0e937cb5fd82bfafe7bef", "id": "src.agent_event_processor.lambda_function", "ignore_all": false, "interface_hash": "2dc4b9a15b4eefbe74b42c8edda92a55f7c7cc1f71914e1cea59cc73eb40b6ec", "mtime": 1757363646, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "src\\agent_event_processor\\lambda_function.py", "plugin_data": null, "size": 7744, "suppressed": [], "version_id": "1.8.0"}