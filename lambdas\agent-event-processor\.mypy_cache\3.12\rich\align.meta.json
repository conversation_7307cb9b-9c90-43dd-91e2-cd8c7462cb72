{"data_mtime": 1757356834, "dep_lines": [4, 5, 6, 7, 8, 11, 287, 288, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.constrain", "rich.jupyter", "rich.measure", "rich.segment", "rich.style", "rich.console", "rich.highlighter", "rich.panel", "itertools", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "_random", "abc", "datetime", "enum", "rich.box", "rich.text", "rich.theme"], "hash": "0036b9b72d4487f61febc21acb71602b28d45e08eb7384f2a810f0c3d15e000b", "id": "rich.align", "ignore_all": true, "interface_hash": "58d5ac5c0fa6d3ca6698782b12ad495f50ec576ca017b711b076dba2fb43fe25", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\align.py", "plugin_data": null, "size": 10288, "suppressed": [], "version_id": "1.8.0"}