{"data_mtime": 1757363959, "dep_lines": [20, 31, 32, 33, 34, 36, 41, 1, 2, 3, 4, 5, 39, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 10, 10, 5, 5, 5, 25, 5, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest._io", "_pytest.compat", "_pytest.config", "_pytest.nodes", "_pytest.outcomes", "_pytest.runner", "dataclasses", "os", "io", "pprint", "typing", "typing_extensions", "builtins", "_pytest._code", "_pytest._io.terminalwriter", "_typeshed", "abc"], "hash": "4d5b7933712d1ab65f44e245db753c817e7f6038ee992df8be5c375571cf6e17", "id": "_pytest.reports", "ignore_all": true, "interface_hash": "91b39ba4e5263360748d141b77f6805d81a667eef0f14151693db671f6e95403", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\reports.py", "plugin_data": null, "size": 20840, "suppressed": [], "version_id": "1.8.0"}