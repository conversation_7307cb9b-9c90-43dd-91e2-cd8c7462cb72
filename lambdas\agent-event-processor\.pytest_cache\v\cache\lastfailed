{"tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_login_event_processing": true, "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_busied_out_event_processing": true, "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_batch_event_processing": true, "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_invalid_xml_handling": true, "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_missing_required_fields": true, "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_lambda_response_format": true, "tests/unit/test_database_service.py::TestDatabaseService::test_init_missing_env_vars": true, "tests/unit/test_event_processor.py::TestEventProcessor::test_resolve_dimension_keys": true, "tests/unit/test_event_processor.py::TestEventProcessor::test_transform_to_fact_data": true, "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_lambda_timeout_handling": true, "test_all_event_types.py::test_event_type": true}