{".class": "MypyFile", "_fullname": "tenacity.stop", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "RetryCallState": {".class": "SymbolTableNode", "cross_ref": "tenacity.RetryCallState", "kind": "Gdef"}, "StopBaseT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "tenacity.stop.StopBaseT", "line": 41, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["tenacity.stop.stop_base", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tenacity.RetryCallState"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tenacity.stop.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tenacity.stop.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tenacity.stop.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tenacity.stop.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tenacity.stop.__package__", "name": "__package__", "type": "builtins.str"}}, "_stop_never": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tenacity.stop.stop_base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tenacity.stop._stop_never", "name": "_stop_never", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tenacity.stop._stop_never", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "tenacity.stop", "mro": ["tenacity.stop._stop_never", "tenacity.stop.stop_base", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop._stop_never.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "arg_types": ["tenacity.stop._stop_never", "tenacity.RetryCallState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _stop_never", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tenacity.stop._stop_never.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tenacity.stop._stop_never", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_utils": {".class": "SymbolTableNode", "cross_ref": "tenacity._utils", "kind": "Gdef"}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "stop_after_attempt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tenacity.stop.stop_base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tenacity.stop.stop_after_attempt", "name": "stop_after_attempt", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tenacity.stop.stop_after_attempt", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "tenacity.stop", "mro": ["tenacity.stop.stop_after_attempt", "tenacity.stop.stop_base", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_after_attempt.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "arg_types": ["tenacity.stop.stop_after_attempt", "tenacity.RetryCallState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of stop_after_attempt", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_attempt_number"], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_after_attempt.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_attempt_number"], "arg_types": ["tenacity.stop.stop_after_attempt", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of stop_after_attempt", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "max_attempt_number": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tenacity.stop.stop_after_attempt.max_attempt_number", "name": "max_attempt_number", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tenacity.stop.stop_after_attempt.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tenacity.stop.stop_after_attempt", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "stop_after_delay": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tenacity.stop.stop_base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tenacity.stop.stop_after_delay", "name": "stop_after_delay", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tenacity.stop.stop_after_delay", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "tenacity.stop", "mro": ["tenacity.stop.stop_after_delay", "tenacity.stop.stop_base", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_after_delay.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "arg_types": ["tenacity.stop.stop_after_delay", "tenacity.RetryCallState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of stop_after_delay", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_delay"], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_after_delay.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_delay"], "arg_types": ["tenacity.stop.stop_after_delay", {".class": "TypeAliasType", "args": [], "type_ref": "tenacity._utils.time_unit_type"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of stop_after_delay", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "max_delay": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tenacity.stop.stop_after_delay.max_delay", "name": "max_delay", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tenacity.stop.stop_after_delay.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tenacity.stop.stop_after_delay", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "stop_all": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tenacity.stop.stop_base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tenacity.stop.stop_all", "name": "stop_all", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tenacity.stop.stop_all", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "tenacity.stop", "mro": ["tenacity.stop.stop_all", "tenacity.stop.stop_base", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_all.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "arg_types": ["tenacity.stop.stop_all", "tenacity.RetryCallState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of stop_all", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "stops"], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_all.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "stops"], "arg_types": ["tenacity.stop.stop_all", "tenacity.stop.stop_base"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of stop_all", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "stops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tenacity.stop.stop_all.stops", "name": "stops", "type": {".class": "Instance", "args": ["tenacity.stop.stop_base"], "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tenacity.stop.stop_all.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tenacity.stop.stop_all", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "stop_any": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tenacity.stop.stop_base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tenacity.stop.stop_any", "name": "stop_any", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tenacity.stop.stop_any", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "tenacity.stop", "mro": ["tenacity.stop.stop_any", "tenacity.stop.stop_base", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_any.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "arg_types": ["tenacity.stop.stop_any", "tenacity.RetryCallState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of stop_any", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "stops"], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_any.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "stops"], "arg_types": ["tenacity.stop.stop_any", "tenacity.stop.stop_base"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of stop_any", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "stops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tenacity.stop.stop_any.stops", "name": "stops", "type": {".class": "Instance", "args": ["tenacity.stop.stop_base"], "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tenacity.stop.stop_any.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tenacity.stop.stop_any", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "stop_base": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tenacity.stop.stop_base", "name": "stop_base", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "tenacity.stop.stop_base", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "tenacity.stop", "mro": ["tenacity.stop.stop_base", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__and__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_base.__and__", "name": "__and__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["tenacity.stop.stop_base", "tenacity.stop.stop_base"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__and__ of stop_base", "ret_type": "tenacity.stop.stop_all", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "dataclass_transform_spec": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "tenacity.stop.stop_base.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "arg_types": ["tenacity.stop.stop_base", "tenacity.RetryCallState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of stop_base", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tenacity.stop.stop_base.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "arg_types": ["tenacity.stop.stop_base", "tenacity.RetryCallState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of stop_base", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "__or__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_base.__or__", "name": "__or__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["tenacity.stop.stop_base", "tenacity.stop.stop_base"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__or__ of stop_base", "ret_type": "tenacity.stop.stop_any", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tenacity.stop.stop_base.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tenacity.stop.stop_base", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "stop_before_delay": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tenacity.stop.stop_base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tenacity.stop.stop_before_delay", "name": "stop_before_delay", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tenacity.stop.stop_before_delay", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "tenacity.stop", "mro": ["tenacity.stop.stop_before_delay", "tenacity.stop.stop_base", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_before_delay.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "arg_types": ["tenacity.stop.stop_before_delay", "tenacity.RetryCallState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of stop_before_delay", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_delay"], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_before_delay.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_delay"], "arg_types": ["tenacity.stop.stop_before_delay", {".class": "TypeAliasType", "args": [], "type_ref": "tenacity._utils.time_unit_type"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of stop_before_delay", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "max_delay": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tenacity.stop.stop_before_delay.max_delay", "name": "max_delay", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tenacity.stop.stop_before_delay.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tenacity.stop.stop_before_delay", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "stop_never": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tenacity.stop.stop_never", "name": "stop_never", "type": "tenacity.stop._stop_never"}}, "stop_when_event_set": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tenacity.stop.stop_base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tenacity.stop.stop_when_event_set", "name": "stop_when_event_set", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tenacity.stop.stop_when_event_set", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "tenacity.stop", "mro": ["tenacity.stop.stop_when_event_set", "tenacity.stop.stop_base", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_when_event_set.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "retry_state"], "arg_types": ["tenacity.stop.stop_when_event_set", "tenacity.RetryCallState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of stop_when_event_set", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "flags": [], "fullname": "tenacity.stop.stop_when_event_set.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["tenacity.stop.stop_when_event_set", "threading.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of stop_when_event_set", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tenacity.stop.stop_when_event_set.event", "name": "event", "type": "threading.Event"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tenacity.stop.stop_when_event_set.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tenacity.stop.stop_when_event_set", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\tenacity\\stop.py"}