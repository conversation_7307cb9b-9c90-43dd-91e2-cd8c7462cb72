{"data_mtime": 1757363959, "dep_lines": [52, 5, 40, 41, 42, 43, 46, 53, 54, 56, 57, 58, 60, 63, 66, 68, 69, 450, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 35, 37, 40, 73, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 76], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["_pytest.config.argparsing", "collections.abc", "_pytest.timing", "_pytest._code", "_pytest.capture", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.main", "_pytest.monkeypatch", "_pytest.nodes", "_pytest.outcomes", "_pytest.pathlib", "_pytest.reports", "_pytest.tmpdir", "_pytest.warning_types", "_pytest.pytester_assertions", "collections", "contextlib", "gc", "importlib", "locale", "os", "platform", "re", "shutil", "subprocess", "sys", "traceback", "fnmatch", "io", "pathlib", "typing", "weakref", "iniconfig", "_pytest", "typing_extensions", "builtins", "_collections_abc", "_pytest._code.source", "_pytest.hookspec", "_pytest.mark", "_typeshed", "abc", "enum", "pluggy", "pluggy._hooks", "pluggy._manager", "pluggy._result", "types"], "hash": "3c086f0dd2383b280cadf1ce33ad092fb68db4a18be4428e49259424a10f84ce", "id": "_pytest.pytester", "ignore_all": true, "interface_hash": "b082df038f06554d8757d08a69b2f717f36684bf38f738a77dc0019cfdb61c0e", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\pytester.py", "plugin_data": null, "size": 62002, "suppressed": ["pexpect"], "version_id": "1.8.0"}