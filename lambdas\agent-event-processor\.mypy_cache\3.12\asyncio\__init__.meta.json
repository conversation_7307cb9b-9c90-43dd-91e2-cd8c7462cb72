{"data_mtime": 1757356836, "dep_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 24, 27, 28, 31, 1, 3, 4, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["collections.abc", "asyncio.base_events", "asyncio.coroutines", "asyncio.events", "asyncio.futures", "asyncio.locks", "asyncio.protocols", "asyncio.queues", "asyncio.runners", "asyncio.streams", "asyncio.subprocess", "asyncio.tasks", "asyncio.transports", "asyncio.exceptions", "asyncio.threads", "asyncio.taskgroups", "asyncio.timeouts", "asyncio.windows_events", "sys", "typing", "typing_extensions", "builtins", "pyexpat.errors", "pyexpat.model", "_typeshed", "abc"], "hash": "641e83229d0ada08bb4cfa3063e0ba16de607d840f25a995752294d00077aae3", "id": "asyncio", "ignore_all": true, "interface_hash": "de9c44d3ca4f2a5ae1f7ad3459bf18459321ef32a1130a85e7437ab525d7300d", "mtime": 1757092230, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\asyncio\\__init__.pyi", "plugin_data": null, "size": 1217, "suppressed": [], "version_id": "1.8.0"}