{".class": "MypyFile", "_fullname": "botocore.retries.base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BaseRetryBackoff": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.base.BaseRetryBackoff", "name": "BaseRetryBackoff", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.retries.base.BaseRetryBackoff", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.base", "mro": ["botocore.retries.base.BaseRetryBackoff", "builtins.object"], "names": {".class": "SymbolTable", "delay_amount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.retries.base.BaseRetryBackoff.delay_amount", "name": "delay_amount", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.base.BaseRetryBackoff", "botocore.retries.standard.RetryContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delay_amount of BaseRetryBackoff", "ret_type": "builtins.float", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.base.BaseRetryBackoff.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.base.BaseRetryBackoff", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseRetryableChecker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.base.BaseRetryableChecker", "name": "BaseRetryableChecker", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.retries.base.BaseRetryableChecker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.base", "mro": ["botocore.retries.base.BaseRetryableChecker", "builtins.object"], "names": {".class": "SymbolTable", "is_retryable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.retries.base.BaseRetryableChecker.is_retryable", "name": "is_retryable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.base.BaseRetryableChecker", "botocore.retries.standard.RetryContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_retryable of BaseRetryableChecker", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.base.BaseRetryableChecker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.base.BaseRetryableChecker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RetryContext": {".class": "SymbolTableNode", "cross_ref": "botocore.retries.standard.RetryContext", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.retries.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.retries.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.retries.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.retries.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.retries.base.__package__", "name": "__package__", "type": "builtins.str"}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\retries\\base.pyi"}