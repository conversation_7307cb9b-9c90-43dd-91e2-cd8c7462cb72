{"data_mtime": 1757356838, "dep_lines": [13, 14, 4, 11, 1, 3, 5, 6, 7, 8, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["pydantic._internal._fields", "pydantic._internal._import_utils", "collections.abc", "pydantic_core.core_schema", "__future__", "collections", "copy", "decimal", "functools", "typing", "pydantic_core", "builtins", "pyexpat.errors", "pyexpat.model", "_typeshed", "abc"], "hash": "7e74f311b6c8e8850d7d64cac19bb5a0acbc030e13b641d2c9cb10c8316144bb", "id": "pydantic._internal._known_annotated_metadata", "ignore_all": true, "interface_hash": "b3dd3b419f681cf0dc7c6d9bc6ef3c21f548f2f1af9c10ca13dceafe11b19ebf", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py", "plugin_data": null, "size": 16426, "suppressed": [], "version_id": "1.8.0"}