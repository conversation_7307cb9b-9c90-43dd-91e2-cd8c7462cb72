{"data_mtime": 1757356839, "dep_lines": [7, 11, 12, 14, 8, 9, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "botocore.compat", "botocore.exceptions", "botocore.utils", "logging", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "abc"], "hash": "8cf66514eff9a8e52cf034b74447549584824359f172debe558f28b37d9e159e", "id": "botocore.loaders", "ignore_all": true, "interface_hash": "2667f49c3276cdba66bbcbfb7352fe35faf46a63554574bfbad213ec0e025858", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\loaders.pyi", "plugin_data": null, "size": 2068, "suppressed": [], "version_id": "1.8.0"}