{".class": "MypyFile", "_fullname": "botocore.config", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.config.Config", "name": "Config", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.config.Config", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.config", "mro": ["botocore.config.Config", "builtins.object"], "names": {".class": "SymbolTable", "NON_LEGACY_OPTION_DEFAULTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.config.Config.NON_LEGACY_OPTION_DEFAULTS", "name": "NON_LEGACY_OPTION_DEFAULTS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}], "type_ref": "builtins.dict"}}}, "OPTION_DEFAULTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.config.Config.OPTION_DEFAULTS", "name": "OPTION_DEFAULTS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}], "type_ref": "collections.OrderedDict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "region_name", "signature_version", "user_agent", "user_agent_extra", "connect_timeout", "read_timeout", "parameter_validation", "max_pool_connections", "proxies", "proxies_config", "s3", "retries", "client_cert", "inject_host_prefix", "endpoint_discovery_enabled", "use_dualstack_endpoint", "use_fips_endpoint", "defaults_mode", "tcp_keepalive", "request_min_compression_size_bytes", "disable_request_compression", "sigv4a_signing_region_set", "client_context_params", "request_checksum_calculation", "response_checksum_validation", "account_id_endpoint_mode"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.config.Config.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "region_name", "signature_version", "user_agent", "user_agent_extra", "connect_timeout", "read_timeout", "parameter_validation", "max_pool_connections", "proxies", "proxies_config", "s3", "retries", "client_cert", "inject_host_prefix", "endpoint_discovery_enabled", "use_dualstack_endpoint", "use_fips_endpoint", "defaults_mode", "tcp_keepalive", "request_min_compression_size_bytes", "disable_request_compression", "sigv4a_signing_region_set", "client_context_params", "request_checksum_calculation", "response_checksum_validation", "account_id_endpoint_mode"], "arg_types": ["botocore.config.Config", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "botocore.config._ProxiesConfigDict"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "botocore.config._S3Dict"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "botocore.config._RetryDict"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "when_supported"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "when_required"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "when_supported"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "when_required"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "preferred"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disabled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "required"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Config", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "inject_host_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_property"], "fullname": "botocore.config.Config.inject_host_prefix", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "botocore.config.Config.inject_host_prefix", "name": "inject_host_prefix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.config.Config"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject_host_prefix of Config", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "botocore.config.Config.inject_host_prefix", "name": "inject_host_prefix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.config.Config"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject_host_prefix of Config", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "botocore.config.Config.inject_host_prefix", "name": "inject_host_prefix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["botocore.config.Config", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject_host_prefix of Config", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "inject_host_prefix", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.config.Config"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject_host_prefix of Config", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "merge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other_config"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.config.Config.merge", "name": "merge", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other_config"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.config._Config", "id": -1, "name": "_Config", "namespace": "", "upper_bound": "botocore.config.Config", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.config._Config", "id": -1, "name": "_Config", "namespace": "", "upper_bound": "botocore.config.Config", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge of Config", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.config._Config", "id": -1, "name": "_Config", "namespace": "", "upper_bound": "botocore.config.Config", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.config._Config", "id": -1, "name": "_Config", "namespace": "", "upper_bound": "botocore.config.Config", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.config.Config.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.config.Config", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DEFAULT_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "botocore.endpoint.DEFAULT_TIMEOUT", "kind": "Gdef"}, "InvalidMaxRetryAttemptsError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.InvalidMaxRetryAttemptsError", "kind": "Gdef"}, "InvalidRetryConfigurationError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.InvalidRetryConfigurationError", "kind": "Gdef"}, "InvalidRetryModeError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.InvalidRetryModeError", "kind": "Gdef"}, "InvalidS3AddressingStyleError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.InvalidS3AddressingStyleError", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MAX_POOL_CONNECTIONS": {".class": "SymbolTableNode", "cross_ref": "botocore.endpoint.MAX_POOL_CONNECTIONS", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.config._Config", "name": "_Config", "upper_bound": "botocore.config.Config", "values": [], "variance": 0}}, "_ProxiesConfigDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.config._ProxiesConfigDict", "name": "_ProxiesConfigDict", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.config._ProxiesConfigDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.config", "mro": ["botocore.config._ProxiesConfigDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["proxy_ca_bundle", "builtins.str"], ["proxy_client_cert", {".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}]}], ["proxy_use_forwarding_for_https", "builtins.bool"]], "required_keys": []}}}, "_RetryDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.config._RetryDict", "name": "_RetryDict", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.config._RetryDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.config", "mro": ["botocore.config._RetryDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["total_max_attempts", "builtins.int"], ["max_attempts", "builtins.int"], ["mode", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "legacy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "adaptive"}]}]], "required_keys": []}}}, "_S3Dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.config._S3Dict", "name": "_S3Dict", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.config._S3Dict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.config", "mro": ["botocore.config._S3Dict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["use_accelerate_endpoint", "builtins.bool"], ["payload_signing_enabled", "builtins.bool"], ["addressing_style", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "virtual"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "path"}]}], ["us_east_1_regional_endpoint", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "regional"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "legacy"}]}]], "required_keys": []}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.config.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.config.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.config.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.config.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.config.__package__", "name": "__package__", "type": "builtins.str"}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\config.pyi"}