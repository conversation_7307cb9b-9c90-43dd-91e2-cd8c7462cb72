{"data_mtime": 1757363959, "dep_lines": [23, 29, 16, 25, 30, 32, 33, 35, 36, 40, 42, 43, 45, 3, 4, 14, 19, 20, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 25, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.deprecated", "_pytest.config", "_pytest.fixtures", "_pytest.main", "_pytest.nodes", "_pytest.outcomes", "_pytest.python", "_pytest.reports", "_pytest.runner", "_pytest.terminal", "_pytest.compat", "pathlib", "typing", "pluggy", "pdb", "warnings", "typing_extensions", "builtins", "_pytest._code", "_pytest.warning_types", "abc", "bdb", "cmd", "enum", "os", "pluggy._hooks", "pluggy._manager"], "hash": "a52199e6141e23bc886cb59d9bfb974470e43062c7e38c2b30e5a351a9a6c68b", "id": "_pytest.hookspec", "ignore_all": true, "interface_hash": "67c997e138286a36d4ab93905b5cf19d8e53cd810412cf6b7e60a727ca7fe1d0", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\hookspec.py", "plugin_data": null, "size": 32558, "suppressed": [], "version_id": "1.8.0"}