{"data_mtime": 1757364036, "dep_lines": [1, 2, 1, 1], "dep_prios": [5, 5, 5, 30], "dependencies": ["collections.abc", "typing", "builtins", "abc"], "hash": "1721ceae53eb6976ad2a0471feedc8c01152d2b48662caddc0df0d8f9e9e084b", "id": "requests.structures", "ignore_all": true, "interface_hash": "38aa33a45be28ec1d218dc97205b2600256436bda2eb15b5c2eb2cc1f1ec16ab", "mtime": 1757363991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\requests-stubs\\structures.pyi", "plugin_data": null, "size": 1109, "suppressed": [], "version_id": "1.8.0"}