{".class": "MypyFile", "_fullname": "urllib3.util.connection", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseHTTPConnection": {".class": "SymbolTableNode", "cross_ref": "urllib3._base_connection.BaseHTTPConnection", "kind": "Gdef"}, "HAS_IPV6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.util.connection.HAS_IPV6", "name": "HAS_IPV6", "type": "builtins.bool"}}, "LocationParseError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.LocationParseError", "kind": "Gdef"}, "_DEFAULT_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.timeout._DEFAULT_TIMEOUT", "kind": "Gdef"}, "_TYPE_SOCKET_OPTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "urllib3.util.connection._TYPE_SOCKET_OPTIONS", "line": 9, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.bytes"]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "_TYPE_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.timeout._TYPE_TIMEOUT", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.connection.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.connection.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.connection.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.connection.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.connection.__package__", "name": "__package__", "type": "builtins.str"}}, "_has_ipv6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["host"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.util.connection._has_ipv6", "name": "_has_ipv6", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["host"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_ipv6", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_set_socket_options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["sock", "options"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.util.connection._set_socket_options", "name": "_set_socket_options", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["sock", "options"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_socket_options", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "allowed_gai_family": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.util.connection.allowed_gai_family", "name": "allowed_gai_family", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "allowed_gai_family", "ret_type": "socket.AddressFamily", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "create_connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["address", "timeout", "source_address", "socket_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.util.connection.create_connection", "name": "create_connection", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["address", "timeout", "source_address", "socket_options"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_connection", "ret_type": "socket.socket", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_connection_dropped": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.util.connection.is_connection_dropped", "name": "is_connection_dropped", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["conn"], "arg_types": ["urllib3._base_connection.BaseHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_connection_dropped", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\util\\connection.py"}