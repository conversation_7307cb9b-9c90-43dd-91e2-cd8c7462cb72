"""
Redshift Database Service for Agent Event Processor.

This module provides a Redshift database service using:
- Redshift Data API with proper error handling
- SQL parameterization to prevent injection
- Non-blocking result fetching with async patterns
- Comprehensive deduplication logic
- SCD Type 2 dimension management
- Robust retry logic and timeouts
"""

import os
import time
import json
from contextlib import contextmanager
from typing import Any, Dict, Generator, Optional, List, Tuple
from datetime import datetime
from decimal import Decimal

import boto3
from botocore.exceptions import ClientError, BotoCoreError
from aws_lambda_powertools import Logger
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)

from src.agent_event_processor.config.settings import DatabaseSettings


# Database error constants for better error handling
class DatabaseErrorCodes:
    """Redshift error codes for better error handling."""
    DUPLICATE_KEY = "23505"
    UNIQUE_VIOLATION = "23505"
    FOREIGN_KEY_VIOLATION = "23503"
    CONNECTION_ERROR = "08000"
    TIMEOUT_ERROR = "57014"


logger = Logger()


class RedshiftDataAPIConnection:
    """Redshift Data API connection."""

    def __init__(
        self,
        cluster_identifier: str,
        database: str,
        user: str,
        region: str,
        query_group: str = "agent-event-processor",
        timeout: int = 300,
    ):
        """Initialize Redshift Data API connection."""
        self.cluster_identifier = cluster_identifier
        self.database = database
        self.user = user
        self.region = region
        self.query_group = query_group
        self.timeout = timeout

        # Initialize boto3 client with retry configuration
        self.client = boto3.client(
            "redshift-data",
            region_name=region,
            config=boto3.session.Config(
                retries={"max_attempts": 3, "mode": "adaptive"},
                read_timeout=60,
                connect_timeout=10,
            ),
        )

        logger.debug(
            "Redshift Data API connection initialized",
            cluster_identifier=cluster_identifier,
            database=database,
            user=user,
            query_group=query_group,
            region=region,
        )

    def cursor(self):
        """Create a cursor for executing queries."""
        return RedshiftDataAPICursor(self)

    def close(self):
        """Close connection (no-op for Data API)."""
        pass

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()


class RedshiftDataAPICursor:
    """Redshift Data API cursor with proper SQL parameterization."""

    def __init__(self, connection: RedshiftDataAPIConnection):
        """Initialize cursor."""
        self.connection = connection
        self._results = []
        self._current_statement_id = None

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=8),
        retry=retry_if_exception_type((ClientError, BotoCoreError)),
    )
    def execute(self, sql: str, parameters: List[Any] = None):
        """Execute SQL statement with proper parameterization."""
        try:
            # Prepare the SQL with parameters
            prepared_sql, redshift_params = self._prepare_sql_with_params(
                sql, parameters
            )

            # Build request parameters
            request_params = self._build_request_params(prepared_sql, redshift_params)

            # Execute the statement
            statement_id = self._execute_statement(request_params)

            # Wait for completion
            self._wait_for_completion(statement_id)

            # Fetch results if this is a SELECT query
            self._fetch_results_if_select(prepared_sql, statement_id)

            return statement_id

        except Exception as e:
            logger.error(
                "Failed to execute SQL statement",
                error=str(e),
                sql_preview=sql[:100],
                parameter_count=len(parameters) if parameters else 0,
                exc_info=True,
            )
            raise

    def _prepare_sql_with_params(
        self, sql: str, parameters: List[Any] = None
    ) -> Tuple[str, List[Dict[str, str]]]:
        """Prepare SQL with proper parameterization to prevent SQL injection."""
        if not parameters:
            return sql, []

        redshift_params = []
        prepared_sql = sql

        for i, param in enumerate(parameters):
            param_name = f"param{i+1}"
            param_value = self._convert_parameter_value(param)

            if param_value is not None:
                redshift_params.append({"name": param_name, "value": param_value})
                # Replace %s with :paramN
                prepared_sql = prepared_sql.replace("%s", f":{param_name}", 1)
            else:
                # Replace %s with NULL for None values
                prepared_sql = prepared_sql.replace("%s", "NULL", 1)

        return prepared_sql, redshift_params

    def _convert_parameter_value(self, param: Any) -> Optional[str]:
        """Convert parameter to string value for Redshift Data API."""
        if param is None:
            return None
        elif isinstance(param, bool):
            return "true" if param else "false"
        elif isinstance(param, (int, float, Decimal)):
            return str(param)
        elif isinstance(param, datetime):
            return param.isoformat()
        elif isinstance(param, (dict, list)):
            return json.dumps(param)
        elif isinstance(param, str) and param.startswith("{") and param.endswith("}"):
            # This looks like a JSON string - pass through for SUPER columns
            return param
        else:
            # Convert to string and handle empty strings
            str_value = str(param)
            return str_value if str_value else None

    def _build_request_params(
        self, sql: str, redshift_params: List[Dict[str, str]]
    ) -> Dict[str, Any]:
        """Build request parameters for execute_statement."""
        request_params = {
            "ClusterIdentifier": self.connection.cluster_identifier,
            "Database": self.connection.database,
            "DbUser": self.connection.user,
            "Sql": sql,
            "StatementName": f"agent-event-{int(time.time())}-{os.getpid()}",
        }

        if redshift_params:
            request_params["Parameters"] = redshift_params

        return request_params

    def _execute_statement(self, request_params: Dict[str, Any]) -> str:
        """Execute the SQL statement and return statement ID."""
        response = self.connection.client.execute_statement(**request_params)
        statement_id = response["Id"]
        self._current_statement_id = statement_id

        logger.debug(
            "SQL statement submitted",
            statement_id=statement_id,
            sql_preview=request_params["Sql"][:100],
        )

        return statement_id

    def _fetch_results_if_select(self, sql: str, statement_id: str):
        """Fetch results if this is a SELECT query."""
        if sql.strip().upper().startswith("SELECT"):
            try:
                self._fetch_results(statement_id)
            except ClientError as e:
                if "ResourceNotFoundException" in str(e):
                    logger.warning(
                        "Query completed but no results available",
                        statement_id=statement_id,
                    )
                    self._results = []
                else:
                    raise
        else:
            self._results = []

    def _wait_for_completion(self, statement_id: str):
        """Wait for statement to complete with exponential backoff."""
        timeout = self.connection.timeout
        start_time = time.time()
        check_interval = 0.5  # Start with 500ms
        max_interval = 5.0  # Max 5 seconds between checks

        while time.time() - start_time < timeout:
            try:
                response = self.connection.client.describe_statement(Id=statement_id)
                status = response["Status"]

                if status == "FINISHED":
                    logger.debug(
                        "Statement completed successfully", statement_id=statement_id
                    )
                    return
                elif status in ["FAILED", "ABORTED"]:
                    error_msg = response.get("Error", "Unknown error")
                    logger.error(
                        "Statement failed",
                        statement_id=statement_id,
                        status=status,
                        error=error_msg,
                    )
                    raise Exception(f"Query {status.lower()}: {error_msg}")
                elif status in ["SUBMITTED", "PICKED", "STARTED"]:
                    # Query is still running, wait before checking again
                    time.sleep(min(check_interval, max_interval))
                    check_interval = min(
                        check_interval * 1.5, max_interval
                    )  # Exponential backoff
                else:
                    logger.warning(
                        "Unknown statement status",
                        status=status,
                        statement_id=statement_id,
                    )
                    time.sleep(check_interval)

            except ClientError as e:
                logger.error(
                    "Error checking statement status",
                    error=str(e),
                    statement_id=statement_id,
                )
                raise

        # Timeout reached
        elapsed = time.time() - start_time
        logger.error(
            "Statement timeout",
            statement_id=statement_id,
            timeout=timeout,
            elapsed=elapsed,
        )
        raise TimeoutError(
            f"Query timeout after {elapsed:.1f} seconds (limit: {timeout}s)"
        )

    def _fetch_results(self, statement_id: str):
        """Fetch query results and convert to list of dictionaries."""
        try:
            response = self.connection.client.get_statement_result(Id=statement_id)
            records = response.get("Records", [])
            column_metadata = response.get("ColumnMetadata", [])

            if not records:
                self._results = []
                return

            # Convert records to list of dictionaries
            results = []
            for record in records:
                row = self._convert_record_to_dict(record, column_metadata)
                results.append(row)

            self._results = results
            logger.debug(
                "Results fetched successfully",
                statement_id=statement_id,
                row_count=len(results),
                column_count=len(column_metadata),
            )

        except Exception as e:
            logger.error(
                "Failed to fetch results", error=str(e), statement_id=statement_id
            )
            raise

    def _convert_record_to_dict(
        self, record: List[Dict], column_metadata: List[Dict]
    ) -> Dict[str, Any]:
        """Convert a single record to a dictionary with proper type conversion."""
        row = {}
        for i, field in enumerate(record):
            # Get column name
            column_name = (
                column_metadata[i]["name"]
                if i < len(column_metadata)
                else f"column_{i}"
            )

            # Extract and convert value based on type
            value = self._extract_field_value(field)
            row[column_name] = value

        return row

    def _extract_field_value(self, field: Dict[str, Any]) -> Any:
        """Extract value from Redshift Data API field with proper type conversion."""
        if "isNull" in field and field["isNull"]:
            return None
        elif "stringValue" in field:
            return field["stringValue"]
        elif "longValue" in field:
            return field["longValue"]
        elif "doubleValue" in field:
            return field["doubleValue"]
        elif "booleanValue" in field:
            return field["booleanValue"]
        else:
            # Fallback for unknown field types
            logger.warning("Unknown field type", field=field)
            return str(field)

    def fetchone(self) -> Optional[Dict[str, Any]]:
        """Fetch one result row."""
        return self._results[0] if self._results else None

    def fetchall(self) -> List[Dict[str, Any]]:
        """Fetch all result rows."""
        return self._results

    def rowcount(self) -> int:
        """Get number of rows returned."""
        return len(self._results)

    def close(self):
        """Close cursor (no-op for Data API)."""
        pass

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()


class DatabaseService:
    """Redshift database service with proper configuration and connection reuse."""

    def __init__(self, settings: DatabaseSettings, aws_region: str = "us-east-1"):
        """Initialize database service with settings configuration."""
        self.settings = settings

        # Get configuration from settings
        self.cluster_identifier = settings.cluster_identifier
        self.database = settings.redshift_database
        self.user = settings.redshift_user
        self.region = aws_region
        self.query_group = settings.query_group
        self.timeout = settings.query_timeout

        # Connection reuse for Lambda container optimization
        self._connection: Optional[RedshiftDataAPIConnection] = None

        logger.info(
            "Database service initialized",
            cluster_identifier=self.cluster_identifier,
            database=self.database,
            user=self.user,
            region=self.region,
            query_group=self.query_group,
            timeout=self.timeout,
        )

    @contextmanager
    def get_connection(self) -> Generator[RedshiftDataAPIConnection, None, None]:
        """Get database connection with proper error handling and reuse optimization."""
        # Reuse existing connection if available (Lambda container optimization)
        if self._connection is None:
            try:
                self._connection = RedshiftDataAPIConnection(
                    cluster_identifier=self.cluster_identifier,
                    database=self.database,
                    user=self.user,
                    region=self.region,
                    query_group=self.query_group,
                    timeout=self.timeout,
                )
                logger.debug("Created new database connection")
            except Exception as e:
                logger.error("Database connection creation failed", error=str(e), exc_info=True)
                raise

        try:
            yield self._connection
        except Exception as e:
            logger.error("Database operation error", error=str(e), exc_info=True)
            # Reset connection on error to force recreation next time
            self._connection = None
            raise

    def execute_query(
        self, sql: str, parameters: Optional[List[Any]] = None
    ) -> List[Dict[str, Any]]:
        """Execute a SELECT query and return results."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            # Execute the query and wait for completion
            cursor.execute(sql, parameters or [])
            # Results are automatically fetched by the cursor for SELECT queries
            return cursor.fetchall()

    def execute_statement(
        self, sql: str, parameters: Optional[List[Any]] = None
    ) -> None:
        """Execute a non-SELECT statement (INSERT, UPDATE, DELETE)."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(sql, parameters or [])


class DatabaseError(Exception):
    """Custom database error for better error handling."""
    pass


class DimensionManager:
    """Dimension table manager with SCD2 and deduplication."""

    def __init__(self, db_service: DatabaseService):
        """Initialize dimension manager."""
        self.db_service = db_service
        logger.info("Dimension manager initialized")

    def _is_duplicate_key_error(self, error: Exception) -> bool:
        """Check if error is a duplicate key constraint violation."""
        error_str = str(error).lower()
        return any(
            keyword in error_str
            for keyword in ["duplicate", "unique", "constraint", "23505"]
        )

    def _find_existing_tenant(self, cursor, tenant_name: str) -> Dict[str, Any]:
        """Find existing tenant record."""
        cursor.execute(
            "SELECT tenant_key FROM dim_tenant WHERE LOWER(tenant_name) = LOWER(%s)",
            [tenant_name],
        )
        return cursor.fetchone()

    def _create_tenant_record(
        self, cursor, tenant_name: str, timezone_name: str
    ) -> int:
        """Create new tenant record and return tenant_key."""
        cursor.execute(
            """
            INSERT INTO dim_tenant (tenant_name, timezone_name, created_at, updated_at)
            VALUES (%s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """,
            [tenant_name, timezone_name],
        )

        cursor.execute(
            "SELECT tenant_key FROM dim_tenant WHERE LOWER(tenant_name) = LOWER(%s)",
            [tenant_name],
        )
        result = cursor.fetchone()

        if result and result.get("tenant_key"):
            tenant_key = result["tenant_key"]
            logger.info(
                "Created new tenant", tenant_name=tenant_name, tenant_key=tenant_key
            )
            return tenant_key

        raise DatabaseError(f"Could not get tenant key after creation for '{tenant_name}'")

    def _handle_tenant_race_condition(self, cursor, tenant_name: str) -> int:
        """Handle race condition when creating tenant."""
        logger.info(
            "Tenant already exists (race condition), fetching existing key",
            tenant_name=tenant_name,
        )
        cursor.execute(
            "SELECT tenant_key FROM dim_tenant WHERE LOWER(tenant_name) = LOWER(%s)",
            [tenant_name],
        )
        result = cursor.fetchone()
        if result and result.get("tenant_key"):
            return result["tenant_key"]
        raise DatabaseError(
            f"Could not find tenant key after race condition for '{tenant_name}'"
        )

    def get_or_create_tenant_key(
        self, tenant_name: str, timezone_name: str = "UTC"
    ) -> int:
        """Get or create tenant dimension key with deduplication."""
        try:
            with self.db_service.get_connection() as conn:
                with conn.cursor() as cursor:
                    existing_record = self._find_existing_tenant(cursor, tenant_name)

                    if existing_record and existing_record.get("tenant_key"):
                        tenant_key = existing_record["tenant_key"]
                        logger.debug(
                            "Found existing tenant",
                            tenant_name=tenant_name,
                            tenant_key=tenant_key,
                        )
                        return tenant_key

                    try:
                        return self._create_tenant_record(
                            cursor, tenant_name, timezone_name
                        )
                    except Exception as insert_error:
                        if self._is_duplicate_key_error(insert_error):
                            return self._handle_tenant_race_condition(
                                cursor, tenant_name
                            )
                        else:
                            raise insert_error

        except Exception as e:
            logger.error(
                "Failed to get or create tenant",
                tenant_name=tenant_name,
                error=str(e),
                exc_info=True,
            )
            raise DatabaseError(
                f"Database operation failed for tenant '{tenant_name}': {str(e)}"
            ) from e

    def _find_current_agent(
        self, cursor, tenant_key: int, agent_name: str
    ) -> Dict[str, Any]:
        """Find current agent record."""
        cursor.execute(
            """
            SELECT agent_key, operator_id, agent_role, agent_uri, workstation
            FROM dim_agent
            WHERE tenant_key = %s AND LOWER(agent_name) = LOWER(%s) AND is_current = TRUE
            """,
            [tenant_key, agent_name],
        )
        return cursor.fetchone()

    def _extract_agent_attributes(self, agent_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract agent attributes for comparison."""
        return {
            "operator_id": agent_data.get("operator_id"),
            "agent_role": agent_data.get("agent_role"),
            "agent_uri": agent_data.get("agent_uri"),
            "workstation": agent_data.get("workstation"),
        }

    def _close_current_agent_record(
        self, cursor, tenant_key: int, agent_name: str
    ) -> None:
        """Close current agent record for SCD Type 2."""
        cursor.execute(
            """
            UPDATE dim_agent
            SET is_current = FALSE, valid_to = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
            WHERE tenant_key = %s AND LOWER(agent_name) = LOWER(%s) AND is_current = TRUE
            """,
            [tenant_key, agent_name],
        )

    def _create_agent_record(
        self, cursor, agent_data: Dict[str, Any], tenant_key: int
    ) -> int:
        """Create new agent record and return agent_key."""
        agent_name = agent_data.get("agent_name")

        cursor.execute(
            """
            INSERT INTO dim_agent (
                agent_name, operator_id, agent_role, agent_uri, workstation,
                tenant_key, valid_from, is_current, created_at, updated_at
            ) VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """,
            [
                agent_name,
                agent_data.get("operator_id"),
                agent_data.get("agent_role"),
                agent_data.get("agent_uri"),
                agent_data.get("workstation"),
                tenant_key,
            ],
        )

        cursor.execute(
            """
            SELECT agent_key FROM dim_agent
            WHERE tenant_key = %s AND LOWER(agent_name) = LOWER(%s) AND is_current = TRUE
            ORDER BY agent_key DESC LIMIT 1
            """,
            [tenant_key, agent_name],
        )
        result = cursor.fetchone()

        if result and result.get("agent_key"):
            agent_key = result["agent_key"]
            logger.info(
                "Created new agent record", agent_name=agent_name, agent_key=agent_key
            )
            return agent_key

        raise DatabaseError(f"Could not get agent key after creation for '{agent_name}'")

    def _handle_agent_race_condition(
        self, cursor, tenant_key: int, agent_name: str
    ) -> int:
        """Handle race condition when creating agent."""
        logger.info(
            "Agent already exists (race condition), fetching existing key",
            agent_name=agent_name,
        )
        cursor.execute(
            """
            SELECT agent_key FROM dim_agent
            WHERE tenant_key = %s AND LOWER(agent_name) = LOWER(%s) AND is_current = TRUE
            ORDER BY agent_key DESC LIMIT 1
            """,
            [tenant_key, agent_name],
        )
        result = cursor.fetchone()
        if result and result.get("agent_key"):
            return result["agent_key"]
        raise DatabaseError(
            f"Could not find agent key after race condition for '{agent_name}'"
        )

    def get_or_create_agent_key(
        self, agent_data: Dict[str, Any], tenant_key: int
    ) -> int:
        """Get or create agent dimension key with SCD Type 2 support."""
        try:
            with self.db_service.get_connection() as conn:
                with conn.cursor() as cursor:
                    agent_name = agent_data.get("agent_name")

                    existing_record = self._find_current_agent(
                        cursor, tenant_key, agent_name
                    )

                    if existing_record and existing_record.get("agent_key"):
                        existing_attrs = {
                            "operator_id": existing_record.get("operator_id"),
                            "agent_role": existing_record.get("agent_role"),
                            "agent_uri": existing_record.get("agent_uri"),
                            "workstation": existing_record.get("workstation"),
                        }
                        current_attrs = self._extract_agent_attributes(agent_data)

                        if existing_attrs == current_attrs:
                            agent_key = existing_record["agent_key"]
                            logger.debug(
                                "Found existing agent with same attributes",
                                agent_name=agent_name,
                                agent_key=agent_key,
                            )
                            return agent_key

                        logger.info(
                            "Agent attributes changed, creating new SCD2 record",
                            agent_name=agent_name,
                            old_attributes=existing_attrs,
                            new_attributes=current_attrs,
                        )
                        self._close_current_agent_record(cursor, tenant_key, agent_name)

                    try:
                        return self._create_agent_record(cursor, agent_data, tenant_key)
                    except Exception as insert_error:
                        if self._is_duplicate_key_error(insert_error):
                            return self._handle_agent_race_condition(
                                cursor, tenant_key, agent_name
                            )
                        else:
                            raise insert_error

        except Exception as e:
            logger.error(
                "Failed to get or create agent",
                agent_data=agent_data,
                error=str(e),
                exc_info=True,
            )
            agent_name = agent_data.get("agent_name", "unknown")
            raise DatabaseError(
                f"Database operation failed for agent '{agent_name}': {str(e)}"
            ) from e

    def get_date_key(self, date_value: datetime) -> int:
        """Get date key in YYYYMMDD format."""
        return int(date_value.strftime("%Y%m%d"))

    def get_time_key(self, time_value: datetime) -> int:
        """Get time key in HHMMSS format."""
        return int(time_value.strftime("%H%M%S"))


class FactManager:
    """Fact table manager with deduplication."""

    def __init__(self, db_service: DatabaseService):
        """Initialize fact manager."""
        self.db_service = db_service
        logger.info("Fact manager initialized")

    def _is_duplicate_key_error(self, error: Exception) -> bool:
        """Check if error is a duplicate key constraint violation."""
        error_str = str(error).lower()
        return any(
            keyword in error_str
            for keyword in ["duplicate", "unique", "constraint", "23505"]
        )

    def insert_agent_event(self, fact_data: Dict[str, Any]) -> bool:
        """Insert agent event into fact table with deduplication."""
        try:
            with self.db_service.get_connection() as conn:
                with conn.cursor() as cursor:

                    # Insert new event
                    try:
                        cursor.execute(
                            """
                            INSERT INTO fact_agent_event (
                                agent_key, tenant_key, date_key, time_key,
                                event_timestamp_utc, event_timestamp_local, event_type,
                                reason_code, workstation, media_label,
                                busied_out_action, busied_out_duration, device_name,
                                event_data_json, processed_at_utc
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP
                            )
                            """,
                            [
                                fact_data.get("agent_key"),
                                fact_data.get("tenant_key"),
                                fact_data.get("date_key"),
                                fact_data.get("time_key"),
                                fact_data.get("event_timestamp_utc"),
                                fact_data.get("event_timestamp_local"),
                                fact_data.get("event_type"),
                                fact_data.get("reason_code"),
                                fact_data.get("workstation"),
                                fact_data.get("media_label"),
                                fact_data.get("busied_out_action"),
                                fact_data.get("busied_out_duration"),
                                fact_data.get("device_name"),
                                json.dumps(fact_data.get("event_data_json", {})),
                            ],
                        )

                        logger.info(
                            "Agent event inserted successfully",
                            event_type=fact_data.get("event_type"),
                            agent_key=fact_data.get("agent_key"),
                            tenant_key=fact_data.get("tenant_key"),
                        )
                        return True

                    except Exception as insert_error:
                        # Handle duplicate key constraint gracefully
                        if self._is_duplicate_key_error(insert_error):
                            logger.warning(
                                "Duplicate event constraint violation, continuing",
                                error=str(insert_error),
                                event_type=fact_data.get("event_type"),
                                agent_key=fact_data.get("agent_key"),
                                tenant_key=fact_data.get("tenant_key"),
                            )
                            return (
                                True  # Continue processing, this is not a fatal error
                            )
                        else:
                            raise insert_error

        except Exception as e:
            logger.error(
                "Failed to insert agent event",
                fact_data=fact_data,
                error=str(e),
                exc_info=True,
            )
            return False
