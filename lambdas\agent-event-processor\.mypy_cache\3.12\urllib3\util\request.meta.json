{"data_mtime": 1757356835, "dep_lines": [9, 8, 1, 3, 4, 5, 6, 1, 1, 1, 1, 26, 24, 40, 33], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 20, 20, 30, 10, 10, 10, 5], "dependencies": ["urllib3.util.util", "urllib3.exceptions", "__future__", "io", "typing", "base64", "enum", "builtins", "pyexpat.errors", "pyexpat.model", "abc"], "hash": "5ee02c1014f9f030196144f0a4c1f91ebdc0d4e3e830dbcd21822e9db22a6dcf", "id": "urllib3.util.request", "ignore_all": true, "interface_hash": "5881f931475142188aec3f7b75f235fbee7a56454ad7b6ce3c7a2938070432a6", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\util\\request.py", "plugin_data": null, "size": 8411, "suppressed": ["brotli", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zstandard", "compression"], "version_id": "1.8.0"}