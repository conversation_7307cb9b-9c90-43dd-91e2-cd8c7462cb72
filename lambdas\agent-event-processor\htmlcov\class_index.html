<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">63%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-08 14:44 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1___init___py.html">src\agent_event_processor\__init__.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614___init___py.html">src\agent_event_processor\config\__init__.py</a></td>
                <td class="name left"><a href="z_20164841b8185614___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t13">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t13"><data value='DatabaseSettings'>DatabaseSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t39">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t39"><data value='AWSSettings'>AWSSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t49">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t49"><data value='LoggingSettings'>LoggingSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t62">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t62"><data value='ClientSettings'>ClientSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t81">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t81"><data value='Settings'>Settings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html">src\agent_event_processor\lambda_function.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>71</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="64 71">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa___init___py.html">src\agent_event_processor\models\__init__.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t15">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t15"><data value='EventType'>EventType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t36">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t36"><data value='AgentEvent'>AgentEvent</data></a></td>
                <td>21</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="15 21">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942___init___py.html">src\agent_event_processor\services\__init__.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t24">src\agent_event_processor\services\database_repository.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html#t24"><data value='AgentEventRepository'>AgentEventRepository</data></a></td>
                <td>25</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="3 25">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html">src\agent_event_processor\services\database_repository.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t35">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t35"><data value='DatabaseErrorCodes'>DatabaseErrorCodes</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t47">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t47"><data value='RedshiftDataAPIConnection'>RedshiftDataAPIConnection</data></a></td>
                <td>17</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="12 17">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t113">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t113"><data value='RedshiftDataAPICursor'>RedshiftDataAPICursor</data></a></td>
                <td>123</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="50 123">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t403">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t403"><data value='DatabaseService'>DatabaseService</data></a></td>
                <td>30</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="16 30">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t481">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t481"><data value='DatabaseError'>DatabaseError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t486">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t486"><data value='DimensionManager'>DimensionManager</data></a></td>
                <td>82</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="4 82">5%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t766">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t766"><data value='FactManager'>FactManager</data></a></td>
                <td>19</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="2 19">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>71</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="71 71">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t25">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t25"><data value='EventProcessor'>EventProcessor</data></a></td>
                <td>47</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="42 47">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe___init___py.html">src\agent_event_processor\utils\__init__.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_decorators_py.html">src\agent_event_processor\utils\decorators.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_decorators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html">src\agent_event_processor\utils\timezone_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="10 34">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_xml_parser_py.html#t16">src\agent_event_processor\utils\xml_parser.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_xml_parser_py.html#t16"><data value='AgentEventXMLParser'>AgentEventXMLParser</data></a></td>
                <td>31</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="24 31">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_xml_parser_py.html">src\agent_event_processor\utils\xml_parser.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_xml_parser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>695</td>
                <td>258</td>
                <td>0</td>
                <td class="right" data-ratio="437 695">63%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-08 14:44 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
