#!/usr/bin/env python3
"""
Test script for Agent Event Processor with LocalStack integration.

This script tests the complete flow: SQS -> Lambda -> Redshift Data API
It accepts AWS profile credentials and works with LocalStack.

Usage:
    python test_busied_out.py                    # Use default/test credentials
    python test_busied_out.py --profile admin-memo  # Use specific AWS profile
    python test_busied_out.py --help             # Show help
"""

import argparse
import json
import os
import sys
import time
from datetime import datetime
from typing import Dict, Any, Optional

import boto3
from botocore.exceptions import ClientError


class LocalStackTester:
    """Test agent event processing with LocalStack."""
    
    def __init__(self, profile: Optional[str] = None, endpoint: str = "http://localhost:4566", region: str = "us-east-1"):
        """Initialize the tester."""
        self.endpoint = endpoint
        self.region = region
        self.profile = profile
        
        # Set up AWS session
        if profile:
            os.environ['AWS_PROFILE'] = profile
            print(f"Using AWS Profile: {profile}")
        else:
            # Use test credentials for LocalStack
            os.environ['AWS_ACCESS_KEY_ID'] = 'test'
            os.environ['AWS_SECRET_ACCESS_KEY'] = 'test'
            print("Using test credentials for LocalStack")
        
        os.environ['AWS_DEFAULT_REGION'] = region
        
        # Initialize clients
        self.sqs = boto3.client('sqs', endpoint_url=endpoint, region_name=region)
        self.lambda_client = boto3.client('lambda', endpoint_url=endpoint, region_name=region)
        self.logs = boto3.client('logs', endpoint_url=endpoint, region_name=region)
        
        print(f"Initialized LocalStack tester (endpoint: {endpoint}, region: {region})")
    
    def wait_for_localstack(self, max_attempts: int = 30) -> bool:
        """Wait for LocalStack to be ready."""
        print("Waiting for LocalStack to be ready...")
        
        import requests
        
        for attempt in range(max_attempts):
            try:
                response = requests.get(f"{self.endpoint}/_localstack/health", timeout=5)
                health = response.json()
                
                if (health.get('services', {}).get('sqs') == 'available' and 
                    health.get('services', {}).get('lambda') == 'available'):
                    print("LocalStack is ready!")
                    return True
                    
            except Exception:
                pass
            
            print(f"Waiting for LocalStack... (attempt {attempt + 1}/{max_attempts})")
            time.sleep(2)
        
        print("LocalStack failed to start within timeout")
        return False
    
    def get_queue_url(self, queue_name: str = "agent-events-queue") -> str:
        """Get SQS queue URL."""
        try:
            response = self.sqs.get_queue_url(QueueName=queue_name)
            return response['QueueUrl']
        except ClientError as e:
            print(f"Error getting queue URL: {e}")
            raise
    
    def create_busied_out_event(self, agent_name: str = "test_agent_busied") -> str:
        """Create a busied out event XML."""
        timestamp = datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        
        return f"""<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>{timestamp}</timestamp>
    <eventType>AgentBusiedOut</eventType>
    <agencyOrElement>TestAgency</agencyOrElement>
    <agent>{agent_name}</agent>
    <reason>normal</reason>
    <workstation>TEST_WS_BUSIED</workstation>
    <operatorId>TEST_OP_{agent_name}</operatorId>
    <agentRole>Test Agent</agentRole>
    <agentUri>tel:+1234567890</agentUri>
    <mediaLabel>TEST_MEDIA_BUSIED</mediaLabel>
    <tenantGroup>TestAgency</tenantGroup>
    <deviceName>TestDevice_BUSIED</deviceName>
    <busiedOutAction>Manual</busiedOutAction>
    <busiedOutDuration>300</busiedOutDuration>
</LogEvent>"""
    
    def send_test_event(self, queue_url: str, agent_name: str = "test_agent_busied") -> str:
        """Send a test busied out event to SQS."""
        xml_event = self.create_busied_out_event(agent_name)
        
        try:
            response = self.sqs.send_message(
                QueueUrl=queue_url,
                MessageBody=xml_event
            )
            message_id = response['MessageId']
            print(f"Sent busied out event for agent {agent_name} (MessageId: {message_id})")
            return message_id
        except ClientError as e:
            print(f"Error sending message: {e}")
            raise
    
    def check_lambda_logs(self, function_name: str = "agent-event-processor", max_wait: int = 30) -> bool:
        """Check Lambda function logs for processing results."""
        log_group = f"/aws/lambda/{function_name}"
        
        print(f"Checking Lambda logs in {log_group}...")
        
        # Wait a bit for logs to appear
        time.sleep(5)
        
        try:
            # Get log streams
            streams_response = self.logs.describe_log_streams(
                logGroupName=log_group,
                orderBy='LastEventTime',
                descending=True,
                limit=5
            )
            
            if not streams_response.get('logStreams'):
                print("No log streams found")
                return False
            
            # Get recent log events
            latest_stream = streams_response['logStreams'][0]['logStreamName']
            
            events_response = self.logs.get_log_events(
                logGroupName=log_group,
                logStreamName=latest_stream,
                startFromHead=False,
                limit=50
            )
            
            events = events_response.get('events', [])
            if not events:
                print("No log events found")
                return False
            
            # Look for processing results
            success_found = False
            error_found = False
            
            print("\nRecent Lambda log events:")
            print("-" * 60)
            
            for event in events[-10:]:  # Show last 10 events
                timestamp = datetime.fromtimestamp(event['timestamp'] / 1000)
                message = event['message'].strip()
                print(f"[{timestamp}] {message}")
                
                if "Successfully processed event" in message or "successful_count" in message:
                    success_found = True
                elif "Failed to process" in message or "error" in message.lower():
                    error_found = True
            
            print("-" * 60)
            
            if success_found:
                print("✓ Event processing appears successful!")
                return True
            elif error_found:
                print("✗ Errors found in processing")
                return False
            else:
                print("? Processing status unclear from logs")
                return False
                
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                print(f"Log group {log_group} not found")
            else:
                print(f"Error checking logs: {e}")
            return False
    
    def test_complete_flow(self) -> bool:
        """Test the complete flow: SQS -> Lambda -> Redshift."""
        print("\n" + "="*70)
        print("Testing Complete Agent Event Processing Flow")
        print("="*70)
        
        try:
            # 1. Wait for LocalStack
            if not self.wait_for_localstack():
                return False
            
            # 2. Get queue URL
            print("\nStep 1: Getting SQS queue URL...")
            queue_url = self.get_queue_url()
            print(f"Queue URL: {queue_url}")
            
            # 3. Send test event
            print("\nStep 2: Sending busied out event...")
            message_id = self.send_test_event(queue_url)
            
            # 4. Wait for processing
            print("\nStep 3: Waiting for Lambda processing...")
            time.sleep(10)  # Give Lambda time to process
            
            # 5. Check logs
            print("\nStep 4: Checking Lambda logs...")
            success = self.check_lambda_logs()
            
            # 6. Summary
            print("\n" + "="*70)
            if success:
                print("✓ TEST PASSED: Busied out event processed successfully!")
                print("The complete flow is working:")
                print("  1. Event sent to SQS ✓")
                print("  2. Lambda triggered ✓")
                print("  3. Event processed ✓")
                print("  4. Data stored in Redshift ✓")
            else:
                print("✗ TEST FAILED: Issues found in processing")
                print("Check the logs above for details")
            
            print("="*70)
            return success
            
        except Exception as e:
            print(f"\n✗ TEST FAILED with exception: {e}")
            return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Test Agent Event Processor with LocalStack",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_busied_out.py                    # Use test credentials
  python test_busied_out.py --profile admin-memo  # Use AWS profile
  
Environment Variables:
  AWS_PROFILE     - AWS profile to use (can be set instead of --profile)
  AWS_ENDPOINT_URL - LocalStack endpoint (default: http://localhost:4566)
        """
    )
    
    parser.add_argument(
        '--profile',
        help='AWS profile to use for credentials'
    )
    parser.add_argument(
        '--endpoint',
        default='http://localhost:4566',
        help='LocalStack endpoint URL (default: http://localhost:4566)'
    )
    parser.add_argument(
        '--region',
        default='us-east-1',
        help='AWS region (default: us-east-1)'
    )
    
    args = parser.parse_args()
    
    # Use profile from args or environment
    profile = args.profile or os.getenv('AWS_PROFILE')
    
    print("Agent Event Processor - LocalStack Integration Test")
    print("=" * 60)
    
    # Create tester and run test
    tester = LocalStackTester(
        profile=profile,
        endpoint=args.endpoint,
        region=args.region
    )
    
    success = tester.test_complete_flow()
    
    if success:
        print("\n🎉 All tests passed! The Lambda is working correctly with LocalStack.")
        sys.exit(0)
    else:
        print("\n❌ Tests failed. Check the setup and try again.")
        print("\nTroubleshooting:")
        print("1. Make sure LocalStack is running: docker-compose up -d")
        print("2. Run setup script: ./scripts/setup_localstack_complete.ps1")
        print("3. Check Docker logs: docker logs agent-event-processor-localstack")
        sys.exit(1)


if __name__ == "__main__":
    main()
