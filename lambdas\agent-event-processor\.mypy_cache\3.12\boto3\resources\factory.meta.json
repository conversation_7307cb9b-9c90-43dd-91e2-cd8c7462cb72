{"data_mtime": 1757356840, "dep_lines": [10, 11, 12, 7, 8, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 20, 20, 30, 30], "dependencies": ["boto3.resources.base", "boto3.utils", "botocore.hooks", "logging", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "abc", "botocore"], "hash": "26f128a34708d25ff8cbbc348eda31f12694b852fff18f0aadeb11e4de952c77", "id": "boto3.resources.factory", "ignore_all": true, "interface_hash": "f271f0bb7cb0d3fec75a54ed0d3c50d6400b4fc24fce3041410fe0f596f7858d", "mtime": 1757092242, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\boto3-stubs\\resources\\factory.pyi", "plugin_data": null, "size": 577, "suppressed": [], "version_id": "1.8.0"}