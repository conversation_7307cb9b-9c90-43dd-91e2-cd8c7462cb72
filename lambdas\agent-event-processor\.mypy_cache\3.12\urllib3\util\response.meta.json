{"data_mtime": 1757356835, "dep_lines": [3, 4, 6, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["http.client", "email.errors", "urllib3.exceptions", "__future__", "http", "builtins", "pyexpat.errors", "pyexpat.model", "abc", "email", "email.message", "io", "typing"], "hash": "bd013adfdba81218f5be98c4771bb994d22124249466477ba6a965508d0164e0", "id": "urllib3.util.response", "ignore_all": true, "interface_hash": "c4192b653f78ab9e535e0032999b7ac687b13a20e21f6819b69f8c3a06c0ccd2", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\util\\response.py", "plugin_data": null, "size": 3374, "suppressed": [], "version_id": "1.8.0"}