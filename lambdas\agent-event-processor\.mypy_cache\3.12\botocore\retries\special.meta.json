{"data_mtime": 1757356839, "dep_lines": [10, 11, 7, 8, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["botocore.retries.base", "botocore.retries.standard", "logging", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "abc"], "hash": "984998c1752a756b692a8c4e3b4f8367e96b73dd657bf5a674ef17e23fd44350", "id": "botocore.retries.special", "ignore_all": true, "interface_hash": "830699e2a18532f394b7bf6abd77e2d88307847351f8915ee87da817d453f64b", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\retries\\special.pyi", "plugin_data": null, "size": 530, "suppressed": [], "version_id": "1.8.0"}