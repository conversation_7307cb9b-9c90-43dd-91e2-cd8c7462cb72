{"data_mtime": 1757356836, "dep_lines": [21, 17, 18, 19, 21, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 20, 5, 20, 20, 30], "dependencies": ["tenacity._utils", "abc", "random", "typing", "tenacity", "builtins", "pyexpat.errors", "pyexpat.model", "datetime"], "hash": "d68c9ed1eac0a89912232b044087e0b595c7ba7bfbb77d41c08da01cbd45b129", "id": "tenacity.wait", "ignore_all": true, "interface_hash": "9617cf930939d48b974c78da8b174dac923a7f740bd438fa6ca82c878df82eec", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\tenacity\\wait.py", "plugin_data": null, "size": 8049, "suppressed": [], "version_id": "1.8.0"}