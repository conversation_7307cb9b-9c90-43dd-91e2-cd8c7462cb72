{"data_mtime": 1757356838, "dep_lines": [19, 20, 21, 22, 7, 16, 17, 19, 28, 1, 3, 4, 5, 6, 8, 9, 10, 13, 15, 16, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 5, 20, 25, 5, 10, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["pydantic._internal._typing_extra", "pydantic._internal._core_utils", "pydantic._internal._forward_ref", "pydantic._internal._utils", "collections.abc", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic._internal", "pydantic.main", "__future__", "sys", "types", "typing", "collections", "contextlib", "<PERSON><PERSON><PERSON>", "itertools", "weakref", "typing_extensions", "typing_inspection", "builtins", "pyexpat.errors", "pyexpat.model", "_collections_abc", "_typeshed", "abc", "pydantic._internal._model_construction"], "hash": "ecf3545a24ca62c8833061fd6a2de57b0a6d7717bd4afbe3eb2df79e0054bddc", "id": "pydantic._internal._generics", "ignore_all": true, "interface_hash": "cee28ebbdd1def41cc8b7dc52a9491f13426bc223ae4631982d8946cac77382f", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_generics.py", "plugin_data": null, "size": 23849, "suppressed": [], "version_id": "1.8.0"}