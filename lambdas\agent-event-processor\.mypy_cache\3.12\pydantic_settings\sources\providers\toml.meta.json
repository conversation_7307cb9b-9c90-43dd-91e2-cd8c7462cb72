{"data_mtime": 1757356839, "dep_lines": [12, 13, 16, 3, 5, 6, 7, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22], "dep_prios": [5, 5, 25, 5, 10, 5, 5, 25, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["pydantic_settings.sources.base", "pydantic_settings.sources.types", "pydantic_settings.main", "__future__", "sys", "pathlib", "typing", "<PERSON><PERSON><PERSON><PERSON>", "builtins", "pyexpat.errors", "pyexpat.model", "_typeshed", "abc", "os", "pydantic", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "re"], "hash": "e64f703096caad4a9736208ce46d6162708e119354249053033170e8fbf62ba0", "id": "pydantic_settings.sources.providers.toml", "ignore_all": true, "interface_hash": "8371f6ceb91d105422cb75fa2b65b932c0e4cda20156fdc63d419b04f3cffa7b", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_settings\\sources\\providers\\toml.py", "plugin_data": null, "size": 1827, "suppressed": ["to<PERSON>li"], "version_id": "1.8.0"}