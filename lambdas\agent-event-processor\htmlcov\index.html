<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">63%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-08 14:44 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1___init___py.html">src\agent_event_processor\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614___init___py.html">src\agent_event_processor\config\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html">src\agent_event_processor\config\settings.py</a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html">src\agent_event_processor\lambda_function.py</a></td>
                <td>71</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="64 71">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa___init___py.html">src\agent_event_processor\models\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html">src\agent_event_processor\models\events.py</a></td>
                <td>51</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="45 51">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942___init___py.html">src\agent_event_processor\services\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_repository_py.html">src\agent_event_processor\services\database_repository.py</a></td>
                <td>39</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="17 39">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html">src\agent_event_processor\services\database_service.py</a></td>
                <td>342</td>
                <td>187</td>
                <td>0</td>
                <td class="right" data-ratio="155 342">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html">src\agent_event_processor\services\event_processor.py</a></td>
                <td>67</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="62 67">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe___init___py.html">src\agent_event_processor\utils\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_decorators_py.html">src\agent_event_processor\utils\decorators.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html">src\agent_event_processor\utils\timezone_utils.py</a></td>
                <td>34</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="10 34">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_xml_parser_py.html">src\agent_event_processor\utils\xml_parser.py</a></td>
                <td>41</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="34 41">83%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>695</td>
                <td>258</td>
                <td>0</td>
                <td class="right" data-ratio="437 695">63%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-08 14:44 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_09c0692e822942fe_xml_parser_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_145eef247bfb46b6___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
