{"data_mtime": 1757363959, "dep_lines": [12, 6, 1, 1], "dep_prios": [5, 5, 5, 30], "dependencies": ["_pytest.reports", "typing", "builtins", "abc"], "hash": "d415b78c3452887a86a9dce619273b2df42dee7c1cd30d655511cc1c7705110b", "id": "_pytest.pytester_assertions", "ignore_all": true, "interface_hash": "0bf73abc30b98938b6faf4827cd838ad9349c3be35de2044a5f72ffddfa5146e", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\pytester_assertions.py", "plugin_data": null, "size": 2327, "suppressed": [], "version_id": "1.8.0"}