{"data_mtime": 1757364037, "dep_lines": [3, 3, 3, 1, 3, 1, 1], "dep_prios": [10, 10, 10, 5, 20, 5, 30], "dependencies": ["requests.cookies", "requests.models", "requests.utils", "typing", "requests", "builtins", "abc"], "hash": "526cc1b3356b8346910e8bf6e30b74c4c144d0cfac2ebd34a6aca4ce82b9ca33", "id": "requests.auth", "ignore_all": true, "interface_hash": "6184c7bb880ba119715433a26d34d110b02fedb58f499e65a1effe2239a18934", "mtime": 1757363991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\requests-stubs\\auth.pyi", "plugin_data": null, "size": 1191, "suppressed": [], "version_id": "1.8.0"}