{"data_mtime": 1757356836, "dep_lines": [19, 16, 17, 19, 22, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 20, 25, 5, 20, 20, 30], "dependencies": ["tenacity._utils", "abc", "typing", "tenacity", "threading", "builtins", "pyexpat.errors", "pyexpat.model", "datetime"], "hash": "c10bb019f08bc3c387d42df1d06f651fdc6d242ca182f89e3d0e341d1014839e", "id": "tenacity.stop", "ignore_all": true, "interface_hash": "42a0b74ba64ee538f0c60a5a27bcb663f249e030243a49447343764ce2887cc1", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\tenacity\\stop.py", "plugin_data": null, "size": 4113, "suppressed": [], "version_id": "1.8.0"}