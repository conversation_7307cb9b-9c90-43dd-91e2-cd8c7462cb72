{"data_mtime": 1757356839, "dep_lines": [17, 7, 8, 11, 13, 14, 16, 18, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["botocore.crt.auth", "collections.abc", "http.client", "urllib.parse", "botocore.awsrequest", "botocore.compat", "botocore.credentials", "botocore.utils", "logging", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "abc", "email", "email.message", "http", "urllib"], "hash": "e339166f299fa73613b64360dd6afee63a131b1f654b7c89a216b833b7bfcdcc", "id": "botocore.auth", "ignore_all": true, "interface_hash": "687923c4e476e6b220972046a480091b3e1e4522b5dc001450316cf503960738", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\auth.pyi", "plugin_data": null, "size": 5591, "suppressed": [], "version_id": "1.8.0"}