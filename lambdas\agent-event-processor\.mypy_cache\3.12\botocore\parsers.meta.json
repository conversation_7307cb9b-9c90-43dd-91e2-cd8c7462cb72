{"data_mtime": 1757356839, "dep_lines": [7, 12, 13, 15, 16, 8, 9, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["collections.abc", "botocore.compat", "botocore.eventstream", "botocore.model", "botocore.utils", "io", "logging", "typing", "builtins", "pyexpat.errors", "pyexpat.model", "abc", "datetime"], "hash": "c42eaa64723d84820c09c5e73449c3cb116323471433dc1059d2c63dca41615e", "id": "botocore.parsers", "ignore_all": true, "interface_hash": "2717a676847a0d8b023cf01069f1b49281a874a22b026be04755230beacb9064", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\parsers.pyi", "plugin_data": null, "size": 2843, "suppressed": [], "version_id": "1.8.0"}