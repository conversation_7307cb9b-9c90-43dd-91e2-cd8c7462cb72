{"data_mtime": 1757364037, "dep_lines": [3, 9, 9, 9, 9, 10, 1, 2, 4, 5, 6, 7, 9, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 5, 10, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30], "dependencies": ["collections.abc", "requests.compat", "requests.cookies", "requests.exceptions", "requests.structures", "requests.models", "sys", "_typeshed", "contextlib", "io", "typing", "typing_extensions", "requests", "builtins", "abc", "collections", "os"], "hash": "6084136b35ce85063d4c5b47a5d636dbf1358cc01820132e33be6cff4f2ab704", "id": "requests.utils", "ignore_all": true, "interface_hash": "787d5fcbda0eea5659721b9f882834e195d17814c96f87252a324e7dfa40bca8", "mtime": 1757363991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\requests-stubs\\utils.pyi", "plugin_data": null, "size": 3043, "suppressed": [], "version_id": "1.8.0"}