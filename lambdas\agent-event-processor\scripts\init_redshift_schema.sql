-- Initialize Redshift database schema for agent event processing
-- This script creates the necessary tables for agent event processing in Redshift
-- Adapted from PostgreSQL schema with Redshift-specific optimizations

-- Create dimension tables

-- Tenant dimension table
CREATE TABLE IF NOT EXISTS dim_tenant (
    tenant_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    tenant_name VARCHAR(100) NOT NULL UNIQUE,
    timezone_name VARCHAR(50) DEFAULT 'UTC',
    created_at TIMESTAMP DEFAULT GETDATE(),
    updated_at TIMESTAMP DEFAULT GETDATE()
);

-- Agent dimension table with SCD Type 2 support
CREATE TABLE IF NOT EXISTS dim_agent (
    agent_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_name VARCHAR(100) NOT NULL,
    operator_id VARCHAR(50),
    agent_role VARCHAR(100),
    agent_uri VARCHAR(200),
    workstation VARCHAR(200),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    valid_from TIMESTA<PERSON> DEFAULT GETDATE(),
    valid_to TIMESTAMP,
    is_current BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT GETDATE(),
    updated_at TIMESTAMP DEFAULT GETDATE()
);

-- Queue dimension table with SCD Type 2 support
CREATE TABLE IF NOT EXISTS dim_queue (
    queue_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    ring_group_name VARCHAR(200) NOT NULL,
    ring_group_uri VARCHAR(500),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    valid_from TIMESTAMP DEFAULT GETDATE(),
    valid_to TIMESTAMP,
    is_current BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT GETDATE(),
    updated_at TIMESTAMP DEFAULT GETDATE()
);

-- Date dimension table
CREATE TABLE IF NOT EXISTS dim_date (
    date_key INTEGER PRIMARY KEY,
    full_date DATE,
    year INTEGER,
    quarter INTEGER,
    month INTEGER,
    day INTEGER,
    day_of_week INTEGER,
    day_name VARCHAR(10),
    month_name VARCHAR(10),
    is_weekend BOOLEAN
);

-- Time dimension table
CREATE TABLE IF NOT EXISTS dim_time (
    time_key INTEGER PRIMARY KEY,
    hour INTEGER,
    minute INTEGER,
    second INTEGER,
    time_of_day VARCHAR(16),
    hour_name VARCHAR(10)
);

-- Fact table for agent events
CREATE TABLE IF NOT EXISTS fact_agent_event (
    state_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agent(agent_key),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    date_key INTEGER REFERENCES dim_date(date_key),
    time_key INTEGER REFERENCES dim_time(time_key),
    queue_key BIGINT REFERENCES dim_queue(queue_key),
    event_timestamp_utc TIMESTAMP NOT NULL,
    event_timestamp_local TIMESTAMP,
    event_type VARCHAR(50) NOT NULL,
    reason_code VARCHAR(100),
    busied_out_action VARCHAR(100),
    busied_out_duration INTEGER,
    media_label VARCHAR(200),
    workstation VARCHAR(200),
    device_name VARCHAR(50),
    -- Store complete event data as JSON for flexibility while maintaining star schema
    event_data_json SUPER,
    processed_at_utc TIMESTAMP DEFAULT GETDATE(),
    -- Add unique constraint to prevent duplicate events
    CONSTRAINT unique_agent_event UNIQUE (agent_key, tenant_key, event_timestamp_utc, event_type, media_label)
);

-- Fact table for agent state intervals (derived from events)
CREATE TABLE IF NOT EXISTS fact_agent_intervals (
    interval_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agent(agent_key),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    start_date_key INTEGER REFERENCES dim_date(date_key),
    start_time_key INTEGER REFERENCES dim_time(time_key),
    end_date_key INTEGER REFERENCES dim_date(date_key),
    end_time_key INTEGER REFERENCES dim_time(time_key),
    queue_key BIGINT REFERENCES dim_queue(queue_key),
    interval_start_utc TIMESTAMP NOT NULL,
    interval_end_utc TIMESTAMP,
    interval_start_local TIMESTAMP,
    interval_end_local TIMESTAMP,
    interval_duration_seconds INTEGER,
    state_type VARCHAR(50) NOT NULL,
    reason_code VARCHAR(100),
    is_current_interval BOOLEAN DEFAULT FALSE,
    processed_at_utc TIMESTAMP DEFAULT GETDATE()
);

-- Fact table for ACD sessions
CREATE TABLE IF NOT EXISTS fact_acd_session (
    session_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agent(agent_key),
    queue_key BIGINT REFERENCES dim_queue(queue_key),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    date_key INTEGER REFERENCES dim_date(date_key),
    login_time_key INTEGER REFERENCES dim_time(time_key),
    logout_time_key INTEGER REFERENCES dim_time(time_key),
    acd_login_utc TIMESTAMP NOT NULL,
    acd_logout_utc TIMESTAMP,
    acd_login_local TIMESTAMP,
    acd_logout_local TIMESTAMP,
    session_duration_seconds INTEGER,
    is_active_session BOOLEAN DEFAULT FALSE,
    processed_at_utc TIMESTAMP DEFAULT GETDATE()
);

-- Create indexes for performance (Redshift-optimized)
-- Note: Redshift automatically creates indexes for PRIMARY KEY and UNIQUE constraints

-- Compound sort keys for dimension tables (Redshift-specific optimization)
-- These help with query performance on filtered and joined data
CREATE INDEX IF NOT EXISTS idx_dim_agent_tenant_name 
ON dim_agent(tenant_key, agent_name, is_current);

CREATE INDEX IF NOT EXISTS idx_dim_queue_tenant_name 
ON dim_queue(tenant_key, ring_group_name, is_current);

-- Fact table indexes optimized for time-series queries
CREATE INDEX IF NOT EXISTS idx_fact_agent_event_timestamp 
ON fact_agent_event(event_timestamp_utc);

CREATE INDEX IF NOT EXISTS idx_fact_agent_event_agent_time 
ON fact_agent_event(agent_key, event_timestamp_utc);

CREATE INDEX IF NOT EXISTS idx_fact_agent_intervals_current 
ON fact_agent_intervals(agent_key, is_current_interval);

CREATE INDEX IF NOT EXISTS idx_fact_acd_session_active 
ON fact_acd_session(agent_key, queue_key, is_active_session);

-- Insert initial test data
INSERT INTO dim_tenant (tenant_name, timezone_name) 
VALUES 
    ('Brandon911', 'America/Winnipeg'),
    ('TestTenant', 'UTC');

-- Note: Date and time dimension population will be done via separate script
-- due to Redshift's limited generate_series support

-- Create distribution and sort keys for optimal performance (Redshift-specific)
-- These optimize data distribution across nodes and query performance

-- Set distribution styles for dimension tables (small tables - distribute to all nodes)
ALTER TABLE dim_tenant SET DISTSTYLE ALL;
ALTER TABLE dim_date SET DISTSTYLE ALL;
ALTER TABLE dim_time SET DISTSTYLE ALL;

-- Set distribution keys for larger dimension tables
ALTER TABLE dim_agent SET DISTKEY(tenant_key);
ALTER TABLE dim_queue SET DISTKEY(tenant_key);

-- Set distribution keys for fact tables to co-locate with dimension data
ALTER TABLE fact_agent_event SET DISTKEY(agent_key);
ALTER TABLE fact_agent_intervals SET DISTKEY(agent_key);
ALTER TABLE fact_acd_session SET DISTKEY(agent_key);

-- Set sort keys for optimal query performance
ALTER TABLE fact_agent_event SET SORTKEY(event_timestamp_utc, agent_key);
ALTER TABLE fact_agent_intervals SET SORTKEY(interval_start_utc, agent_key);
ALTER TABLE fact_acd_session SET SORTKEY(acd_login_utc, agent_key);

COMMIT;
