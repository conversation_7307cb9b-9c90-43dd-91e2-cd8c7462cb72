{"data_mtime": 1757363959, "dep_lines": [12, 7, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.agent_event_processor.models.events", "datetime", "pytest", "pydantic", "builtins", "_collections_abc", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.python_api", "abc", "contextlib", "enum", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_core", "pydantic_core._pydantic_core", "re", "src", "src.agent_event_processor", "src.agent_event_processor.models", "types", "typing", "typing_extensions"], "hash": "41b061598b52c7c4a4e3107bad33991e67af96a3aa63816f44b2ef5e27f3a500", "id": "tests.unit.test_models", "ignore_all": false, "interface_hash": "d5d5c2d2686cfcfffcaa9b0889da50bd248245931c691f525f8d6cb8f16807c5", "mtime": 1757363740, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "tests\\unit\\test_models.py", "plugin_data": null, "size": 4390, "suppressed": [], "version_id": "1.8.0"}