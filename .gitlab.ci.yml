variables:
  PYTHON_VERSION: "3.12"
  PACKAGE_NAME: "smartanalytics-processors"
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    - if: $CI_COMMIT_BRANCH
    - if: $CI_COMMIT_TAG =~ /^rel-/

default:
  cache:
    key: "pip-$PYTHON_VERSION"
    paths:
      - .cache/pip

stages:
  - test
  - build
  - publish

# Static App Sec Testing
include:
  - template: Jobs/SAST.gitlab-ci.yml

testing:
  stage: test
  script:
    - python --version || true
    - python -m pip install --upgrade pip
    # install dev + runtime deps if present; fall back to just runtime
    - |
      if [ -f requirements-dev.txt ]; then
        pip install -r requirements-dev.txt
      fi
      if [ -f requirements.txt ]; then
        pip install -r requirements.txt
      fi
    - if [ -f .pre-commit-config.yaml ]; then pre-commit run --all-files || true; fi
    - if [ -f .flake8 ]; then flake8 .; fi
    - |
      if [ -d tests ]; then
        pytest -q --maxfail=1 --disable-warnings --junitxml=report.xml --cov=. --cov-report=xml || pytest -q --junitxml=report.xml
      else
        echo "No tests/ directory; skipping pytest."
      fi
  artifacts:
    reports:
      junit: report.xml
    paths:
      - .coverage
      - coverage.xml
    expire_in: 1 week
  tags:
    - codebuild-gitlab-ci-${CI_PROJECT_NAMESPACE_SLUG}-$CI_PROJECT_ID-$CI_PIPELINE_IID-$CI_JOB_NAME
    - image:custom-linux-python:$PYTHON_VERSION

build:
  stage: build
  script:
    - apt-get update && apt-get install -y zip rsync
    - python -m pip install --upgrade pip
    - mkdir -p build
    - |
      set -euo pipefail
      echo "Packaging Lambdas from ./lambdas/*/src ..."
      for LAMBDA in lambdas/*; do
        [ -d "$LAMBDA/src" ] || continue

        NAME="$(basename "$LAMBDA")"
        WORK="build/$NAME"
        echo "==> Building $NAME"
        rm -rf "$WORK" && mkdir -p "$WORK"

        # 1) shared/root requirements (optional)
        if [ -f "requirements.txt" ]; then
          pip install --no-warn-script-location --upgrade -r requirements.txt -t "$WORK"
        fi

        # 2) per-lambda requirements (optional)
        if [ -f "$LAMBDA/requirements.txt" ]; then
          pip install --no-warn-script-location --upgrade -r "$LAMBDA/requirements.txt" -t "$WORK"
        fi

        # 3) copy code (contents of src/* to the root of the zip)
        rsync -a --delete --exclude '__pycache__' "$LAMBDA/src/" "$WORK/"

        # 4) create the zip
        (cd "$WORK" && zip -r "../smart-analytics-${NAME}.zip" .)
      done
      echo "Built archives:"
      ls -l build/
  artifacts:
    paths:
      - build/
    expire_in: 1 hour
  tags:
    - codebuild-gitlab-ci-${CI_PROJECT_NAMESPACE_SLUG}-$CI_PROJECT_ID-$CI_PIPELINE_IID-$CI_JOB_NAME
    - image:custom-linux-python:$PYTHON_VERSION

publish:
  stage: publish
  needs: ["build"]
  script:
    - |
      PACKAGE_VERSION="$CI_COMMIT_TAG"
      if [ -z "$PACKAGE_VERSION" ]; then
        PACKAGE_VERSION=$(date +"%Y-%m-%d_%H-%M-%S")
      fi
      echo "PACKAGE_VERSION: $PACKAGE_VERSION"

      shopt -s nullglob
      for file in build/*.zip; do
        base=$(basename "$file")
        url="${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/${PACKAGE_NAME}/${PACKAGE_VERSION}/${base}"
        echo "Uploading $base -> $url"
        curl --header "JOB-TOKEN: $CI_JOB_TOKEN" --upload-file "$file" "$url"
      done
  rules:
    # adjust namespace conditions if needed
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"
    - if: $CI_COMMIT_BRANCH == "main" && $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG =~ /^rel-/
  tags:
    - codebuild-gitlab-ci-${CI_PROJECT_NAMESPACE_SLUG}-$CI_PROJECT_ID-$CI_PIPELINE_IID-$CI_JOB_NAME
    - image:custom-linux-python:$PYTHON_VERSION
