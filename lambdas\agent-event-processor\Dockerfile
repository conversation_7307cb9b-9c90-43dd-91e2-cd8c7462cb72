FROM public.ecr.aws/lambda/python:3.12

# Install AWS CLI for credential management
RUN yum update -y && \
    yum install -y unzip curl && \
    curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
    unzip awscliv2.zip && \
    ./aws/install && \
    rm -rf awscliv2.zip aws && \
    yum clean all

COPY requirements.txt ${LAMBDA_TASK_ROOT}
RUN pip install --no-cache-dir -r requirements.txt

COPY src/agent_event_processor/ ${LAMBDA_TASK_ROOT}/agent_event_processor/

ENV PYTHONPATH="${LAMBDA_TASK_ROOT}"
ENV PYTHONUNBUFFERED=1

# Create AWS credentials directory
RUN mkdir -p /root/.aws

CMD ["agent_event_processor.lambda_function.lambda_handler"]
