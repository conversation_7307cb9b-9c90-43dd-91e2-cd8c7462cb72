{"data_mtime": 1757363959, "dep_lines": [1, 24, 25, 29, 30, 31, 32, 35, 425, 1, 2, 3, 4, 5, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 25, 25, 20, 10, 10, 10, 5, 5, 30, 30], "dependencies": ["collections.abc", "_pytest._code", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.outcomes", "_pytest.warning_types", "_pytest.nodes", "_pytest.scope", "collections", "dataclasses", "inspect", "warnings", "typing", "builtins", "abc", "enum"], "hash": "cafab028cd25c7ac5d38fe22236608bcad721071e93877d27f5d6c1186fec3d5", "id": "_pytest.mark.structures", "ignore_all": true, "interface_hash": "1140c97a5a75707dc6c866f1709412eec9a9fb880f140998f2b30f81d3f2edbb", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\mark\\structures.py", "plugin_data": null, "size": 21219, "suppressed": [], "version_id": "1.8.0"}