{"data_mtime": 1757363959, "dep_lines": [25, 15, 16, 17, 27, 32, 33, 36, 37, 39, 2, 3, 4, 5, 6, 7, 8, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest.nodes", "_pytest.reports", "_pytest.stash", "_pytest.pathlib", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.monkeypatch", "dataclasses", "os", "re", "tempfile", "pathlib", "shutil", "typing", "typing_extensions", "builtins", "_collections_abc", "_pytest.hookspec", "_pytest.mark", "_typeshed", "abc", "enum", "pluggy", "pluggy._hooks"], "hash": "35fcab0f8845ddac6c301c7be0ff4af8f7e194f5f2b91a62a9272894a2d95b99", "id": "_pytest.tmpdir", "ignore_all": true, "interface_hash": "2abff3a310e368e7a7ac4d00f30a89452c1961e101dd79704390f9a3e74463c9", "mtime": 1757092213, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\tmpdir.py", "plugin_data": null, "size": 11708, "suppressed": [], "version_id": "1.8.0"}