{".class": "MypyFile", "_fullname": "pydantic._internal._decorators", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyDecoratorCallable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._decorators.AnyDecoratorCallable", "line": 670, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.classmethod"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.staticmethod"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "functools.partialmethod"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "ComputedFieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.ComputedFieldInfo", "kind": "Gdef"}, "DecoratedType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.DecoratedType", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "pydantic._internal._decorators.DecoratedType", "line": 157, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.DecoratedType", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "builtins.classmethod"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.DecoratedType", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "builtins.staticmethod"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.DecoratedType", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "builtins.property"]}}}, "Decorator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators.Decorator", "name": "Decorator", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._decorators.Decorator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 231, "name": "cls_ref", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 232, "name": "cls_var_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 233, "name": "func", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 234, "name": "shim", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 235, "name": "info", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic._internal._decorators", "mro": ["pydantic._internal._decorators.Decorator", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic._internal._decorators.Decorator.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "cls_ref", "cls_var_name", "func", "shim", "info"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.Decorator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "cls_ref", "cls_var_name", "func", "shim", "info"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.Decorator"}, "builtins.str", "builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Decorator", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic._internal._decorators.Decorator.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cls_ref"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cls_var_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "func"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shim"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "info"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["cls_ref", "cls_var_name", "func", "shim", "info"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._decorators.Decorator.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["cls_ref", "cls_var_name", "func", "shim", "info"], "arg_types": ["builtins.str", "builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Decorator", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic._internal._decorators.Decorator.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["cls_ref", "cls_var_name", "func", "shim", "info"], "arg_types": ["builtins.str", "builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Decorator", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "bind_to_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.Decorator.bind_to_cls", "name": "bind_to_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.Decorator"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind_to_cls of Decorator", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.Decorator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3], "arg_names": ["cls_", "cls_var_name", "shim", "info"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._decorators.Decorator.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3], "arg_names": ["cls_", "cls_var_name", "shim", "info"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of Decorator", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.Decorator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pydantic._internal._decorators.Decorator.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3], "arg_names": ["cls_", "cls_var_name", "shim", "info"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of Decorator", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.Decorator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "cls_ref": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.Decorator.cls_ref", "name": "cls_ref", "type": "builtins.str"}}, "cls_var_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.Decorator.cls_var_name", "name": "cls_var_name", "type": "builtins.str"}}, "func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_settable_property", "is_ready"], "fullname": "pydantic._internal._decorators.Decorator.func", "name": "func", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.Decorator.info", "name": "info", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}}}, "shim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.Decorator.shim", "name": "shim", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.Decorator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "id": 1, "name": "DecoratorInfoType", "namespace": "pydantic._internal._decorators.Decorator", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.Decorator"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["DecoratorInfoType"], "typeddict_type": null}}, "DecoratorInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._decorators.DecoratorInfo", "line": 146, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}}}, "DecoratorInfoType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfoType", "name": "DecoratorInfoType", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.RootValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "pydantic.fields.ComputedFieldInfo"]}, "values": [], "variance": 0}}, "DecoratorInfos": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators.DecoratorInfos", "name": "DecoratorInfos", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._decorators.DecoratorInfos", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 420, "name": "validators", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 421, "name": "field_validators", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 422, "name": "root_validators", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.RootValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 423, "name": "field_serializers", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 424, "name": "model_serializers", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ModelSerializerDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 425, "name": "model_validators", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 426, "name": "computed_fields", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic.fields.ComputedFieldInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic._internal._decorators", "mro": ["pydantic._internal._decorators.DecoratorInfos", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic._internal._decorators.DecoratorInfos.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "validators", "field_validators", "root_validators", "field_serializers", "model_serializers", "model_validators", "computed_fields"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.DecoratorInfos.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "validators", "field_validators", "root_validators", "field_serializers", "model_serializers", "model_validators", "computed_fields"], "arg_types": ["pydantic._internal._decorators.DecoratorInfos", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.RootValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ModelSerializerDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic.fields.ComputedFieldInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DecoratorInfos", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic._internal._decorators.DecoratorInfos.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validators"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "field_validators"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "root_validators"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "field_serializers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model_serializers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model_validators"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "computed_fields"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["validators", "field_validators", "root_validators", "field_serializers", "model_serializers", "model_validators", "computed_fields"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._decorators.DecoratorInfos.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["validators", "field_validators", "root_validators", "field_serializers", "model_serializers", "model_validators", "computed_fields"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.RootValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ModelSerializerDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic.fields.ComputedFieldInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of DecoratorInfos", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic._internal._decorators.DecoratorInfos.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["validators", "field_validators", "root_validators", "field_serializers", "model_serializers", "model_validators", "computed_fields"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.RootValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ModelSerializerDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic.fields.ComputedFieldInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of DecoratorInfos", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model_dc"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._decorators.DecoratorInfos.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model_dc"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of DecoratorInfos", "ret_type": "pydantic._internal._decorators.DecoratorInfos", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pydantic._internal._decorators.DecoratorInfos.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model_dc"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of DecoratorInfos", "ret_type": "pydantic._internal._decorators.DecoratorInfos", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "computed_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.DecoratorInfos.computed_fields", "name": "computed_fields", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic.fields.ComputedFieldInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}}, "field_serializers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.DecoratorInfos.field_serializers", "name": "field_serializers", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}}, "field_validators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.DecoratorInfos.field_validators", "name": "field_validators", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}}, "model_serializers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.DecoratorInfos.model_serializers", "name": "model_serializers", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ModelSerializerDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}}, "model_validators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.DecoratorInfos.model_validators", "name": "model_validators", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}}, "root_validators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.DecoratorInfos.root_validators", "name": "root_validators", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.RootValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}}, "validators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.DecoratorInfos.validators", "name": "validators", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.ValidatorDecoratorInfo"], "type_ref": "pydantic._internal._decorators.Decorator"}], "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.DecoratorInfos.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators.DecoratorInfos", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FieldSerializerDecoratorInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "name": "FieldSerializerDecoratorInfo", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 106, "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 107, "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 108, "name": "return_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 109, "name": "when_used", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 110, "name": "check_fields", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic._internal._decorators", "mro": ["pydantic._internal._decorators.FieldSerializerDecoratorInfo", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "fields", "mode", "return_type", "when_used", "check_fields"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "fields", "mode", "return_type", "when_used", "check_fields"], "arg_types": ["pydantic._internal._decorators.FieldSerializerDecoratorInfo", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FieldSerializerDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fields"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "return_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "when_used"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "check_fields"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["fields", "mode", "return_type", "when_used", "check_fields"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["fields", "mode", "return_type", "when_used", "check_fields"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FieldSerializerDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["fields", "mode", "return_type", "when_used", "check_fields"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FieldSerializerDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "check_fields": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo.check_fields", "name": "check_fields", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "decorator_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo.decorator_repr", "name": "decorator_repr", "type": "builtins.str"}}, "fields": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}}, "mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo.mode", "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}}}, "return_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo.return_type", "name": "return_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "when_used": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo.when_used", "name": "when_used", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.FieldSerializerDecoratorInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FieldValidatorDecoratorInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "name": "FieldValidatorDecoratorInfo", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 70, "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 71, "name": "mode", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators.FieldValidatorModes"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 72, "name": "check_fields", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 73, "name": "json_schema_input_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic._internal._decorators", "mro": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic._internal._decorators.FieldValidatorDecoratorInfo.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "fields", "mode", "check_fields", "json_schema_input_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.FieldValidatorDecoratorInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "fields", "mode", "check_fields", "json_schema_input_type"], "arg_types": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators.FieldValidatorModes"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FieldValidatorDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic._internal._decorators.FieldValidatorDecoratorInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fields"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "check_fields"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json_schema_input_type"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["fields", "mode", "check_fields", "json_schema_input_type"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._decorators.FieldValidatorDecoratorInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["fields", "mode", "check_fields", "json_schema_input_type"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators.FieldValidatorModes"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FieldValidatorDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic._internal._decorators.FieldValidatorDecoratorInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["fields", "mode", "check_fields", "json_schema_input_type"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators.FieldValidatorModes"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FieldValidatorDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "check_fields": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.FieldValidatorDecoratorInfo.check_fields", "name": "check_fields", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "decorator_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.FieldValidatorDecoratorInfo.decorator_repr", "name": "decorator_repr", "type": "builtins.str"}}, "fields": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.FieldValidatorDecoratorInfo.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}}, "json_schema_input_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.FieldValidatorDecoratorInfo.json_schema_input_type", "name": "json_schema_input_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.FieldValidatorDecoratorInfo.mode", "name": "mode", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators.FieldValidatorModes"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.FieldValidatorDecoratorInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FieldValidatorModes": {".class": "SymbolTableNode", "cross_ref": "pydantic.functional_validators.FieldValidatorModes", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "GlobalsNamespace": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._namespace_utils.GlobalsNamespace", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MappingNamespace": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._namespace_utils.MappingNamespace", "kind": "Gdef"}, "ModelSerializerDecoratorInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "name": "ModelSerializerDecoratorInfo", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 127, "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 128, "name": "return_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 129, "name": "when_used", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic._internal._decorators", "mro": ["pydantic._internal._decorators.ModelSerializerDecoratorInfo", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic._internal._decorators.ModelSerializerDecoratorInfo.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mode", "return_type", "when_used"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.ModelSerializerDecoratorInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mode", "return_type", "when_used"], "arg_types": ["pydantic._internal._decorators.ModelSerializerDecoratorInfo", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ModelSerializerDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic._internal._decorators.ModelSerializerDecoratorInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "return_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "when_used"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["mode", "return_type", "when_used"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._decorators.ModelSerializerDecoratorInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["mode", "return_type", "when_used"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModelSerializerDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic._internal._decorators.ModelSerializerDecoratorInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["mode", "return_type", "when_used"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModelSerializerDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "decorator_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.ModelSerializerDecoratorInfo.decorator_repr", "name": "decorator_repr", "type": "builtins.str"}}, "mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.ModelSerializerDecoratorInfo.mode", "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}}}, "return_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.ModelSerializerDecoratorInfo.return_type", "name": "return_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "when_used": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.ModelSerializerDecoratorInfo.when_used", "name": "when_used", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ModelSerializerDecoratorInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModelValidatorDecoratorInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "name": "ModelValidatorDecoratorInfo", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 143, "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic._internal._decorators", "mro": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic._internal._decorators.ModelValidatorDecoratorInfo.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.ModelValidatorDecoratorInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "arg_types": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ModelValidatorDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic._internal._decorators.ModelValidatorDecoratorInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "mode"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["mode"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._decorators.ModelValidatorDecoratorInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["mode"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModelValidatorDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic._internal._decorators.ModelValidatorDecoratorInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["mode"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModelValidatorDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "decorator_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.ModelValidatorDecoratorInfo.decorator_repr", "name": "decorator_repr", "type": "builtins.str"}}, "mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.ModelValidatorDecoratorInfo.mode", "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ModelValidatorDecoratorInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Parameter": {".class": "SymbolTableNode", "cross_ref": "inspect.Parameter", "kind": "Gdef"}, "PydanticDescriptorProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy", "name": "PydanticDescriptorProxy", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 177, "name": "wrapped", "type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.DecoratedType"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 178, "name": "decorator_info", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators.DecoratorInfo"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 179, "name": "shim", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic._internal._decorators", "mro": ["pydantic._internal._decorators.PydanticDescriptorProxy", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "obj_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "obj_type"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.object"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of PydanticDescriptorProxy", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of PydanticDescriptorProxy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "wrapped", "decorator_info", "shim"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "wrapped", "decorator_info", "shim"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.DecoratedType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators.DecoratorInfo"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PydanticDescriptorProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapped"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "decorator_info"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shim"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of PydanticDescriptorProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["wrapped", "decorator_info", "shim"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["wrapped", "decorator_info", "shim"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.DecoratedType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators.DecoratorInfo"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of PydanticDescriptorProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["wrapped", "decorator_info", "shim"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.DecoratedType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators.DecoratorInfo"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of PydanticDescriptorProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.__post_init__", "name": "__post_init__", "type": null}}, "__set_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.__set_name__", "name": "__set_name__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__set_name__ of PydanticDescriptorProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_call_wrapped_attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["self", "func", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy._call_wrapped_attr", "name": "_call_wrapped_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["self", "func", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_call_wrapped_attr of PydanticDescriptorProxy", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "decorator_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.decorator_info", "name": "decorator_info", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators.DecoratorInfo"}}}, "shim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.shim", "name": "shim", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "wrapped": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.wrapped", "name": "wrapped", "type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.DecoratedType"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.PydanticDescriptorProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "id": 1, "name": "ReturnType", "namespace": "pydantic._internal._decorators.PydanticDescriptorProxy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ReturnType"], "typeddict_type": null}}, "PydanticUndefined": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefined", "kind": "Gdef"}, "PydanticUndefinedType": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefinedType", "kind": "Gdef"}, "PydanticUserError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUserError", "kind": "Gdef"}, "ReturnType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ReturnType", "name": "ReturnType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "RootValidatorDecoratorInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators.RootValidatorDecoratorInfo", "name": "RootValidatorDecoratorInfo", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._decorators.RootValidatorDecoratorInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 87, "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic._internal._decorators", "mro": ["pydantic._internal._decorators.RootValidatorDecoratorInfo", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic._internal._decorators.RootValidatorDecoratorInfo.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.RootValidatorDecoratorInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "arg_types": ["pydantic._internal._decorators.RootValidatorDecoratorInfo", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RootValidatorDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic._internal._decorators.RootValidatorDecoratorInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "mode"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["mode"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._decorators.RootValidatorDecoratorInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["mode"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of RootValidatorDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic._internal._decorators.RootValidatorDecoratorInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["mode"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of RootValidatorDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "decorator_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.RootValidatorDecoratorInfo.decorator_repr", "name": "decorator_repr", "type": "builtins.str"}}, "mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.RootValidatorDecoratorInfo.mode", "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.RootValidatorDecoratorInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators.RootValidatorDecoratorInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Signature": {".class": "SymbolTableNode", "cross_ref": "inspect.Signature", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "ValidatorDecoratorInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo", "name": "ValidatorDecoratorInfo", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 46, "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "each_item", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 49, "name": "always", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "check_fields", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic._internal._decorators", "mro": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "fields", "mode", "each_item", "always", "check_fields"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "fields", "mode", "each_item", "always", "check_fields"], "arg_types": ["pydantic._internal._decorators.ValidatorDecoratorInfo", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValidatorDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fields"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "each_item"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "check_fields"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["fields", "mode", "each_item", "always", "check_fields"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["fields", "mode", "each_item", "always", "check_fields"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ValidatorDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["fields", "mode", "each_item", "always", "check_fields"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ValidatorDecoratorInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "always": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo.always", "name": "always", "type": "builtins.bool"}}, "check_fields": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo.check_fields", "name": "check_fields", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "decorator_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo.decorator_repr", "name": "decorator_repr", "type": "builtins.str"}}, "each_item": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo.each_item", "name": "each_item", "type": "builtins.bool"}}, "fields": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}}, "mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo.mode", "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators.ValidatorDecoratorInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators.ValidatorDecoratorInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._decorators.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._decorators.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._decorators.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._decorators.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._decorators.__package__", "name": "__package__", "type": "builtins.str"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "_function_like": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._decorators._function_like", "name": "_function_like", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, "args", "kwargs"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "functools._T", "id": 1, "name": "_T", "namespace": "functools.partial", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "functools._T", "id": 1, "name": "_T", "namespace": "functools.partial", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "functools.partial"}], "def_extras": {"first_arg": null}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "partial", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "functools._T", "id": 1, "name": "_T", "namespace": "functools.partial", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "functools.partial"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "functools._T", "id": 1, "name": "_T", "namespace": "functools.partial", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, "args", "keywords"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "functools._T", "id": 1, "name": "_T", "namespace": "functools.partialmethod", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "functools._T", "id": 1, "name": "_T", "namespace": "functools.partialmethod", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "functools.partialmethod"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "partialmethod", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "functools._T", "id": 1, "name": "_T", "namespace": "functools.partialmethod", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "functools.partialmethod"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "functools._T", "id": 1, "name": "_T", "namespace": "functools.partialmethod", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, "args", "keywords"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "functools._Descriptor"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "functools._T", "id": 1, "name": "_T", "namespace": "functools.partialmethod", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "functools.partialmethod"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "partialmethod", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "functools._T", "id": 1, "name": "_T", "namespace": "functools.partialmethod", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "functools.partialmethod"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "functools._T", "id": 1, "name": "_T", "namespace": "functools.partialmethod", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["code", "globals", "name", "argdefs", "closure"], "arg_types": ["types.CodeType", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["types._Cell"], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}], "bound_args": ["types.FunctionType"], "def_extras": {"first_arg": null}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "FunctionType", "ret_type": "types.FunctionType", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["types.BuiltinFunctionType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "BuiltinFunctionType", "ret_type": "types.BuiltinFunctionType", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "builtins.object"], "bound_args": ["types.MethodType"], "def_extras": {"first_arg": null}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "MethodType", "ret_type": "types.MethodType", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["types.WrapperDescriptorType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WrapperDescriptorType", "ret_type": "types.WrapperDescriptorType", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["types.MethodWrapperType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "MethodWrapperType", "ret_type": "types.MethodWrapperType", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["types.MemberDescriptorType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "MemberDescriptorType", "ret_type": "types.MemberDescriptorType", "type_guard": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_is_classmethod_from_sig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["function"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators._is_classmethod_from_sig", "name": "_is_classmethod_from_sig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["function"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators.AnyDecoratorCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_classmethod_from_sig", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_sentinel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._decorators._sentinel", "name": "_sentinel", "type": "builtins.object"}}, "_serializer_info_arg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mode", "n_positional"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators._serializer_info_arg", "name": "_serializer_info_arg", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mode", "n_positional"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_serializer_info_arg", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef"}, "can_be_positional": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.can_be_positional", "kind": "Gdef"}, "core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef"}, "count_positional_required_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sig"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.count_positional_required_params", "name": "count_positional_required_params", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sig"], "arg_types": ["inspect.Signature"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count_positional_required_params", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "ensure_classmethod_based_on_signature": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["function"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.ensure_classmethod_based_on_signature", "name": "ensure_classmethod_based_on_signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["function"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators.AnyDecoratorCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_classmethod_based_on_signature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "ensure_property": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["f"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.ensure_property", "name": "ensure_property", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["f"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_property", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}, "get_attribute_from_base_dicts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tp", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.get_attribute_from_base_dicts", "name": "get_attribute_from_base_dicts", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tp", "name"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_attribute_from_base_dicts", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_attribute_from_bases": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tp", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.get_attribute_from_bases", "name": "get_attribute_from_bases", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tp", "name"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "type_ref": "builtins.tuple"}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_attribute_from_bases", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_bases": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tp"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.get_bases", "name": "get_bases", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tp"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bases", "ret_type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "type_ref": "builtins.tuple"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_callable_return_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["callable_obj", "globalns", "localns"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.get_callable_return_type", "name": "get_callable_return_type", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["callable_obj", "globalns", "localns"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_callable_return_type", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic_core._pydantic_core.PydanticUndefinedType"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_function_type_hints": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._typing_extra.get_function_type_hints", "kind": "Gdef"}, "get_type_ref": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.get_type_ref", "kind": "Gdef"}, "inspect_annotated_serializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["serializer", "mode"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.inspect_annotated_serializer", "name": "inspect_annotated_serializer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["serializer", "mode"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inspect_annotated_serializer", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "inspect_field_serializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["serializer", "mode"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.inspect_field_serializer", "name": "inspect_field_serializer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["serializer", "mode"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inspect_field_serializer", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "inspect_model_serializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["serializer", "mode"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.inspect_model_serializer", "name": "inspect_model_serializer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["serializer", "mode"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inspect_model_serializer", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "inspect_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["validator", "mode"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.inspect_validator", "name": "inspect_validator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["validator", "mode"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators.FieldValidatorModes"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inspect_validator", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_instance_method_from_sig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["function"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.is_instance_method_from_sig", "name": "is_instance_method_from_sig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["function"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators.AnyDecoratorCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_instance_method_from_sig", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_typeddict": {".class": "SymbolTableNode", "cross_ref": "typing.is_typeddict", "kind": "Gdef"}, "isdatadescriptor": {".class": "SymbolTableNode", "cross_ref": "inspect.isdatadescriptor", "kind": "Gdef"}, "islice": {".class": "SymbolTableNode", "cross_ref": "itertools.islice", "kind": "Gdef"}, "ismethoddescriptor": {".class": "SymbolTableNode", "cross_ref": "inspect.ismethoddescriptor", "kind": "Gdef"}, "mro": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tp"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.mro", "name": "mro", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tp"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mro", "ret_type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "type_ref": "builtins.tuple"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "mro_for_bases": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["bases"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.mro_for_bases", "name": "mro_for_bases", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["bases"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mro_for_bases", "ret_type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "type_ref": "builtins.tuple"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "partialmethod": {".class": "SymbolTableNode", "cross_ref": "functools.partialmethod", "kind": "Gdef"}, "signature": {".class": "SymbolTableNode", "cross_ref": "inspect.signature", "kind": "Gdef"}, "slots_true": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._internal_dataclass.slots_true", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "unwrap_wrapped_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["func", "unwrap_partial", "unwrap_class_static_method"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._decorators.unwrap_wrapped_function", "name": "unwrap_wrapped_function", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["func", "unwrap_partial", "unwrap_class_static_method"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unwrap_wrapped_function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_decorators.py"}