{".class": "MypyFile", "_fullname": "_pytest.pytester", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Collect@1132": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.Collect@1132", "name": "Collect", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.pytester.Collect@1132", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.Collect@1132", "builtins.object"], "names": {".class": "SymbolTable", "pytest_configure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x", "config"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Collect@1132.pytest_configure", "name": "pytest_configure", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x", "config"], "arg_types": ["_pytest.pytester.Collect@1132", "_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": "x"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_configure of Collect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.Collect@1132", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CollectReport": {".class": "SymbolTableNode", "cross_ref": "_pytest.reports.CollectReport", "kind": "Gdef"}, "Collector": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes.Collector", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.Config", "kind": "Gdef"}, "CwdSnapshot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.CwdSnapshot", "name": "CwdSnapshot", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.pytester.CwdSnapshot", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.CwdSnapshot", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.CwdSnapshot.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.CwdSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CwdSnapshot", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__saved": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.CwdSnapshot.__saved", "name": "__saved", "type": "builtins.str"}}, "restore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.CwdSnapshot.restore", "name": "restore", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.CwdSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore of CwdSnapshot", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pytester.CwdSnapshot.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.CwdSnapshot", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ExitCode": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.ExitCode", "kind": "Gdef"}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Final", "kind": "Gdef"}, "FixtureRequest": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures.FixtureRequest", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "HookRecorder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.HookRecorder", "name": "HookRecorder", "type_vars": []}, "deletable_attributes": [], "flags": ["is_final"], "fullname": "_pytest.pytester.HookRecorder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.HookRecorder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "pluginmanager", "_ispytest"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.HookRecorder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "pluginmanager", "_ispytest"], "arg_types": ["_pytest.pytester.HookRecorder", "_pytest.config.PytestPluginManager", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HookRecorder", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_pluginmanager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.HookRecorder._pluginmanager", "name": "_pluginmanager", "type": "_pytest.config.PytestPluginManager"}}, "_undo_wrapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.HookRecorder._undo_wrapping", "name": "_undo_wrapping", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "assert_contains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entries"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.HookRecorder.assert_contains", "name": "assert_contains", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "entries"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_contains of HookRecorder", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "assertoutcome": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "passed", "skipped", "failed"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.HookRecorder.assertoutcome", "name": "assertoutcome", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "passed", "skipped", "failed"], "arg_types": ["_pytest.pytester.HookRecorder", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assertoutcome of HookRecorder", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "calls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.pytester.HookRecorder.calls", "name": "calls", "type": {".class": "Instance", "args": ["_pytest.pytester.RecordedHookCall"], "type_ref": "builtins.list"}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.HookRecorder.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.HookRecorder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of HookRecorder", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "countoutcomes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.HookRecorder.countoutcomes", "name": "countoutcomes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.HookRecorder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "countoutcomes of HookRecorder", "ret_type": {".class": "Instance", "args": ["builtins.int"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "finish_recording": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.HookRecorder.finish_recording", "name": "finish_recording", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.HookRecorder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finish_recording of HookRecorder", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "getcall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.HookRecorder.getcall", "name": "getcall", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["_pytest.pytester.HookRecorder", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getcall of HookRecorder", "ret_type": "_pytest.pytester.RecordedHookCall", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "getcalls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.HookRecorder.getcalls", "name": "getcalls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getcalls of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.pytester.RecordedHookCall"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "getfailedcollections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.HookRecorder.getfailedcollections", "name": "getfailedcollections", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.HookRecorder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfailedcollections of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.reports.CollectReport"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "getfailures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "_pytest.pytester.HookRecorder.getfailures", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "_pytest.pytester.HookRecorder.getfailures", "name": "getfailures", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfailures of HookRecorder", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"]}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.pytester.HookRecorder.getfailures", "name": "getfailures", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "LiteralType", "fallback": "builtins.str", "value": "pytest_collectreport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfailures of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.reports.CollectReport"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.pytester.HookRecorder.getfailures", "name": "getfailures", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "LiteralType", "fallback": "builtins.str", "value": "pytest_collectreport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfailures of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.reports.CollectReport"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.pytester.HookRecorder.getfailures", "name": "getfailures", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "LiteralType", "fallback": "builtins.str", "value": "pytest_runtest_logreport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfailures of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.reports.TestReport"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.pytester.HookRecorder.getfailures", "name": "getfailures", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "LiteralType", "fallback": "builtins.str", "value": "pytest_runtest_logreport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfailures of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.reports.TestReport"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.pytester.HookRecorder.getfailures", "name": "getfailures", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfailures of HookRecorder", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"]}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.pytester.HookRecorder.getfailures", "name": "getfailures", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfailures of HookRecorder", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"]}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "LiteralType", "fallback": "builtins.str", "value": "pytest_collectreport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfailures of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.reports.CollectReport"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "LiteralType", "fallback": "builtins.str", "value": "pytest_runtest_logreport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfailures of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.reports.TestReport"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfailures of HookRecorder", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"]}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "getreports": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "_pytest.pytester.HookRecorder.getreports", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "_pytest.pytester.HookRecorder.getreports", "name": "getreports", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getreports of HookRecorder", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"]}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.pytester.HookRecorder.getreports", "name": "getreports", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "LiteralType", "fallback": "builtins.str", "value": "pytest_collectreport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getreports of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.reports.CollectReport"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.pytester.HookRecorder.getreports", "name": "getreports", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "LiteralType", "fallback": "builtins.str", "value": "pytest_collectreport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getreports of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.reports.CollectReport"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.pytester.HookRecorder.getreports", "name": "getreports", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "LiteralType", "fallback": "builtins.str", "value": "pytest_runtest_logreport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getreports of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.reports.TestReport"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.pytester.HookRecorder.getreports", "name": "getreports", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "LiteralType", "fallback": "builtins.str", "value": "pytest_runtest_logreport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getreports of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.reports.TestReport"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.pytester.HookRecorder.getreports", "name": "getreports", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getreports of HookRecorder", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"]}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.pytester.HookRecorder.getreports", "name": "getreports", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getreports of HookRecorder", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"]}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "LiteralType", "fallback": "builtins.str", "value": "pytest_collectreport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getreports of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.reports.CollectReport"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "LiteralType", "fallback": "builtins.str", "value": "pytest_runtest_logreport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getreports of HookRecorder", "ret_type": {".class": "Instance", "args": ["_pytest.reports.TestReport"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "names"], "arg_types": ["_pytest.pytester.HookRecorder", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getreports of HookRecorder", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"]}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "listoutcomes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.HookRecorder.listoutcomes", "name": "listoutcomes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.HookRecorder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "listoutcomes of HookRecorder", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["_pytest.reports.TestReport"], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"]}], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"]}], "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "matchreport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "inamepart", "names", "when"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.HookRecorder.matchreport", "name": "matchreport", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "inamepart", "names", "when"], "arg_types": ["_pytest.pytester.HookRecorder", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matchreport of HookRecorder", "ret_type": {".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "popcall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.HookRecorder.popcall", "name": "popcall", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["_pytest.pytester.HookRecorder", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "popcall of HookRecorder", "ret_type": "_pytest.pytester.RecordedHookCall", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "ret": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.pytester.HookRecorder.ret", "name": "ret", "type": {".class": "UnionType", "items": ["builtins.int", "_pytest.config.ExitCode", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pytester.HookRecorder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.HookRecorder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IGNORE_PAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.pytester.IGNORE_PAM", "name": "IGNORE_PAM", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "IniConfig": {".class": "SymbolTableNode", "cross_ref": "iniconfig.IniConfig", "kind": "Gdef"}, "Item": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes.Item", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LineComp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.LineComp", "name": "LineComp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.pytester.LineComp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.LineComp", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineComp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.LineComp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LineComp", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "assert_contains_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines2"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineComp.assert_contains_lines", "name": "assert_contains_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines2"], "arg_types": ["_pytest.pytester.LineComp", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_contains_lines of LineComp", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "stringio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.LineComp.stringio", "name": "stringio", "type": "io.StringIO"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pytester.LineComp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.LineComp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LineMatcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.LineMatcher", "name": "LineMatcher", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.pytester.LineMatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.LineMatcher", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": ["_pytest.pytester.LineMatcher", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LineMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["_pytest.pytester.LineMatcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of LineMatcher", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_fail": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher._fail", "name": "_fail", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "arg_types": ["_pytest.pytester.LineMatcher", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fail of LineMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_getlines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines2"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher._getlines", "name": "_getlines", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines2"], "arg_types": ["_pytest.pytester.LineMatcher", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, "_pytest._code.source.Source"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getlines of LineMatcher", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher._log", "name": "_log", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["_pytest.pytester.LineMatcher", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_log of LineMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_log_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.pytester.LineMatcher._log_output", "name": "_log_output", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "_log_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.pytester.LineMatcher._log_text", "name": "_log_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.LineMatcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_log_text of LineMatcher", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.pytester.LineMatcher._log_text", "name": "_log_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.LineMatcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_log_text of LineMatcher", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_match_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "lines2", "match_func", "match_nickname", "consecutive"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher._match_lines", "name": "_match_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "lines2", "match_func", "match_nickname", "consecutive"], "arg_types": ["_pytest.pytester.LineMatcher", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_match_lines of LineMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_match_lines_random": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lines2", "match_func"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher._match_lines_random", "name": "_match_lines_random", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "lines2", "match_func"], "arg_types": ["_pytest.pytester.LineMatcher", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_match_lines_random of LineMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_no_match_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "pat", "match_func", "match_nickname"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher._no_match_line", "name": "_no_match_line", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "pat", "match_func", "match_nickname"], "arg_types": ["_pytest.pytester.LineMatcher", "builtins.str", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_no_match_line of LineMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fnmatch_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "lines2", "consecutive"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher.fnmatch_lines", "name": "fnmatch_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "lines2", "consecutive"], "arg_types": ["_pytest.pytester.LineMatcher", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fnmatch_lines of LineMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fnmatch_lines_random": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines2"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher.fnmatch_lines_random", "name": "fnmatch_lines_random", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines2"], "arg_types": ["_pytest.pytester.LineMatcher", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fnmatch_lines_random of LineMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_lines_after": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fnline"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher.get_lines_after", "name": "get_lines_after", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fnline"], "arg_types": ["_pytest.pytester.LineMatcher", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_lines_after of LineMatcher", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.LineMatcher.lines", "name": "lines", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "no_fnmatch_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pat"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher.no_fnmatch_line", "name": "no_fnmatch_line", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pat"], "arg_types": ["_pytest.pytester.LineMatcher", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_fnmatch_line of LineMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "no_re_match_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pat"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher.no_re_match_line", "name": "no_re_match_line", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pat"], "arg_types": ["_pytest.pytester.LineMatcher", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_re_match_line of LineMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "re_match_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "lines2", "consecutive"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher.re_match_lines", "name": "re_match_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "lines2", "consecutive"], "arg_types": ["_pytest.pytester.LineMatcher", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "re_match_lines of LineMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "re_match_lines_random": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines2"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher.re_match_lines_random", "name": "re_match_lines_random", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines2"], "arg_types": ["_pytest.pytester.LineMatcher", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "re_match_lines_random of LineMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LineMatcher.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.LineMatcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of LineMatcher", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pytester.LineMatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.LineMatcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LineMatcher_fixture": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["request"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.pytester.LineMatcher_fixture", "name": "LineMatcher_fixture", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "LineMatcher_fixture", "ret_type": {".class": "TypeType", "item": "_pytest.pytester.LineMatcher"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.pytester.LineMatcher_fixture", "name": "LineMatcher_fixture", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "LineMatcher_fixture", "ret_type": {".class": "TypeType", "item": "_pytest.pytester.LineMatcher"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "LsofFdLeakChecker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.LsofFdLeakChecker", "name": "LsofFdLeakChecker", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.pytester.LsofFdLeakChecker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.LsofFdLeakChecker", "builtins.object"], "names": {".class": "SymbolTable", "get_open_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LsofFdLeakChecker.get_open_files", "name": "get_open_files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.LsofFdLeakChecker"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_open_files of LsofFdLeakChecker", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "matching_platform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.LsofFdLeakChecker.matching_platform", "name": "matching_platform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.LsofFdLeakChecker"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matching_platform of LsofFdLeakChecker", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pytest_runtest_protocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.pytester.LsofFdLeakChecker.pytest_runtest_protocol", "name": "pytest_runtest_protocol", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["_pytest.pytester.LsofFdLeakChecker", "_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_protocol of LsofFdLeakChecker", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.pytester.LsofFdLeakChecker.pytest_runtest_protocol", "name": "pytest_runtest_protocol", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["_pytest.pytester.LsofFdLeakChecker", "_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_runtest_protocol of LsofFdLeakChecker", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pytester.LsofFdLeakChecker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.LsofFdLeakChecker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MonkeyPatch": {".class": "SymbolTableNode", "cross_ref": "_pytest.monkeypatch.MonkeyPatch", "kind": "Gdef"}, "NOTSET": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.NOTSET", "kind": "Gdef"}, "NotSetType": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.NotSetType", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Parser": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.argparsing.Parser", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PytestArg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.PytestArg", "name": "PytestArg", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.pytester.PytestArg", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.PytestArg", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.PytestArg.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["_pytest.pytester.PytestArg", "_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PytestArg", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.PytestArg._request", "name": "_request", "type": "_pytest.fixtures.FixtureRequest"}}, "gethookrecorder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hook"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.PytestArg.gethookrecorder", "name": "gethookrecorder", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hook"], "arg_types": ["_pytest.pytester.PytestArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gethookrecorder of PytestArg", "ret_type": "_pytest.pytester.HookRecorder", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pytester.PytestArg.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.PytestArg", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestPluginManager": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.PytestPluginManager", "kind": "Gdef"}, "PytestWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestWarning", "kind": "Gdef"}, "Pytester": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.Pytester", "name": "Pytester", "type_vars": []}, "deletable_attributes": [], "flags": ["is_final"], "fullname": "_pytest.pytester.Pytester", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.Pytester", "builtins.object"], "names": {".class": "SymbolTable", "CLOSE_STDIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "_pytest.pytester.Pytester.CLOSE_STDIN", "name": "CLOSE_STDIN", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "_pytest.compat.NotSetType", "value": "token"}, "type_ref": "_pytest.compat.NotSetType"}}}, "TimeoutExpired": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.Pytester.TimeoutExpired", "name": "TimeoutExpired", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.pytester.Pytester.TimeoutExpired", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.Pytester.TimeoutExpired", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pytester.Pytester.TimeoutExpired.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.Pytester.TimeoutExpired", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "request", "tmp_path_factory", "monkeypatch", "_ispytest"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "request", "tmp_path_factory", "monkeypatch", "_ispytest"], "arg_types": ["_pytest.pytester.Pytester", "_pytest.fixtures.FixtureRequest", "_pytest.tmpdir.TempPathFactory", "_pytest.monkeypatch.MonkeyPatch", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Pytester", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["_pytest.pytester.Pytester"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Pytester", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__take_sys_modules_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.__take_sys_modules_snapshot", "name": "__take_sys_modules_snapshot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.Pytester"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__take_sys_modules_snapshot of Pytester", "ret_type": "_pytest.pytester.SysModulesSnapshot", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__test__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_pytest.pytester.Pytester.__test__", "name": "__test__", "type": "builtins.bool"}}, "_cwd_snapshot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.Pytester._cwd_snapshot", "name": "_cwd_snapshot", "type": "_pytest.pytester.CwdSnapshot"}}, "_dump_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lines", "fp"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester._dump_lines", "name": "_dump_lines", "type": null}}, "_ensure_basetemp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester._ensure_basetemp", "name": "_ensure_basetemp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "args"], "arg_types": ["_pytest.pytester.Pytester", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ensure_basetemp of Pytester", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester._finalize", "name": "_finalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.Pytester"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_finalize of Pytester", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_getpytestargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester._getpytestargs", "name": "_getpytestargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.Pytester"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getpytestargs of Pytester", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_makefile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "ext", "lines", "files", "encoding"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester._makefile", "name": "_makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "ext", "lines", "files", "encoding"], "arg_types": ["_pytest.pytester.Pytester", "builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bytes"]}], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_makefile of Pytester", "ret_type": "pathlib.Path", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.Pytester._method", "name": "_method", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_mod_collections": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.pytester.Pytester._mod_collections", "name": "_mod_collections", "type": {".class": "Instance", "args": ["_pytest.nodes.Collector", {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector"]}], "type_ref": "builtins.list"}], "type_ref": "weakref.<PERSON>eyDictionary"}}}, "_monkeypatch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.Pytester._monkeypatch", "name": "_monkeypatch", "type": "_pytest.monkeypatch.MonkeyPatch"}}, "_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.Pytester._name", "name": "_name", "type": "builtins.str"}}, "_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.pytester.Pytester._path", "name": "_path", "type": "pathlib.Path"}}, "_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.Pytester._request", "name": "_request", "type": "_pytest.fixtures.FixtureRequest"}}, "_sys_modules_snapshot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.Pytester._sys_modules_snapshot", "name": "_sys_modules_snapshot", "type": "_pytest.pytester.SysModulesSnapshot"}}, "_sys_path_snapshot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.Pytester._sys_path_snapshot", "name": "_sys_path_snapshot", "type": "_pytest.pytester.SysPathsSnapshot"}}, "_test_tmproot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.Pytester._test_tmproot", "name": "_test_tmproot", "type": "pathlib.Path"}}, "chdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.chdir", "name": "chdir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.Pytester"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chdir of Pytester", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "collect_by_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "modcol", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.collect_by_name", "name": "collect_by_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "modcol", "name"], "arg_types": ["_pytest.pytester.Pytester", "_pytest.nodes.Collector", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collect_by_name of <PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.Pytester.config", "name": "config", "type": "_pytest.config.Config"}}, "copy_example": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.copy_example", "name": "copy_example", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "name"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_example of Pytester", "ret_type": "pathlib.Path", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "genitems": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "colitems"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.genitems", "name": "genitems", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "colitems"], "arg_types": ["_pytest.pytester.Pytester", {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector"]}], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "genitems of Pytester", "ret_type": {".class": "Instance", "args": ["_pytest.nodes.Item"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "getinicfg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.getinicfg", "name": "getinicfg", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "source"], "arg_types": ["_pytest.pytester.Pytester", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getinicfg of Pytester", "ret_type": "iniconfig.SectionWrapper", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "getitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "source", "funcname"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.getitem", "name": "getitem", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "source", "funcname"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getitem of Pytester", "ret_type": "_pytest.nodes.Item", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "getitems": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.getitems", "name": "getitems", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "source"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getitems of Pytester", "ret_type": {".class": "Instance", "args": ["_pytest.nodes.Item"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "getmodulecol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "source", "configargs", "withinit"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.getmodulecol", "name": "getmodulecol", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "source", "configargs", "withinit"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getmodulecol of Pytester", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "getnode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "arg"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.getnode", "name": "getnode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "arg"], "arg_types": ["_pytest.pytester.Pytester", "_pytest.config.Config", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getnode of Pytester", "ret_type": {".class": "UnionType", "items": ["_pytest.nodes.Collector", "_pytest.nodes.Item"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "getpathnode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.getpathnode", "name": "getpathnode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpathnode of Pytester", "ret_type": {".class": "UnionType", "items": ["_pytest.nodes.Collector", "_pytest.nodes.Item"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "inline_genitems": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.inline_genitems", "name": "inline_genitems", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["_pytest.pytester.Pytester", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inline_genitems of Pytester", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["_pytest.nodes.Item"], "type_ref": "builtins.list"}, "_pytest.pytester.HookRecorder"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "inline_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5], "arg_names": ["self", "args", "plugins", "no_reraise_ctrlc"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.inline_run", "name": "inline_run", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["self", "args", "plugins", "no_reraise_ctrlc"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inline_run of Pytester", "ret_type": "_pytest.pytester.HookRecorder", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "inline_runsource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "source", "cmdlineargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.inline_runsource", "name": "inline_runsource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "source", "cmdlineargs"], "arg_types": ["_pytest.pytester.Pytester", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inline_runsource of Pytester", "ret_type": "_pytest.pytester.HookRecorder", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "make_hook_recorder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pluginmanager"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.make_hook_recorder", "name": "make_hook_recorder", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pluginmanager"], "arg_types": ["_pytest.pytester.Pytester", "_pytest.config.PytestPluginManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_hook_recorder of Pytester", "ret_type": "_pytest.pytester.HookRecorder", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "makeconftest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.makeconftest", "name": "makeconftest", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "source"], "arg_types": ["_pytest.pytester.Pytester", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makeconftest of Pytester", "ret_type": "pathlib.Path", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "makefile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "ext", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "ext", "args", "kwargs"], "arg_types": ["_pytest.pytester.Pytester", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of Pytester", "ret_type": "pathlib.Path", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "makeini": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.makeini", "name": "makeini", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "source"], "arg_types": ["_pytest.pytester.Pytester", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makeini of Pytester", "ret_type": "pathlib.Path", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "makepyfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.makepyfile", "name": "makepyfile", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["_pytest.pytester.Pytester", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makepyfile of Pytester", "ret_type": "pathlib.Path", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "makepyprojecttoml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.makepyprojecttoml", "name": "makepyprojecttoml", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "source"], "arg_types": ["_pytest.pytester.Pytester", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makepyprojecttoml of Pytester", "ret_type": "pathlib.Path", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "maketxtfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.maketxtfile", "name": "maketxtfile", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["_pytest.pytester.Pytester", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maketxtfile of Pytester", "ret_type": "pathlib.Path", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "mkdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.mkdir", "name": "mkdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mkdir of Pytester", "ret_type": "pathlib.Path", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "mkpydir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.mkpydir", "name": "mkpydir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mkpydir of Pytester", "ret_type": "pathlib.Path", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "parseconfig": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.parseconfig", "name": "parseconfig", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseconfig of Pytester", "ret_type": "_pytest.config.Config", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "parseconfigure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.parseconfigure", "name": "parseconfigure", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseconfigure of Pytester", "ret_type": "_pytest.config.Config", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.pytester.Pytester.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.Pytester"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of <PERSON><PERSON><PERSON>", "ret_type": "pathlib.Path", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.pytester.Pytester.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.Pytester"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of <PERSON><PERSON><PERSON>", "ret_type": "pathlib.Path", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "plugins": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.pytester.Pytester.plugins", "name": "plugins", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.object"]}], "type_ref": "builtins.list"}}}, "popen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "cmdargs", "stdout", "stderr", "stdin", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.popen", "name": "popen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "cmdargs", "stdout", "stderr", "stdin", "kw"], "arg_types": ["_pytest.pytester.Pytester", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}], "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.int", "typing.TextIO"]}, {".class": "UnionType", "items": ["builtins.int", "typing.TextIO"]}, {".class": "UnionType", "items": ["_pytest.compat.NotSetType", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, "builtins.int"]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pope<PERSON> of Pytester", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5], "arg_names": ["self", "cmdargs", "timeout", "stdin"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["self", "cmdargs", "timeout", "stdin"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["_pytest.compat.NotSetType", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, "builtins.int"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of <PERSON><PERSON><PERSON>", "ret_type": "_pytest.pytester.RunResult", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "runitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.runitem", "name": "runitem", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "source"], "arg_types": ["_pytest.pytester.Pytester", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "runitem of Pytester", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "runpytest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.runpytest", "name": "runpytest", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "runpytest of Pytester", "ret_type": "_pytest.pytester.RunResult", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "runpytest_inprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.runpytest_inprocess", "name": "runpytest_inprocess", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "runpytest_inprocess of Pytester", "ret_type": "_pytest.pytester.RunResult", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "runpytest_subprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5], "arg_names": ["self", "args", "timeout"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.runpytest_subprocess", "name": "runpytest_subprocess", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5], "arg_names": ["self", "args", "timeout"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}]}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "runpytest_subprocess of Pytester", "ret_type": "_pytest.pytester.RunResult", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "runpython": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "script"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.runpython", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "script"], "arg_types": ["_pytest.pytester.Pytester", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "runpython of Pytester", "ret_type": "_pytest.pytester.RunResult", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "runpython_c": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "command"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.runpython_c", "name": "runpython_c", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "command"], "arg_types": ["_pytest.pytester.Pytester", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "runpython_c of Pytester", "ret_type": "_pytest.pytester.RunResult", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "spawn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "cmd", "expect_timeout"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.spawn", "name": "spawn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "cmd", "expect_timeout"], "arg_types": ["_pytest.pytester.Pytester", "builtins.str", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "spawn of Pytester", "ret_type": {".class": "AnyType", "missing_import_name": "_pytest.pytester.pexpect", "source_any": null, "type_of_any": 3}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "spawn_pytest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "expect_timeout"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.spawn_pytest", "name": "spawn_pytest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "expect_timeout"], "arg_types": ["_pytest.pytester.Pytester", "builtins.str", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "spawn_pytest of Pytester", "ret_type": {".class": "AnyType", "missing_import_name": "_pytest.pytester.pexpect", "source_any": null, "type_of_any": 3}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "syspathinsert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.Pytester.syspathinsert", "name": "syspath<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "path"], "arg_types": ["_pytest.pytester.Pytester", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "syspath<PERSON><PERSON> of Pytester", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pytester.Pytester.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.Pytester", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RecordedHookCall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.RecordedHookCall", "name": "RecordedHookCall", "type_vars": []}, "deletable_attributes": [], "flags": ["is_final"], "fullname": "_pytest.pytester.RecordedHookCall", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.RecordedHookCall", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "_pytest.pytester.RecordedHookCall.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_pytest.pytester.RecordedHookCall", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of RecordedHookCall", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.RecordedHookCall.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "kwargs"], "arg_types": ["_pytest.pytester.RecordedHookCall", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RecordedHookCall", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.RecordedHookCall.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["_pytest.pytester.RecordedHookCall"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of RecordedHookCall", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.RecordedHookCall._name", "name": "_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pytester.RecordedHookCall.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.RecordedHookCall", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RunResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.RunResult", "name": "RunResult", "type_vars": []}, "deletable_attributes": [], "flags": ["is_final"], "fullname": "_pytest.pytester.RunResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.RunResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "ret", "outlines", "errlines", "duration"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.RunResult.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "ret", "outlines", "errlines", "duration"], "arg_types": ["_pytest.pytester.RunResult", {".class": "UnionType", "items": ["builtins.int", "_pytest.config.ExitCode"]}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RunResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.RunResult.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["_pytest.pytester.RunResult"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of RunResult", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "assert_outcomes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "passed", "skipped", "failed", "errors", "xpassed", "xfailed", "warnings", "deselected"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.RunResult.assert_outcomes", "name": "assert_outcomes", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "passed", "skipped", "failed", "errors", "xpassed", "xfailed", "warnings", "deselected"], "arg_types": ["_pytest.pytester.RunResult", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_outcomes of RunResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "duration": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.RunResult.duration", "name": "duration", "type": "builtins.float"}}, "errlines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.RunResult.errlines", "name": "errlines", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "outlines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.RunResult.outlines", "name": "outlines", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "parse_summary_nouns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "lines"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "_pytest.pytester.RunResult.parse_summary_nouns", "name": "parse_summary_nouns", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "lines"], "arg_types": [{".class": "TypeType", "item": "_pytest.pytester.RunResult"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_summary_nouns of RunResult", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.pytester.RunResult.parse_summary_nouns", "name": "parse_summary_nouns", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "lines"], "arg_types": [{".class": "TypeType", "item": "_pytest.pytester.RunResult"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_summary_nouns of RunResult", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "parseoutcomes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.RunResult.parseoutcomes", "name": "parseoutcomes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.RunResult"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseoutcomes of RunResult", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "ret": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.pytester.RunResult.ret", "name": "ret", "type": {".class": "UnionType", "items": ["builtins.int", "_pytest.config.ExitCode"]}}}, "stderr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.RunResult.stderr", "name": "stderr", "type": "_pytest.pytester.LineMatcher"}}, "stdout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.RunResult.stdout", "name": "stdout", "type": "_pytest.pytester.LineMatcher"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pytester.RunResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.RunResult", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SectionWrapper": {".class": "SymbolTableNode", "cross_ref": "iniconfig.SectionWrapper", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "_pytest.main.Session", "kind": "Gdef"}, "Source": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.source.Source", "kind": "Gdef"}, "StringIO": {".class": "SymbolTableNode", "cross_ref": "io.StringIO", "kind": "Gdef"}, "SysModulesSnapshot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.SysModulesSnapshot", "name": "SysModulesSnapshot", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.pytester.SysModulesSnapshot", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.SysModulesSnapshot", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "preserve"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.SysModulesSnapshot.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "preserve"], "arg_types": ["_pytest.pytester.SysModulesSnapshot", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SysModulesSnapshot", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__preserve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.SysModulesSnapshot.__preserve", "name": "__preserve", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "__saved": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.SysModulesSnapshot.__saved", "name": "__saved", "type": {".class": "Instance", "args": ["builtins.str", "types.ModuleType"], "type_ref": "builtins.dict"}}}, "restore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.SysModulesSnapshot.restore", "name": "restore", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.SysModulesSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore of SysModulesSnapshot", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pytester.SysModulesSnapshot.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.SysModulesSnapshot", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SysPathsSnapshot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.SysPathsSnapshot", "name": "SysPathsSnapshot", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.pytester.SysPathsSnapshot", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.SysPathsSnapshot", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.SysPathsSnapshot.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.SysPathsSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SysPathsSnapshot", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__saved": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.pytester.SysPathsSnapshot.__saved", "name": "__saved", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["sys._MetaPathFinder"], "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "restore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.SysPathsSnapshot.restore", "name": "restore", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.pytester.SysPathsSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore of SysPathsSnapshot", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pytester.SysPathsSnapshot.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.SysPathsSnapshot", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TempPathFactory": {".class": "SymbolTableNode", "cross_ref": "_pytest.tmpdir.TempPathFactory", "kind": "Gdef"}, "TestReport": {".class": "SymbolTableNode", "cross_ref": "_pytest.reports.TestReport", "kind": "Gdef"}, "TextIO": {".class": "SymbolTableNode", "cross_ref": "typing.TextIO", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WeakKeyDictionary": {".class": "SymbolTableNode", "cross_ref": "weakref.<PERSON>eyDictionary", "kind": "Gdef"}, "_PluggyPlugin": {".class": "SymbolTableNode", "cross_ref": "_pytest.config._PluggyPlugin", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.pytester.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.pytester.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.pytester.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.pytester.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.pytester.__package__", "name": "__package__", "type": "builtins.str"}}, "_config_for_test": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.pytester._config_for_test", "name": "_config_for_test", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_config_for_test", "ret_type": {".class": "Instance", "args": ["_pytest.config.Config", {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.pytester._config_for_test", "name": "_config_for_test", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_config_for_test", "ret_type": {".class": "Instance", "args": ["_pytest.config.Config", {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_get_multicapture": {".class": "SymbolTableNode", "cross_ref": "_pytest.capture._get_multicapture", "kind": "Gdef"}, "_pytest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["request"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.pytester._pytest", "name": "_pytest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pytest", "ret_type": "_pytest.pytester.PytestArg", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.pytester._pytest", "name": "_pytest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pytest", "ret_type": "_pytest.pytester.PytestArg", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_sys_snapshot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.pytester._sys_snapshot", "name": "_sys_snapshot", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sys_snapshot", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.pytester._sys_snapshot", "name": "_sys_snapshot", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sys_snapshot", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "bestrelpath": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.bestrelpath", "kind": "Gdef"}, "check_ispytest": {".class": "SymbolTableNode", "cross_ref": "_pytest.deprecated.check_ispytest", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "copytree": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.copytree", "kind": "Gdef"}, "fail": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.fail", "kind": "Gdef"}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef"}, "fixture": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures.fixture", "kind": "Gdef"}, "fnmatch": {".class": "SymbolTableNode", "cross_ref": "fnmatch.fnmatch", "kind": "Gdef"}, "gc": {".class": "SymbolTableNode", "cross_ref": "gc", "kind": "Gdef"}, "get_public_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["values"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.get_public_names", "name": "get_public_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["values"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_public_names", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "hookimpl": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.hookimpl", "kind": "Gdef"}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "importorskip": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.importorskip", "kind": "Gdef"}, "linecomp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.pytester.linecomp", "name": "linecomp", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linecomp", "ret_type": "_pytest.pytester.LineComp", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.pytester.linecomp", "name": "linecomp", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linecomp", "ret_type": "_pytest.pytester.LineComp", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "locale": {".class": "SymbolTableNode", "cross_ref": "locale", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.main", "kind": "Gdef"}, "make_numbered_dir": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.make_numbered_dir", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "pexpect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "_pytest.pytester.pexpect", "name": "pexpect", "type": {".class": "AnyType", "missing_import_name": "_pytest.pytester.pexpect", "source_any": null, "type_of_any": 3}}}, "platform": {".class": "SymbolTableNode", "cross_ref": "platform", "kind": "Gdef"}, "pytest_addoption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["parser"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.pytest_addoption", "name": "pytest_addoption", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["parser"], "arg_types": ["_pytest.config.argparsing.Parser"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_addoption", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pytest_configure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "flags": [], "fullname": "_pytest.pytester.pytest_configure", "name": "pytest_configure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_configure", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pytest_plugins": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.pytester.pytest_plugins", "name": "pytest_plugins", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "pytester": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["request", "tmp_path_factory", "monkeypatch"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "_pytest.pytester.pytester", "name": "pytester", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["request", "tmp_path_factory", "monkeypatch"], "arg_types": ["_pytest.fixtures.FixtureRequest", "_pytest.tmpdir.TempPathFactory", "_pytest.monkeypatch.MonkeyPatch"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytester", "ret_type": "_pytest.pytester.Pytester", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.pytester.pytester", "name": "pytester", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["request", "tmp_path_factory", "monkeypatch"], "arg_types": ["_pytest.fixtures.FixtureRequest", "_pytest.tmpdir.TempPathFactory", "_pytest.monkeypatch.MonkeyPatch"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytester", "ret_type": "_pytest.pytester.Pytester", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "reprec@1142": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.reprec@1142", "name": "reprec", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.pytester.reprec@1142", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.reprec@1142", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.reprec@1142", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "reprec@1180": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.reprec@1180", "name": "reprec", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.pytester.reprec@1180", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.reprec@1180", "builtins.object"], "names": {".class": "SymbolTable", "ret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "<EMAIL>", "name": "ret", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.reprec@1180", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "reprec@1186": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pytester.reprec@1186", "name": "reprec", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_pytest.pytester.reprec@1186", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pytester", "mro": ["_pytest.pytester.reprec@1186", "builtins.object"], "names": {".class": "SymbolTable", "ret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "<EMAIL>", "name": "ret", "type": "_pytest.config.ExitCode"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pytester.reprec@1186", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "rex_outcome": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.pytester.rex_outcome", "name": "rex_outcome", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "rex_session_duration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.pytester.rex_session_duration", "name": "rex_session_duration", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "skip": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.skip", "kind": "Gdef"}, "subprocess": {".class": "SymbolTableNode", "cross_ref": "subprocess", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "timing": {".class": "SymbolTableNode", "cross_ref": "_pytest.timing", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\_pytest\\pytester.py"}