#!/bin/bash
# Bash script to send all 6 test events to LocalStack SQS

set -e

# Default configuration
PROFILE="${AWS_PROFILE:-}"
QUEUE_NAME="agent-events-queue"
LOCALSTACK_ENDPOINT="http://localhost:4566"
REGION="us-east-1"

# Function to show help
show_help() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --profile <profile>     AWS profile to use (default: \$AWS_PROFILE)"
    echo "  --queue-name <name>     SQS queue name (default: agent-events-queue)"
    echo "  --endpoint <url>        LocalStack endpoint (default: http://localhost:4566)"
    echo "  --region <region>       AWS region (default: us-east-1)"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Example:"
    echo "  AWS_PROFILE='admin-memo' $0"
    echo "  $0 --profile admin-memo"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --profile)
            PROFILE="$2"
            shift 2
            ;;
        --queue-name)
            QUEUE_NAME="$2"
            shift 2
            ;;
        --endpoint)
            LOCALSTACK_ENDPOINT="$2"
            shift 2
            ;;
        --region)
            REGION="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

echo "Sending test events to LocalStack SQS..."
echo "Profile: $PROFILE"
echo "Queue: $QUEUE_NAME"
echo "Endpoint: $LOCALSTACK_ENDPOINT"

# Set AWS environment variables
if [ -n "$PROFILE" ]; then
    export AWS_PROFILE="$PROFILE"
    echo "Using AWS Profile: $PROFILE"
else
    # Use test credentials for LocalStack
    export AWS_ACCESS_KEY_ID="test"
    export AWS_SECRET_ACCESS_KEY="test"
    echo "Using test credentials for LocalStack"
fi

export AWS_DEFAULT_REGION="$REGION"

# Function to create XML event
create_event_xml() {
    local event_type="$1"
    local agent_name="$2"
    local additional_data="$3"
    
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
    
    cat << EOF
<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>$timestamp</timestamp>
    <eventType>$event_type</eventType>
    <agencyOrElement>TestAgency</agencyOrElement>
    <agent>$agent_name</agent>
    <reason>normal</reason>
    <workstation>TEST_WS_$event_type</workstation>
    <operatorId>TEST_OP_$agent_name</operatorId>
    <agentRole>Test Agent</agentRole>
    <agentUri>tel:+1234567890</agentUri>
    <mediaLabel>TEST_MEDIA_$event_type</mediaLabel>
    <tenantGroup>TestAgency</tenantGroup>
    <deviceName>TestDevice_$event_type</deviceName>$additional_data
</LogEvent>
EOF
}

# Function to send message to SQS
send_sqs_message() {
    local message_body="$1"
    local event_type="$2"
    local agent_name="$3"
    
    echo "Sending $event_type event for agent $agent_name..."
    
    # Get queue URL
    local queue_url
    queue_url=$(aws --endpoint-url="$LOCALSTACK_ENDPOINT" sqs get-queue-url --queue-name "$QUEUE_NAME" --region "$REGION" --query 'QueueUrl' --output text)
    
    if [ $? -ne 0 ]; then
        echo "  ✗ Failed to get queue URL"
        return 1
    fi
    
    # Send message
    local message_id
    message_id=$(aws --endpoint-url="$LOCALSTACK_ENDPOINT" sqs send-message --queue-url "$queue_url" --message-body "$message_body" --region "$REGION" --query 'MessageId' --output text)
    
    if [ $? -ne 0 ]; then
        echo "  ✗ Failed to send message"
        return 1
    fi
    
    echo "  ✓ Sent $event_type event (MessageId: $message_id)"
    return 0
}

# Define test events
declare -a test_events=(
    "Login|test_agent_login|"
    "Logout|test_agent_logout|"
    "ACDLogin|test_agent_acd_login|
    <acdId>TEST_ACD_001</acdId>
    <ringGroupName>TEST_RING_GROUP</ringGroupName>"
    "ACDLogout|test_agent_acd_logout|
    <acdId>TEST_ACD_002</acdId>
    <ringGroupName>TEST_RING_GROUP_2</ringGroupName>"
    "AgentBusiedOut|test_agent_busied|
    <busiedOutAction>Manual</busiedOutAction>
    <busiedOutDuration>300</busiedOutDuration>"
    "AgentAvailable|test_agent_available|
    <busiedOutAction>Manual</busiedOutAction>"
)

# Send all events
success_count=0
total_count=${#test_events[@]}

echo ""
echo "Sending $total_count test events..."

for event_data in "${test_events[@]}"; do
    IFS='|' read -r event_type agent_name additional_data <<< "$event_data"
    
    xml=$(create_event_xml "$event_type" "$agent_name" "$additional_data")
    
    if send_sqs_message "$xml" "$event_type" "$agent_name"; then
        ((success_count++))
    fi
    
    # Small delay between messages
    sleep 0.5
done

# Summary
echo ""
echo "============================================================"
echo "Test Events Summary"
echo "============================================================"
echo "Total events: $total_count"
echo "Successful: $success_count"
echo "Failed: $((total_count - success_count))"

if [ $success_count -eq $total_count ]; then
    echo ""
    echo "All events sent successfully! 🎉"
    echo "The Lambda function should now process these events."
    echo "Check the Lambda logs for processing results."
else
    echo ""
    echo "Some events failed to send. Check the errors above."
    exit 1
fi

echo ""
echo "To check Lambda logs:"
echo "  docker logs agent-event-processor-lambda"
echo ""
echo "To check LocalStack logs:"
echo "  docker logs agent-event-processor-localstack"
