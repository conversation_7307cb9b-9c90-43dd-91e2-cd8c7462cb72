"""
Unit tests for database service components.

This module tests the database service, dimension manager,
and fact manager with proper mocking and validation.
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from typing import Dict, Any

from src.agent_event_processor.services.database_service import (
    DatabaseService,
    DimensionManager,
    FactManager,
    RedshiftDataAPIConnection,
    RedshiftDataAPICursor,
)
from src.agent_event_processor.config.settings import DatabaseSettings


class TestRedshiftDataAPIConnection:
    """Test suite for RedshiftDataAPIConnection."""

    def test_init(self):
        """Test connection initialization."""
        with patch("boto3.client") as mock_boto3:
            conn = RedshiftDataAPIConnection(
                cluster_identifier="test-cluster",
                database="test-db",
                user="test-user",
                region="us-east-1",
            )

            assert conn.cluster_identifier == "test-cluster"
            assert conn.database == "test-db"
            assert conn.user == "test-user"
            assert conn.region == "us-east-1"
            assert conn.query_group == "agent-event-processor"
            assert conn.timeout == 300

            mock_boto3.assert_called_once()

    def test_cursor_creation(self):
        """Test cursor creation."""
        with patch("boto3.client"):
            conn = RedshiftDataAPIConnection(
                cluster_identifier="test-cluster",
                database="test-db",
                user="test-user",
                region="us-east-1",
            )

            cursor = conn.cursor()
            assert isinstance(cursor, RedshiftDataAPICursor)
            assert cursor.connection == conn


class TestRedshiftDataAPICursor:
    """Test suite for RedshiftDataAPICursor."""

    @pytest.fixture
    def mock_connection(self):
        """Create mock connection."""
        conn = Mock(spec=RedshiftDataAPIConnection)
        conn.cluster_identifier = "test-cluster"
        conn.database = "test-db"
        conn.user = "test-user"
        conn.timeout = 300
        conn.client = Mock()
        return conn

    @pytest.fixture
    def cursor(self, mock_connection):
        """Create cursor with mock connection."""
        return RedshiftDataAPICursor(mock_connection)

    def test_prepare_sql_with_params_no_params(self, cursor):
        """Test SQL preparation without parameters."""
        sql = "SELECT * FROM test_table"
        prepared_sql, params = cursor._prepare_sql_with_params(sql)

        assert prepared_sql == sql
        assert params == []

    def test_prepare_sql_with_params_with_params(self, cursor):
        """Test SQL preparation with parameters."""
        sql = "SELECT * FROM test_table WHERE id = %s AND name = %s"
        parameters = [123, "test_name"]

        prepared_sql, params = cursor._prepare_sql_with_params(sql, parameters)

        assert (
            prepared_sql
            == "SELECT * FROM test_table WHERE id = :param1 AND name = :param2"
        )
        assert len(params) == 2
        assert params[0] == {"name": "param1", "value": "123"}
        assert params[1] == {"name": "param2", "value": "test_name"}

    def test_prepare_sql_with_none_params(self, cursor):
        """Test SQL preparation with None parameters."""
        sql = "SELECT * FROM test_table WHERE id = %s"
        parameters = [None]

        prepared_sql, params = cursor._prepare_sql_with_params(sql, parameters)

        assert prepared_sql == "SELECT * FROM test_table WHERE id = NULL"
        assert params == []

    def test_convert_parameter_value_types(self, cursor):
        """Test parameter value conversion for different types."""
        # Test None
        assert cursor._convert_parameter_value(None) is None

        # Test boolean
        assert cursor._convert_parameter_value(True) == "true"
        assert cursor._convert_parameter_value(False) == "false"

        # Test numbers
        assert cursor._convert_parameter_value(123) == "123"
        assert cursor._convert_parameter_value(45.67) == "45.67"

        # Test datetime
        dt = datetime(2024, 1, 15, 10, 30, 0)
        assert cursor._convert_parameter_value(dt) == "2024-01-15T10:30:00"

        # Test string
        assert cursor._convert_parameter_value("test") == "test"
        assert cursor._convert_parameter_value("") is None

    def test_build_request_params(self, cursor):
        """Test request parameter building."""
        sql = "SELECT * FROM test"
        redshift_params = [{"name": "param1", "value": "123"}]

        params = cursor._build_request_params(sql, redshift_params)

        assert params["ClusterIdentifier"] == "test-cluster"
        assert params["Database"] == "test-db"
        assert params["DbUser"] == "test-user"
        assert params["Sql"] == sql
        assert "StatementName" in params
        assert params["Parameters"] == redshift_params

    def test_extract_field_value_types(self, cursor):
        """Test field value extraction for different types."""
        # Test null
        assert cursor._extract_field_value({"isNull": True}) is None

        # Test string
        assert cursor._extract_field_value({"stringValue": "test"}) == "test"

        # Test long
        assert cursor._extract_field_value({"longValue": 123}) == 123

        # Test double
        assert cursor._extract_field_value({"doubleValue": 45.67}) == 45.67

        # Test boolean
        assert cursor._extract_field_value({"booleanValue": True}) is True

    def test_convert_record_to_dict(self, cursor):
        """Test record to dictionary conversion."""
        record = [{"stringValue": "test_value"}, {"longValue": 123}, {"isNull": True}]
        column_metadata = [{"name": "col1"}, {"name": "col2"}, {"name": "col3"}]

        result = cursor._convert_record_to_dict(record, column_metadata)

        assert result == {"col1": "test_value", "col2": 123, "col3": None}

    def test_fetchone_with_results(self, cursor):
        """Test fetchone with results."""
        cursor._results = [{"id": 1}, {"id": 2}]
        result = cursor.fetchone()
        assert result == {"id": 1}

    def test_fetchone_no_results(self, cursor):
        """Test fetchone without results."""
        cursor._results = []
        result = cursor.fetchone()
        assert result is None

    def test_fetchall(self, cursor):
        """Test fetchall."""
        test_results = [{"id": 1}, {"id": 2}]
        cursor._results = test_results
        result = cursor.fetchall()
        assert result == test_results

    def test_rowcount(self, cursor):
        """Test rowcount."""
        cursor._results = [{"id": 1}, {"id": 2}, {"id": 3}]
        assert cursor.rowcount() == 3


class TestDatabaseService:
    """Test suite for DatabaseService."""

    @pytest.fixture
    def mock_settings(self):
        """Create mock database settings."""
        mock = Mock(spec=DatabaseSettings)
        mock.cluster_identifier = "test-cluster"
        mock.redshift_database = "test-db"
        mock.redshift_user = "test-user"
        mock.query_group = "test-group"
        mock.query_timeout = 300
        return mock

    def test_init_success(self, mock_settings):
        """Test successful initialization."""
        with patch.dict(
            "os.environ",
            {
                "DATABASE_CLUSTER_IDENTIFIER": "test-cluster",
                "DATABASE_REDSHIFT_DATABASE": "test-db",
                "DATABASE_REDSHIFT_USER": "test-user",
                "AWS_REGION": "us-east-1",
            },
        ):
            service = DatabaseService(mock_settings)

            assert service.cluster_identifier == "test-cluster"
            assert service.database == "test-db"
            assert service.user == "test-user"
            assert service.region == "us-east-1"

    def test_init_missing_settings_attributes(self):
        """Test initialization with missing settings attributes."""
        incomplete_settings = Mock(spec=DatabaseSettings)
        # Don't set required attributes

        with pytest.raises(AttributeError):
            DatabaseService(incomplete_settings)

    def test_get_connection_context_manager(self, mock_settings):
        """Test connection context manager."""
        with patch.dict(
            "os.environ",
            {
                "DATABASE_CLUSTER_IDENTIFIER": "test-cluster",
                "DATABASE_REDSHIFT_DATABASE": "test-db",
                "DATABASE_REDSHIFT_USER": "test-user",
            },
        ):
            service = DatabaseService(mock_settings)

            with patch(
                "src.agent_event_processor.services.database_service.RedshiftDataAPIConnection"
            ) as mock_conn_class:
                mock_conn = Mock()
                mock_conn_class.return_value = mock_conn

                with service.get_connection() as conn:
                    assert conn == mock_conn

                # Connection should be reused, not closed
                mock_conn.close.assert_not_called()


class TestDimensionManager:
    """Test suite for DimensionManager."""

    @pytest.fixture
    def mock_db_service(self):
        """Create mock database service."""
        return Mock(spec=DatabaseService)

    @pytest.fixture
    def dimension_manager(self, mock_db_service):
        """Create dimension manager with mock service."""
        return DimensionManager(mock_db_service)

    def test_get_date_key(self, dimension_manager):
        """Test date key generation."""
        test_date = datetime(2024, 1, 15, 10, 30, 0)
        date_key = dimension_manager.get_date_key(test_date)
        assert date_key == 20240115

    def test_get_time_key(self, dimension_manager):
        """Test time key generation."""
        test_time = datetime(2024, 1, 15, 10, 30, 45)
        time_key = dimension_manager.get_time_key(test_time)
        assert time_key == 103045


class TestFactManager:
    """Test suite for FactManager."""

    @pytest.fixture
    def mock_db_service(self):
        """Create mock database service."""
        return Mock(spec=DatabaseService)

    @pytest.fixture
    def fact_manager(self, mock_db_service):
        """Create fact manager with mock service."""
        return FactManager(mock_db_service)

    def test_init(self, fact_manager, mock_db_service):
        """Test fact manager initialization."""
        assert fact_manager.db_service == mock_db_service
