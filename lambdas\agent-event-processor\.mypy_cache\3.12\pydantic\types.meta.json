{"data_mtime": 1757356838, "dep_lines": [35, 35, 35, 35, 43, 8, 31, 33, 35, 36, 37, 38, 39, 40, 3, 5, 6, 7, 9, 10, 11, 12, 14, 15, 27, 29, 31, 32, 3072, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 25, 5, 10, 5, 20, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._fields", "pydantic._internal._internal_dataclass", "pydantic._internal._utils", "pydantic._internal._validators", "pydantic._internal._core_metadata", "collections.abc", "pydantic_core.core_schema", "typing_inspection.introspection", "pydantic._internal", "pydantic._migration", "pydantic.annotated_handlers", "pydantic.errors", "pydantic.json_schema", "pydantic.warnings", "__future__", "base64", "dataclasses", "re", "datetime", "decimal", "enum", "pathlib", "types", "typing", "uuid", "annotated_types", "pydantic_core", "typing_extensions", "pydantic", "builtins", "pyexpat.errors", "pyexpat.model", "_collections_abc", "_decimal", "_operator", "_typeshed", "abc", "os", "pydantic._internal._repr", "pydantic_core._pydantic_core"], "hash": "9964ef407fd6b7f09c7107041d88dc516a72a23d54d3858e9eb32c318a1dffae", "id": "pydantic.types", "ignore_all": true, "interface_hash": "b5d2417eca2f89d9cd69ebf063810fdbb416cfdcc0f6092228a39dfb4333ee1f", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\types.py", "plugin_data": null, "size": 104781, "suppressed": [], "version_id": "1.8.0"}