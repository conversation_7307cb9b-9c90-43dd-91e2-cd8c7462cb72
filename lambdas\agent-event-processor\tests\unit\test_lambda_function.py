"""Unit tests for Lambda function handler.

This module tests the main Lambda handler with mocked dependencies
and various event scenarios including all event types and error handling.
"""

import json
from unittest.mock import patch

from aws_lambda_typing.events import SQSEvent

from src.agent_event_processor.lambda_function import lambda_handler


class TestLambdaHandler:
    """Test suite for Lambda handler function."""

    def create_sqs_event(self, xml_content, message_id="test-msg-1"):
        """Helper to create SQS event with XML content."""
        return {
            "Records": [
                {
                    "messageId": message_id,
                    "receiptHandle": f"receipt-{message_id}",
                    "body": xml_content,
                    "attributes": {
                        "ApproximateReceiveCount": "1",
                        "SentTimestamp": "1642248600000",
                    },
                    "messageAttributes": {},
                    "md5OfBody": "test-md5",
                    "eventSource": "aws:sqs",
                    "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:agent-events-queue",
                    "awsRegion": "us-east-1",
                }
            ]
        }

    @patch("src.agent_event_processor.lambda_function._event_processor")
    def test_successful_login_event_processing(
        self, mock_event_processor, mock_lambda_context
    ):
        """Test successful processing of Login event."""
        # Mock the event processor
        mock_event_processor.process_single_event.return_value = True

        # Create Login event XML with proper nested structure
        login_xml = """<LogEvent>
            <timestamp>2024-01-15T10:30:00Z</timestamp>
            <eventType>Login</eventType>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>john.doe</agent>
            <login>
                <mediaLabel>Audio_1</mediaLabel>
                <uri>tel:+2045553006</uri>
                <agentRole>Rural - CT</agentRole>
                <reason>normal</reason>
                <operatorId>OP001</operatorId>
                <workstation>WS-001</workstation>
                <deviceName>Headset</deviceName>
            </login>
        </LogEvent>"""

        sqs_event = self.create_sqs_event(login_xml)
        response = lambda_handler(sqs_event, mock_lambda_context)

        # Verify response
        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["successful_count"] == 1
        assert body["failed_count"] == 0
        assert "batchItemFailures" not in response

        # Verify processor was called
        mock_event_processor.process_single_event.assert_called_once()

    @patch("src.agent_event_processor.lambda_function._event_processor")
    def test_acd_login_event_processing(
        self, mock_event_processor, mock_lambda_context
    ):
        """Test successful processing of ACD Login event."""
        # Mock the event processor
        mock_event_processor.process_single_event.return_value = True

        # Create ACD Login event XML with proper nested structure
        acd_login_xml = """<LogEvent>
            <timestamp>2024-01-15T10:32:00Z</timestamp>
            <eventType>ACDLogin</eventType>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>john.doe</agent>
            <acdLogin>
                <mediaLabel>Audio_1</mediaLabel>
                <agentUri>tel:+2045553006</agentUri>
                <agentRole>Rural - CT</agentRole>
                <workstation>OP6</workstation>
                <ringGroupName>911 Queue</ringGroupName>
                <ringGroupUri>sip:<EMAIL></ringGroupUri>
            </acdLogin>
        </LogEvent>"""

        sqs_event = self.create_sqs_event(acd_login_xml)
        response = lambda_handler(sqs_event, mock_lambda_context)

        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["successful_count"] == 1
        assert body["failed_count"] == 0

        # Verify processor was called
        mock_event_processor.process_single_event.assert_called_once()

    @patch("src.agent_event_processor.lambda_function._event_processor")
    def test_batch_processing_multiple_events(
        self, mock_event_processor, mock_lambda_context
    ):
        """Test batch processing with multiple events."""
        # Mock the event processor
        mock_event_processor.process_single_event.return_value = True

        # Create batch with multiple events with proper nested structure
        events = [
            """<LogEvent>
                <timestamp>2024-01-15T10:30:00Z</timestamp>
                <eventType>Login</eventType>
                <agencyOrElement>Brandon911</agencyOrElement>
                <agent>john.doe</agent>
                <login>
                    <mediaLabel>Audio_1</mediaLabel>
                    <uri>tel:+2045553006</uri>
                    <agentRole>Rural - CT</agentRole>
                    <reason>normal</reason>
                </login>
            </LogEvent>""",
            """<LogEvent>
                <timestamp>2024-01-15T10:31:00Z</timestamp>
                <eventType>AgentAvailable</eventType>
                <agencyOrElement>Brandon911</agencyOrElement>
                <agent>john.doe</agent>
                <agentAvailable>
                    <mediaLabel>Audio_1</mediaLabel>
                    <uri>tel:+2045553006</uri>
                    <agentRole>Rural - CT</agentRole>
                </agentAvailable>
            </LogEvent>""",
            """<LogEvent>
                <timestamp>2024-01-15T10:32:00Z</timestamp>
                <eventType>Logout</eventType>
                <agencyOrElement>Brandon911</agencyOrElement>
                <agent>john.doe</agent>
                <logout>
                    <mediaLabel>Audio_1</mediaLabel>
                    <uri>tel:+2045553006</uri>
                    <workstation>OP6</workstation>
                    <deviceName>Headset</deviceName>
                    <responseCode>200</responseCode>
                </logout>
            </LogEvent>""",
        ]

        sqs_event = {
            "Records": [
                {
                    "messageId": f"msg-{i}",
                    "receiptHandle": f"receipt-{i}",
                    "body": xml_content,
                    "attributes": {},
                    "messageAttributes": {},
                    "md5OfBody": "test-md5",
                    "eventSource": "aws:sqs",
                    "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:agent-events-queue",
                    "awsRegion": "us-east-1",
                }
                for i, xml_content in enumerate(events)
            ]
        }

        response = lambda_handler(sqs_event, mock_lambda_context)

        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["successful_count"] == 3
        assert body["failed_count"] == 0

        # Verify all events were processed
        assert mock_event_processor.process_single_event.call_count == 3

    @patch("src.agent_event_processor.lambda_function._event_processor")
    def test_partial_batch_failure(self, mock_event_processor, mock_lambda_context):
        """Test handling of partial batch failures."""
        # Make second call to processor fail (msg-2 will fail during XML parsing)
        mock_event_processor.process_single_event.side_effect = [
            True,  # First event succeeds
            Exception(
                "Processing failed"
            ),  # Third event fails (second fails during XML parsing)
        ]

        # Create batch with valid and invalid events
        sqs_event = {
            "Records": [
                {
                    "messageId": "msg-1",
                    "receiptHandle": "receipt-1",
                    "body": """<LogEvent>
                     <timestamp>2024-01-15T10:30:00Z</timestamp>
                     <eventType>Login</eventType>
                     <agencyOrElement>Brandon911</agencyOrElement>
                     <agent>john.doe</agent>
                     <login>
                         <mediaLabel>Audio_1</mediaLabel>
                         <uri>tel:+2045553006</uri>
                         <agentRole>Rural - CT</agentRole>
                         <reason>normal</reason>
                     </login>
                 </LogEvent>""",
                },
                {
                    "messageId": "msg-2",
                    "receiptHandle": "receipt-2",
                    "body": "invalid xml",
                },
                {
                    "messageId": "msg-3",
                    "receiptHandle": "receipt-3",
                    "body": """<LogEvent>
                     <timestamp>2024-01-15T10:30:00Z</timestamp>
                     <eventType>Login</eventType>
                     <agencyOrElement>Brandon911</agencyOrElement>
                     <agent>john.doe</agent>
                     <login>
                         <mediaLabel>Audio_1</mediaLabel>
                         <uri>tel:+2045553006</uri>
                         <agentRole>Rural - CT</agentRole>
                         <reason>normal</reason>
                     </login>
                 </LogEvent>""",
                },
            ]
        }

        response = lambda_handler(sqs_event, mock_lambda_context)

        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["successful_count"] == 1
        assert body["failed_count"] == 2

        # Verify batch item failures for SQS
        assert "batchItemFailures" in response
        assert len(response["batchItemFailures"]) == 2
        assert "msg-2" in [
            failure["itemIdentifier"] for failure in response["batchItemFailures"]
        ]
        assert "msg-3" in [
            failure["itemIdentifier"] for failure in response["batchItemFailures"]
        ]

    @patch("src.agent_event_processor.lambda_function._event_processor")
    def test_invalid_xml_handling(self, mock_event_processor, mock_lambda_context):
        """Test handling of invalid XML."""
        # Mock the event processor (won't be called due to XML parsing failure)
        mock_event_processor.process_single_event.return_value = True

        # Create event with invalid XML
        sqs_event = self.create_sqs_event("This is not valid XML")
        response = lambda_handler(sqs_event, mock_lambda_context)

        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["successful_count"] == 0
        assert body["failed_count"] == 1

        # Should have batch item failures
        assert "batchItemFailures" in response
        assert len(response["batchItemFailures"]) == 1

    @patch("src.agent_event_processor.lambda_function._event_processor")
    def test_empty_records(self, mock_event_processor, mock_lambda_context):
        """Test handling of empty SQS records."""
        # Mock the event processor (won't be called for empty records)
        mock_event_processor.process_single_event.return_value = True

        # Empty SQS event
        empty_event: SQSEvent = {"Records": []}
        response = lambda_handler(empty_event, mock_lambda_context)

        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["successful_count"] == 0
        assert body["failed_count"] == 0

    @patch("src.agent_event_processor.lambda_function._event_processor")
    def test_sns_wrapped_message(self, mock_event_processor, mock_lambda_context):
        """Test processing SNS-wrapped SQS message."""
        # Mock the event processor
        mock_event_processor.process_single_event.return_value = True

        # SNS message wrapping XML with proper nested structure
        login_xml = """<LogEvent>
            <timestamp>2024-01-15T10:30:00Z</timestamp>
            <eventType>Login</eventType>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>john.doe</agent>
            <login>
                <mediaLabel>Audio_1</mediaLabel>
                <uri>tel:+2045553006</uri>
                <agentRole>Rural - CT</agentRole>
                <reason>normal</reason>
            </login>
        </LogEvent>"""

        sns_message = {
            "Type": "Notification",
            "MessageId": "sns-msg-id",
            "TopicArn": "arn:aws:sns:us-east-1:123456789012:agent-events",
            "Message": login_xml,
            "Timestamp": "2024-01-15T10:30:00.000Z",
        }

        sqs_event = self.create_sqs_event(json.dumps(sns_message))
        response = lambda_handler(sqs_event, mock_lambda_context)

        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["successful_count"] == 1
        assert body["failed_count"] == 0
